﻿//*********************************************************
// Framework
// Author:  Jasen
// Date  :  2022-12-1
//*********************************************************

using System;
using System.Collections.Generic;
using System.Globalization;
using System.Reflection;

namespace Aurora.Framework
{
    public class MessageHandlerComponent:BaseComponent,IAwake
    {
        public static MessageHandlerComponent Instance { get; set; }
        //当前进程非转发消息，需要处理的消息
        public Dictionary<ushort, List<IMessageHandler>> Handlers = new Dictionary<ushort, List<IMessageHandler>>();
    }

    [ComponentSystem(typeof(MessageHandlerComponent))]
    public static class MessageHandlerComponentSystem
    {
        [MethodAwake]
        public static void OnAwake(MessageHandlerComponent self)
        {
            if (self == null) return;
            MessageHandlerComponent.Instance = self;

            if (EntitySystem.OperatingEnvironment == EntitySystem.OperatingEnvironmentType.Server)
            {
                OnInitServer(self);
            }
            else
            {
                OnInitClient(self);
            }
        }

        private static void OnInitServer(MessageHandlerComponent self)
        {
            List<Type> allTypes = AssemblyManager.Instance.GetAllTypes();
            if (allTypes.Count <= 0)
            {
                throw new FrameworkException("Assembly Init is Empty!");
            }
            foreach (Type type in allTypes)
            {
                var methods = type.GetMethods();
                foreach (var method in methods)
                {
                    var msgMethodAttr = method.GetCustomAttribute<MsgAttribute>();
                    if (msgMethodAttr == null)
                    {
                        continue;
                    }
                    IMessageHandler handler = MsgHandlerBinder.Bind(msgMethodAttr.MsgType, method);
                    if (handler == null)
                    {
                        Log.Error($"消息处理函数绑定错误：创建Handler为空，Type={msgMethodAttr.MsgType.Name},Method={method.Name}");
                        continue;
                    }
                    ushort msgID = MessageIDComponent.Instance.GetMsgID(msgMethodAttr.MsgType);
                    if (!self.Handlers.TryGetValue(msgID, out List<IMessageHandler> listValue))
                    {
                        self.Handlers[msgID] = new List<IMessageHandler>();
                    }
                    self.Handlers[msgID].Add(handler);
                }
            }
        }


        private static void OnInitClient(MessageHandlerComponent self)
        {
            self.Handlers.Clear();
            Dictionary<Type, List<string>> list = AssemblyManager.Instance.GetAllTypeMethods<MsgAttribute>();
            foreach (KeyValuePair<Type, List<string>> value in list)
            {
                for(int i = 0;i < value.Value.Count; i++)
                {
                    var method = value.Key.GetMethod(value.Value[i]);
                    var msgMethodAttr = method.GetCustomAttribute<MsgAttribute>();
                    if (msgMethodAttr == null)
                    {
                        continue;
                    }
                    IMessageHandler handler = MsgHandlerBinder.Bind(msgMethodAttr.MsgType, method);
                    if (handler == null)
                    {
                        Log.Error($"消息处理函数绑定错误：创建Handler为空，Type={msgMethodAttr.MsgType.Name},Method={method.Name}");
                        continue;
                    }
                    ushort msgID = MessageIDComponent.Instance.GetMsgID(msgMethodAttr.MsgType);
                    if (!self.Handlers.TryGetValue(msgID, out List <IMessageHandler> listValue))
                    {
                        self.Handlers[msgID] = new List<IMessageHandler>();
                    }
                    self.Handlers[msgID].Add(handler);
                }
            }
        }
        public static void Handle(this MessageHandlerComponent self,Session session,Packet pkt, bool bMusthandle = true)
        {
            if (self == null || pkt == null) return;
            ushort msgID = pkt.MsgID;
            List<IMessageHandler> handles = null;
            if(!self.Handlers.TryGetValue(msgID,out handles))
            {
                string msgName = msgID.ToString(CultureInfo.InvariantCulture);
                Type msgType = MessageIDComponent.Instance.GetMsgType(msgID);
                if (msgType != null)
                {
                    msgName = msgType.Name;
                }
                if (bMusthandle)
                {
                    Log.Error($"未找到消息处理函数,Type={msgName}");
                }
                return;
            }
            if (handles.Count > 1)
            {
                string msgName = msgID.ToString(CultureInfo.InvariantCulture);
                Type msgType = MessageIDComponent.Instance.GetMsgType(msgID);
                if (msgType != null)
                {
                    msgName = msgType.Name;
                }
                //if (bMusthandle)
                {
                    Log.Error($"消息处理函数过多,Type={msgName}");
                }
                return;
            }
            foreach(var h in handles)
            {
                try
                {
                    h.Handle(session, pkt);
                }
                catch(Exception e)
                {
                    string msgName = msgID.ToString(CultureInfo.InvariantCulture);
                    Type msgType = MessageIDComponent.Instance.GetMsgType(msgID);
                    if (msgType != null)
                    {
                        msgName = msgType.Name;
                    }
                    if (bMusthandle)
                    {
                        Log.Exception($"消息处理异常,Type={msgName},错误:{e.Message},StackTrace:\n{e.StackTrace}\n");
                        throw;
                    }
                }
            }
        }

        // 仅客户端使用,执行缓存的消息
        public static void HandleCache(this MessageHandlerComponent self, Session session, ushort msgID, IMessage msg, bool bMusthandle = true, int RpcID = 0, MessageAddress messAddr = null)
        {
            if (self == null) return;
            List<IMessageHandler> handles = null;
            if (!self.Handlers.TryGetValue(msgID, out handles))
            {
                string msgName = msgID.ToString(CultureInfo.InvariantCulture);
                Type msgType = MessageIDComponent.Instance.GetMsgType(msgID);
                if (msgType != null)
                {
                    msgName = msgType.Name;
                }
                if (bMusthandle)
                {
                    Log.Error($"未找到消息处理函数,Type={msgName}");
                }
                return;
            }
            if (handles.Count > 1)
            {
                string msgName = msgID.ToString(CultureInfo.InvariantCulture);
                Type msgType = MessageIDComponent.Instance.GetMsgType(msgID);
                if (msgType != null)
                {
                    msgName = msgType.Name;
                }
                if (bMusthandle)
                {
                    Log.Error($"消息处理函数过多,Type={msgName}");
                }
                return;
            }
            foreach (var h in handles)
            {
                try
                {
                    Packet pkt = null;
                    //try
                    {
                        if (messAddr == null)
                            pkt = Packet.Create(msg);
                        else
                        {
                            pkt = Packet.Create(msg);
                            {
                                pkt.RpcID = RpcID;
                                pkt.TargetAddress.CopyFrom(messAddr);
                            }
                        }

                        h.Handle(session, pkt);
                    }
                    //finally
                    //{
                    //    if(pkt != null)
                    //        pkt.Dispose();
                    //}
                }
                catch (Exception e)
                {
                    string msgName = msgID.ToString(CultureInfo.InvariantCulture);
                    Type msgType = MessageIDComponent.Instance.GetMsgType(msgID);
                    if (msgType != null)
                    {
                        msgName = msgType.Name;
                    }
                    if (bMusthandle)
                    {
                        Log.Exception($"消息处理异常,Type={msgName},错误:{e.Message},StackTrace:\n{e.StackTrace}\n");
                        throw;
                    }
                }
            }
        }

        public static bool HasHandler(this MessageHandlerComponent self, Packet pkt)
        {
            if (self == null || pkt == null) return false;
            return self.HasHandler(pkt.MsgID);
        }
        public static bool HasHandler(this MessageHandlerComponent self,IMessage message)
        {
            if (self == null || message == null) return false;
            ushort msgID = MessageIDComponent.Instance.GetMsgID(message.GetType());
            return self.HasHandler(msgID);
        }
        public static bool HasHandler(this MessageHandlerComponent self, ushort msgID)
        {
            if (self == null) return false;
            return self.Handlers.ContainsKey(msgID);
        }
    }

}
