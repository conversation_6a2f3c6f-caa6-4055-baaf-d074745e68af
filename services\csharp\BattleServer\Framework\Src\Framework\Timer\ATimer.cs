﻿//*********************************************************
// Framework
// Author:  Jasen
// Date  :  2022-11-8
//*********************************************************

using System;

namespace Aurora.Framework
{
    public class ATimer : Singleton<ATimer>
    {
        public const uint HundredNanoSecondPerMilliSecond = 10000;
        public const uint MilliSecondPerSecond = 1000;

#if CLIENT_FRAMEWORK
        private long _tickCountExtendedBits;
        private long _tickCountExtenedBitsStep;
        private bool _tickCountNegative;
        private long _tickCount64;

        public int FrameTime;
        public float FrameTimeInSeconds;
        private long _lastSystemTime;

        private TimeZoneInfo _localTimeZone;

        private long _lastSynchDateTicks;
        private long _lastSynchSystemTicks;
#endif

        public ATimer()
        {
#if CLIENT_FRAMEWORK
            _tickCountExtendedBits = 0;
            _tickCountExtenedBitsStep = 1 << 32;
            int rawTickCount = Environment.TickCount;
            _tickCountNegative = rawTickCount < 0;
            _tickCount64 = (uint)rawTickCount;

            FrameTime = 0;
            FrameTimeInSeconds = 0;
            _lastSystemTime = SystemTicks;

            _localTimeZone = null;

            _lastSynchDateTicks = 0;
            _lastSynchSystemTicks = 0;
#endif
        }

#if CLIENT_FRAMEWORK
        public void Update()
        {
            int rawTickCount = Environment.TickCount;
            if (_tickCountNegative)
            {
                if (rawTickCount >= 0)
                {
                    _tickCountNegative = false;
                    _tickCountExtendedBits += _tickCountExtenedBitsStep;
                }
            }
            else
            {
                if (rawTickCount < 0)
                {
                    _tickCountNegative = true;
                }
            }
            _tickCount64 = _tickCountExtendedBits | (uint)rawTickCount;

            long now = SystemTicks;
            FrameTime = (int)(now - _lastSystemTime);
            FrameTimeInSeconds = (float)FrameTime / 1000;
            _lastSystemTime = now;
        }

        public void Clear()
        {
            _localTimeZone = null;

            _lastSynchDateTicks = 0;
            _lastSynchSystemTicks = 0;
        }
#endif

#if SERVER_FRAMEWORK
        public string GetTimeZone()
        {
            return TimeZoneInfo.Local.ToSerializedString();
        }
#endif

#if CLIENT_FRAMEWORK
        public void SetTimeZone(string timeZoneString)
        {
            _localTimeZone = TimeZoneInfo.FromSerializedString(timeZoneString);
        }

        public void SynchDateTicks(long dateTicks)
        {
            _lastSynchDateTicks = dateTicks;
            _lastSynchSystemTicks = SystemTicks;
        }
#endif

        /// <summary>
        /// 不受服务器时间影响，用于无需存储的进程内部短时段计时
        /// </summary>
        public long SystemTicks
        {
            get
            {
#if SERVER_FRAMEWORK
                return Environment.TickCount64;
#endif

#if CLIENT_FRAMEWORK
                return _tickCount64;
#endif
            }
        }

        /// <summary>
        /// 随服务器时间变化，用于需要存储或多端同步的长时段计时
        /// </summary>
        public long DateTicks
        {
            get
            {
#if SERVER_FRAMEWORK
                return DateTimeToDateTicks(DateTime.UtcNow);
#endif

#if CLIENT_FRAMEWORK
                if (_lastSynchDateTicks > 0)
                {
                    return _lastSynchDateTicks + (SystemTicks - _lastSynchSystemTicks);
                }
                else
                {
                    //Log.Warning($"DateTicks Has Not Been Synchronized.");
                    return DateTimeToDateTicks(DateTime.UtcNow);
                }
#endif
            }
        }

        public long GetTotalMillisecondsFrom1970()
        {
            return DateTicks - DateTimeToDateTicks(DateTime.UnixEpoch);
        }

        public long GetTotalMillisecondsFrom1970(long DataTick)
        {
            return DataTick - DateTimeToDateTicks(DateTime.UnixEpoch);
        }

        public long GetTotalSecondsFrom1970()
        {
            return (DateTicks - DateTimeToDateTicks(DateTime.UnixEpoch)) / MilliSecondPerSecond;
        }

        public long GetTotalSecondsFrom1970(long DataTick)
        {
            return (DataTick - DateTimeToDateTicks(DateTime.UnixEpoch)) / MilliSecondPerSecond;
        }

        public long TotalSecondsFrom1970ToDateTicks(long totalSeconds)
        {
            return DateTimeToDateTicks(DateTime.UnixEpoch) + totalSeconds * MilliSecondPerSecond;
        }

        public long TotalMillisecondsFrom1970ToDateTicks(long totalMilliseconds)
        {
            return DateTimeToDateTicks(DateTime.UnixEpoch) + totalMilliseconds;
        }

        public DateTime DateTicksToDateTime(long dateTicks)
        {
            return new DateTime(dateTicks * HundredNanoSecondPerMilliSecond, DateTimeKind.Utc);
        }

        public enum DateFormatType
        {
            Default,            // yyyy-MM-dd HH:mm:ss
            DateOnly,           // yyyy-MM-dd
            TimeOnly,           // HH:mm:ss
            ShortDate,          // yy/MM/dd
            ChineseDate,        // yyyy年MM月dd日
            ChineseDateTime,    // yyyy年MM月dd日 HH时mm分
            MonthDay,           // MM-dd
            MonthDayTime,       // MM-dd HH:mm
            WeekDateTime,       // ddd, yyyy-MM-dd HH:mm
            FileNameFriendly    // yyyyMMdd_HHmmss
        }
        // 根据传入时间戳，返回指定格式的本地时间
        public string GetLocalDateStrByTimeStamp(long timeStamp = 0, DateFormatType formatType = DateFormatType.Default)
        {
            DateTime dateTime = timeStamp == 0 ? GetCurLocalDateTime() : GetLocalDateTimeByTimeStamp(timeStamp);

            return formatType switch
            {
                DateFormatType.DateOnly => dateTime.ToString("yyyy-MM-dd"),
                DateFormatType.TimeOnly => dateTime.ToString("HH:mm:ss"),
                DateFormatType.ShortDate => dateTime.ToString("yy/MM/dd"),
                DateFormatType.ChineseDate => dateTime.ToString("yyyy年MM月dd日"),
                DateFormatType.ChineseDateTime => dateTime.ToString("yyyy年MM月dd日 HH时mm分"),
                DateFormatType.MonthDay => dateTime.ToString("MM-dd"),
                DateFormatType.MonthDayTime => dateTime.ToString("MM-dd HH:mm"),
                DateFormatType.WeekDateTime => dateTime.ToString("ddd, yyyy-MM-dd HH:mm"),
                DateFormatType.FileNameFriendly => dateTime.ToString("yyyyMMdd_HHmmss"),
                _ => dateTime.ToString("yyyy-MM-dd HH:mm:ss") // Default
            };
        }

        // 返回当前本地时间dateTime
        public DateTime GetCurLocalDateTime()
        {
            return ATimer.Instance.UtcToLocal(UtcNow);
        }

        // 返回指定时间戳对应的本地时间的dateTime
        public DateTime GetLocalDateTimeByTimeStamp(long timeStamp)
        {
            DateTime timeStampUtcNow = new DateTime(timeStamp * HundredNanoSecondPerMilliSecond);
            TimeZoneInfo myTimeZone = TimeZoneInfo.Local;
            DateTime myLocalTime = TimeZoneInfo.ConvertTimeFromUtc(timeStampUtcNow, myTimeZone);
            return myLocalTime;
        }

        public string GetTimeStampString(long time)
        {
            DateTime dateTime = ATimer.Instance.GetLocalDateTimeByTimeStamp(time);
            string str = "";
            if (dateTime.Year != 1970)
            {
                str = $"{dateTime.Year}-{dateTime.Month}-{dateTime.Day} {dateTime.Hour}:{dateTime.Minute}:{dateTime.Second}";
            }

            return str;
        }


        public long DateTimeToDateTicks(DateTime dateTime)
        {
            if (dateTime.Kind == DateTimeKind.Utc)
            {
                return dateTime.Ticks / HundredNanoSecondPerMilliSecond;
            }
            else
            {
                Log.Error($"Can Only Convert Utc DateTime To DateTicks.");
                return 0;
            }
        }

        public DateTime LocalToUtc(DateTime dateTime)
        {
#if SERVER_FRAMEWORK
            return dateTime.ToUniversalTime();
#endif

#if CLIENT_FRAMEWORK
            if (_localTimeZone != null)
            {
                return TimeZoneInfo.ConvertTimeToUtc(dateTime, _localTimeZone);
            }
            else
            {
                //Log.Warning($"LocalTimeZone Has Not Been Synchronized.");
                return dateTime.ToUniversalTime();
            }
#endif
        }

        public DateTime UtcToLocal(DateTime dateTime)
        {
#if SERVER_FRAMEWORK
            return dateTime.ToLocalTime();
#endif

#if CLIENT_FRAMEWORK
            if (_localTimeZone != null)
            {
                return TimeZoneInfo.ConvertTimeFromUtc(dateTime, _localTimeZone);
            }
            else
            {
                //Log.Warning($"LocalTimeZone Has Not Been Synchronized.");
                return dateTime.ToLocalTime();
            }
#endif
        }

        public TimeSpan GetUtcOffset()
        {
#if SERVER_FRAMEWORK
            return TimeZoneInfo.Local.GetUtcOffset(DateTime.UtcNow);
#endif

#if CLIENT_FRAMEWORK
            if (_localTimeZone != null)
            {
                return _localTimeZone.GetUtcOffset(UtcNow);
            }
            else
            {
                //Log.Warning($"LocalTimeZone Has Not Been Synchronized.");
                return TimeZoneInfo.Local.GetUtcOffset(DateTime.UtcNow);
            }
#endif
        }

        public DateTime UtcNow
        {
            get
            {
#if SERVER_FRAMEWORK
                return DateTime.UtcNow;
#endif

#if CLIENT_FRAMEWORK
                return DateTicksToDateTime(DateTicks);
#endif
            }
        }

        public DateTime Now
        {
            get
            {
#if SERVER_FRAMEWORK
                return DateTime.Now;
#endif

#if CLIENT_FRAMEWORK
                return UtcToLocal(UtcNow);
#endif
            }
        }
    }
}
