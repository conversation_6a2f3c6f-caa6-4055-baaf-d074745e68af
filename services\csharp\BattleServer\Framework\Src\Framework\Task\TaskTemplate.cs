﻿//*********************************************************
// Framework
// Author:  Jasen
// Date  :  2022-11-3
//*********************************************************


using System;

namespace Aurora.Framework
{
    [System.Runtime.CompilerServices.AsyncMethodBuilder(typeof(ATaskAsyncMethodBuilder<>))]
    public class ATask<TResult>
    {
        private ATaskAwaiter<TResult> m_Awaiter;
        public ATask()
        {
            m_Awaiter = ReferencePool.Acquire<ATaskAwaiter<TResult>>();
        }
        public ATaskAwaiter<TResult> GetAwaiter()
        {
            return m_Awaiter;
        }
        //设置异常
        public void SetException(Exception e)
        {
            m_Awaiter.SetException(e);
        }
        //设置异步结果，同时触发Awaiter后续
        public void SetResult(TResult result)
        {
            m_Awaiter.SetResult(result);
        }
        private async ATaskNothing InnerCoroutine()
        {
            await this;
        }
        public void Coroutine()
        {
            InnerCoroutine().Coroutine();
        }
    }
}
