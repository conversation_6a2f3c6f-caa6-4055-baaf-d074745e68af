﻿
using Aurora.Framework;
using Game.Core;
using System.Net.NetworkInformation;

namespace BattleServer.Game
{
    public class StatePreparation : State
    {
        private float _waitTime;

        public StatePreparation(StateComponent stateComponent) : base(stateComponent)
        {
        }

        public override void OnInit()
        {
            base.OnInit();
        }

        public override void OnEnter()
        {
            Log.Debug("[StatePreparation] OnEnter");

            TablePlayMode playMode = TablePlayMode.GetData(1);
            _waitTime = playMode.PreDuration * 1000 + 5;
            //_waitTime = 5 * 1000;

            // 机器人自动准备
            var players = _stateComponent.GetScene().GetPlayers();
            foreach (var player in players.Values)
            {
                if (player.IsRobot)
                {
                    player.OnBattleReady();
                }
            }
        }

        public override void OnUpdate(float deltaTime)
        {
            if (IsAllPlayerReady())
            {
                _stateComponent.ChangeState(StateType.Fight);
                return;
            }
            else
            {
                _waitTime -= deltaTime;
                if (_waitTime <= 0)
                {
                    Log.Debug("[StatePreparation] Wait time out, changing to Fight state.");
                    // 如果等待时间超时，强制所有玩家进入战斗状态
                    var players = _stateComponent.GetScene().GetPlayers();
                    foreach (var player in players.Values)
                    {
                        if (!player.IsReadyToBattle())
                        {
                            // 如果玩家没有准备，强制设置为准备状态
                            player.OnBattleReady();
                        }
                    }
                    _stateComponent.ChangeState(StateType.Fight);
                    return;
                }
                else
                {
                    //Log.Debug($"[StatePreparation] Waiting for players to prepare. Remaining time: {_waitTime / 1000} seconds.");
                }
            }
            //mTimeOut -= deltaTime;
            //if (mTimeOut <= 0)
            //{
            //    _stateComponent.ChangeState(StateType.Fight);
            //}
            //else
            //{
            //    if (IsAllPlayerReady())
            //    {
            //        _stateComponent.ChangeState(StateType.Fight);
            //    }
            //}
        }

        private bool IsAllPlayerReady()
        {
            var players = _stateComponent.GetScene().GetPlayers();
            foreach (var player in players.Values)
            {
                if (!player.IsReadyToBattle())
                {
                    return false;
                }
            }
            return true;
        }
    }
}
