﻿//*********************************************************
// Framework
// Author:  Jasen
// Date  :  2022-11-8
//*********************************************************

using System;

namespace Aurora.Framework
{
    //时间触发处理器基类
    public interface ITimerHandler
    {
        void Excute(bool result = true);
    }

    //正常时间触发处理器
    public sealed class TimerHandler<T> : ITimerHandler where T : BaseComponent
    {
        private T m_Object;
        private Action<T> m_Callback;

        public TimerHandler(T obj, Action<T> callback)
        {
            m_Object = obj;
            m_Callback = callback;
        }

        public void Excute(bool result = true)
        {
            if (m_Callback != null)
            {
                m_Callback.Invoke(m_Object);
            }
        }
    }

    //异步时间触发处理器
    public sealed class AsyncTimerHandler : ITimerHandler
    {
        public AsyncTimerHandler(ATask<bool> timerTask)
        {
            TimerTask = timerTask;
        }
        ATask<bool> TimerTask;
        public void Excute(bool result = true)
        {
            TimerTask.SetResult(result);
        }
    }

    public sealed class ObjectTimerHandler<T> : ITimerHandler where T : class
    {
        private T m_Object;
        private Action<T> m_Callback;

        public ObjectTimerHandler(T obj, Action<T> callback)
        {
            m_Object = obj;
            m_Callback = callback;
        }

        public void Excute(bool result = true)
        {
            if (m_Callback != null)
            {
                m_Callback.Invoke(m_Object);
            }
        }
    }
}
