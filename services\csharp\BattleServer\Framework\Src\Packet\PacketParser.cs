﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Text;

namespace Aurora.Framework
{
    public enum ParserState
    {
        PacketSize,
        PacketBody
    }

    public class PacketParser
    {
        private readonly CircularBuffer m_Buffer;
        private int m_PacketSize;
        private ParserState m_State;
        private readonly byte[] m_Cache = new byte[8];
        public Packet Pkt;

        public PacketParser(CircularBuffer buffer)
        {
            this.m_Buffer = buffer;
        }

        public bool Parse()
        {
            while (true)
            {
                switch (m_State)
                {
                    case ParserState.PacketSize:
                        {
                            if (m_Buffer.Length < 2)
                            {
                                return false;
                            }

                            m_Buffer.Read(m_Cache, 0, 2);

                            m_PacketSize = BitConverter.ToUInt16(m_Cache, 0);
                            if (m_PacketSize > ushort.MaxValue)
                            {
                                throw new FrameworkException($"recv packet size error, 可能是外网探测端口: {m_PacketSize}");
                            }

                            m_State = ParserState.PacketBody;
                            break;
                        }
                    case ParserState.PacketBody:
                        {
                            //减去包头2字节长度
                            if (m_Buffer.Length < m_PacketSize-2)
                            {
                                return false;
                            }

                            Packet pkt = Packet.Create();
                            pkt.Write(m_Cache, 0, 2);
                            m_Buffer.Read(pkt, m_PacketSize-2);
                            pkt.ParseHead();
                            pkt.ParseMessage();
                            this.Pkt = pkt;

                            m_State = ParserState.PacketSize;
                            return true;
                        }
                    default:
                        throw new ArgumentOutOfRangeException();
                }
            }
        }
    }
}
