// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.16.0
// source: PublicEnum.proto

package public

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

//=====================平台能力开始================================
type LoginByType int32

const (
	LoginByType_LoginByType_Other            LoginByType = 0
	LoginByType_LoginByType_QQGameCenter     LoginByType = 1
	LoginByType_LoginByType_WeChatGameCenter LoginByType = 2
)

// Enum value maps for LoginByType.
var (
	LoginByType_name = map[int32]string{
		0: "LoginByType_Other",
		1: "LoginByType_QQGameCenter",
		2: "LoginByType_WeChatGameCenter",
	}
	LoginByType_value = map[string]int32{
		"LoginByType_Other":            0,
		"LoginByType_QQGameCenter":     1,
		"LoginByType_WeChatGameCenter": 2,
	}
)

func (x LoginByType) Enum() *LoginByType {
	p := new(LoginByType)
	*p = x
	return p
}

func (x LoginByType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoginByType) Descriptor() protoreflect.EnumDescriptor {
	return file_PublicEnum_proto_enumTypes[0].Descriptor()
}

func (LoginByType) Type() protoreflect.EnumType {
	return &file_PublicEnum_proto_enumTypes[0]
}

func (x LoginByType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoginByType.Descriptor instead.
func (LoginByType) EnumDescriptor() ([]byte, []int) {
	return file_PublicEnum_proto_rawDescGZIP(), []int{0}
}

//货币类型
type MoneyType int32

const (
	MoneyType_MoneyType_None    MoneyType = 0 //未知的类型
	MoneyType_MoneyType_Gold    MoneyType = 1 //金币
	MoneyType_MoneyType_Diamond MoneyType = 2 //钻石
	MoneyType_MoneyType_Power   MoneyType = 3 //体力
	MoneyType_MoneyType_Exp     MoneyType = 4 //经验
)

// Enum value maps for MoneyType.
var (
	MoneyType_name = map[int32]string{
		0: "MoneyType_None",
		1: "MoneyType_Gold",
		2: "MoneyType_Diamond",
		3: "MoneyType_Power",
		4: "MoneyType_Exp",
	}
	MoneyType_value = map[string]int32{
		"MoneyType_None":    0,
		"MoneyType_Gold":    1,
		"MoneyType_Diamond": 2,
		"MoneyType_Power":   3,
		"MoneyType_Exp":     4,
	}
)

func (x MoneyType) Enum() *MoneyType {
	p := new(MoneyType)
	*p = x
	return p
}

func (x MoneyType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MoneyType) Descriptor() protoreflect.EnumDescriptor {
	return file_PublicEnum_proto_enumTypes[1].Descriptor()
}

func (MoneyType) Type() protoreflect.EnumType {
	return &file_PublicEnum_proto_enumTypes[1]
}

func (x MoneyType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MoneyType.Descriptor instead.
func (MoneyType) EnumDescriptor() ([]byte, []int) {
	return file_PublicEnum_proto_rawDescGZIP(), []int{1}
}

//掉落类型
type AwardItemType int32

const (
	AwardItemType_AwardItemType_Money AwardItemType = 0 //货币
	AwardItemType_AwardItemType_Item  AwardItemType = 1 //普通物品
	AwardItemType_AwardItemType_Equip AwardItemType = 2 //装备
	AwardItemType_AwardItemType_Skill AwardItemType = 3 //技能
	AwardItemType_AwardItemType_Gem   AwardItemType = 4 //宝石
)

// Enum value maps for AwardItemType.
var (
	AwardItemType_name = map[int32]string{
		0: "AwardItemType_Money",
		1: "AwardItemType_Item",
		2: "AwardItemType_Equip",
		3: "AwardItemType_Skill",
		4: "AwardItemType_Gem",
	}
	AwardItemType_value = map[string]int32{
		"AwardItemType_Money": 0,
		"AwardItemType_Item":  1,
		"AwardItemType_Equip": 2,
		"AwardItemType_Skill": 3,
		"AwardItemType_Gem":   4,
	}
)

func (x AwardItemType) Enum() *AwardItemType {
	p := new(AwardItemType)
	*p = x
	return p
}

func (x AwardItemType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AwardItemType) Descriptor() protoreflect.EnumDescriptor {
	return file_PublicEnum_proto_enumTypes[2].Descriptor()
}

func (AwardItemType) Type() protoreflect.EnumType {
	return &file_PublicEnum_proto_enumTypes[2]
}

func (x AwardItemType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AwardItemType.Descriptor instead.
func (AwardItemType) EnumDescriptor() ([]byte, []int) {
	return file_PublicEnum_proto_rawDescGZIP(), []int{2}
}

//功能解锁状态
type FuncLockType int32

const (
	FuncLockType_FuncLockType_Lock   FuncLockType = 0 //未解锁
	FuncLockType_FuncLockType_UnLock FuncLockType = 1 //解锁
	FuncLockType_FuncLockType_Close  FuncLockType = 2 //关闭
)

// Enum value maps for FuncLockType.
var (
	FuncLockType_name = map[int32]string{
		0: "FuncLockType_Lock",
		1: "FuncLockType_UnLock",
		2: "FuncLockType_Close",
	}
	FuncLockType_value = map[string]int32{
		"FuncLockType_Lock":   0,
		"FuncLockType_UnLock": 1,
		"FuncLockType_Close":  2,
	}
)

func (x FuncLockType) Enum() *FuncLockType {
	p := new(FuncLockType)
	*p = x
	return p
}

func (x FuncLockType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FuncLockType) Descriptor() protoreflect.EnumDescriptor {
	return file_PublicEnum_proto_enumTypes[3].Descriptor()
}

func (FuncLockType) Type() protoreflect.EnumType {
	return &file_PublicEnum_proto_enumTypes[3]
}

func (x FuncLockType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FuncLockType.Descriptor instead.
func (FuncLockType) EnumDescriptor() ([]byte, []int) {
	return file_PublicEnum_proto_rawDescGZIP(), []int{3}
}

//场景掉落类型
type SceneDropType int32

const (
	SceneDropType_SceneDropType_Monster SceneDropType = 0 //小怪
	SceneDropType_SceneDropType_Horse   SceneDropType = 1 //彩虹马
)

// Enum value maps for SceneDropType.
var (
	SceneDropType_name = map[int32]string{
		0: "SceneDropType_Monster",
		1: "SceneDropType_Horse",
	}
	SceneDropType_value = map[string]int32{
		"SceneDropType_Monster": 0,
		"SceneDropType_Horse":   1,
	}
)

func (x SceneDropType) Enum() *SceneDropType {
	p := new(SceneDropType)
	*p = x
	return p
}

func (x SceneDropType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SceneDropType) Descriptor() protoreflect.EnumDescriptor {
	return file_PublicEnum_proto_enumTypes[4].Descriptor()
}

func (SceneDropType) Type() protoreflect.EnumType {
	return &file_PublicEnum_proto_enumTypes[4]
}

func (x SceneDropType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SceneDropType.Descriptor instead.
func (SceneDropType) EnumDescriptor() ([]byte, []int) {
	return file_PublicEnum_proto_rawDescGZIP(), []int{4}
}

//红点枚举
type RedDotType int32

const (
	RedDotType_RedDotType_None          RedDotType = 0 //
	RedDotType_RedDotType_Skill         RedDotType = 1 //技能红点
	RedDotType_RedDotType_Mail          RedDotType = 2 //邮件红点
	RedDotType_RedDotType_Friend        RedDotType = 3 //好友红点
	RedDotType_RedDotType_FuncPreview   RedDotType = 4 //功能预告
	RedDotType_RedDotType_HeavenlyDao   RedDotType = 5 //天道修为
	RedDotType_RedDotType_DailyPlusGift RedDotType = 6 //每日特惠礼包
	RedDotType_RedDotType_Bag           RedDotType = 7 //背包红点
	RedDotType_RedDotType_MAX           RedDotType = 8 //放在最后
)

// Enum value maps for RedDotType.
var (
	RedDotType_name = map[int32]string{
		0: "RedDotType_None",
		1: "RedDotType_Skill",
		2: "RedDotType_Mail",
		3: "RedDotType_Friend",
		4: "RedDotType_FuncPreview",
		5: "RedDotType_HeavenlyDao",
		6: "RedDotType_DailyPlusGift",
		7: "RedDotType_Bag",
		8: "RedDotType_MAX",
	}
	RedDotType_value = map[string]int32{
		"RedDotType_None":          0,
		"RedDotType_Skill":         1,
		"RedDotType_Mail":          2,
		"RedDotType_Friend":        3,
		"RedDotType_FuncPreview":   4,
		"RedDotType_HeavenlyDao":   5,
		"RedDotType_DailyPlusGift": 6,
		"RedDotType_Bag":           7,
		"RedDotType_MAX":           8,
	}
)

func (x RedDotType) Enum() *RedDotType {
	p := new(RedDotType)
	*p = x
	return p
}

func (x RedDotType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RedDotType) Descriptor() protoreflect.EnumDescriptor {
	return file_PublicEnum_proto_enumTypes[5].Descriptor()
}

func (RedDotType) Type() protoreflect.EnumType {
	return &file_PublicEnum_proto_enumTypes[5]
}

func (x RedDotType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RedDotType.Descriptor instead.
func (RedDotType) EnumDescriptor() ([]byte, []int) {
	return file_PublicEnum_proto_rawDescGZIP(), []int{5}
}

//性别枚举
type EGenderType int32

const (
	EGenderType_Default EGenderType = 0 //神秘
	EGenderType_Man     EGenderType = 1 //男性
	EGenderType_Woman   EGenderType = 2 //女性
)

// Enum value maps for EGenderType.
var (
	EGenderType_name = map[int32]string{
		0: "Default",
		1: "Man",
		2: "Woman",
	}
	EGenderType_value = map[string]int32{
		"Default": 0,
		"Man":     1,
		"Woman":   2,
	}
)

func (x EGenderType) Enum() *EGenderType {
	p := new(EGenderType)
	*p = x
	return p
}

func (x EGenderType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EGenderType) Descriptor() protoreflect.EnumDescriptor {
	return file_PublicEnum_proto_enumTypes[6].Descriptor()
}

func (EGenderType) Type() protoreflect.EnumType {
	return &file_PublicEnum_proto_enumTypes[6]
}

func (x EGenderType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EGenderType.Descriptor instead.
func (EGenderType) EnumDescriptor() ([]byte, []int) {
	return file_PublicEnum_proto_rawDescGZIP(), []int{6}
}

//属性
type AttributeType int32

const (
	AttributeType_AttributeType_NONE                     AttributeType = 0  //空
	AttributeType_AttributeType_Attack                   AttributeType = 1  //基础攻击力
	AttributeType_AttributeType_Hp                       AttributeType = 2  //血量
	AttributeType_AttributeType_Def                      AttributeType = 3  //基础防御
	AttributeType_AttributeType_MoveSpeed                AttributeType = 4  //移速
	AttributeType_AttributeType_GunAtk                   AttributeType = 5  //枪械攻击力
	AttributeType_AttributeType_PhysicsAtk               AttributeType = 6  //物理攻击力
	AttributeType_AttributeType_IceAtk                   AttributeType = 7  //冰攻击力
	AttributeType_AttributeType_ElectricityAtk           AttributeType = 8  //电攻击力
	AttributeType_AttributeType_FireAtk                  AttributeType = 9  //火攻击力
	AttributeType_AttributeType_EnergyAtk                AttributeType = 10 //能量攻击力
	AttributeType_AttributeType_WindAtk                  AttributeType = 11 //风攻击力
	AttributeType_AttributeType_Penetrate                AttributeType = 12 //穿透值
	AttributeType_AttributeType_HitPre                   AttributeType = 20 //命中
	AttributeType_AttributeType_DodgePre                 AttributeType = 21 //闪避
	AttributeType_AttributeType_AttackPre                AttributeType = 22 //基础攻击百分比
	AttributeType_AttributeType_HpPre                    AttributeType = 23 //血量百分比
	AttributeType_AttributeType_DefPre                   AttributeType = 24 //基础抗百分比
	AttributeType_AttributeType_MoveSpeedPre             AttributeType = 25 //移动速度百分比
	AttributeType_AttributeType_CriticalPre              AttributeType = 26 //暴击率
	AttributeType_AttributeType_CriticalMultiplePre      AttributeType = 27 //暴击倍数
	AttributeType_AttributeType_CriticalResistPre        AttributeType = 28 //暴击抵抗
	AttributeType_AttributeType_BaseDamagePre            AttributeType = 29 //输出伤害增幅
	AttributeType_AttributeType_GunDamagePre             AttributeType = 30 //枪械伤害增幅
	AttributeType_AttributeType_PhysicsDamagePre         AttributeType = 31 //物理伤害增幅
	AttributeType_AttributeType_IceDamagePre             AttributeType = 32 //冰伤害增幅
	AttributeType_AttributeType_ElectricityDamagePre     AttributeType = 33 //电伤害增幅
	AttributeType_AttributeType_FireDamagePre            AttributeType = 34 //火伤害增幅
	AttributeType_AttributeType_EnergyDamagePre          AttributeType = 35 //能量伤害增幅
	AttributeType_AttributeType_WindDamagePre            AttributeType = 36 //风伤害增幅
	AttributeType_AttributeType_PenetratePre             AttributeType = 37 //穿透值百分比
	AttributeType_AttributeType_GunDefPre                AttributeType = 38 //枪械抵抗
	AttributeType_AttributeType_PhysicsDefPre            AttributeType = 39 //物理抵抗
	AttributeType_AttributeType_IceDefPre                AttributeType = 40 //冰抵抗
	AttributeType_AttributeType_ElectricityDefPre        AttributeType = 41 //电抵抗百分比
	AttributeType_AttributeType_FireDefPre               AttributeType = 42 //火抵抗百分比
	AttributeType_AttributeType_EnergyDefPre             AttributeType = 43 //能量抵抗百分比
	AttributeType_AttributeType_WindDefPre               AttributeType = 44 //风抵抗百分比
	AttributeType_AttributeType_GroundDemageAddPre       AttributeType = 45 //地面单位伤害增幅
	AttributeType_AttributeType_MidairDamageAddPre       AttributeType = 46 //空中单位伤害增幅
	AttributeType_AttributeType_NearDamageAddPre         AttributeType = 47 //近战单位伤害增加
	AttributeType_AttributeType_LongDisDamageAddPre      AttributeType = 48 //远程单位伤害增幅
	AttributeType_AttributeType_SmallMonsterDamageAddPre AttributeType = 49 //小怪的伤害加成
	AttributeType_AttributeType_EliteMonsterDamageAddPre AttributeType = 50 //精英伤害加成
	AttributeType_AttributeType_BossMonsterDamageAddPre  AttributeType = 51 //Boss伤害加成
	AttributeType_AttributeType_DecreaseGunCD            AttributeType = 52 //减少枪械CD
	AttributeType_AttributeType_DecreaseSkillCD          AttributeType = 53 //减少技能CD
	AttributeType_AttributeType_GoldBonusPre             AttributeType = 80 //金币加成
	AttributeType_AttributeType_ExpBonusPre              AttributeType = 81 //经验加成
	AttributeType_AttributeType_WarCoinPre               AttributeType = 82 //战币加成
	AttributeType_AttributeType_Magazine                 AttributeType = 83 //弹夹数量
	AttributeType_AttributeType_MagazinePre              AttributeType = 84 //弹夹数量百分比
)

// Enum value maps for AttributeType.
var (
	AttributeType_name = map[int32]string{
		0:  "AttributeType_NONE",
		1:  "AttributeType_Attack",
		2:  "AttributeType_Hp",
		3:  "AttributeType_Def",
		4:  "AttributeType_MoveSpeed",
		5:  "AttributeType_GunAtk",
		6:  "AttributeType_PhysicsAtk",
		7:  "AttributeType_IceAtk",
		8:  "AttributeType_ElectricityAtk",
		9:  "AttributeType_FireAtk",
		10: "AttributeType_EnergyAtk",
		11: "AttributeType_WindAtk",
		12: "AttributeType_Penetrate",
		20: "AttributeType_HitPre",
		21: "AttributeType_DodgePre",
		22: "AttributeType_AttackPre",
		23: "AttributeType_HpPre",
		24: "AttributeType_DefPre",
		25: "AttributeType_MoveSpeedPre",
		26: "AttributeType_CriticalPre",
		27: "AttributeType_CriticalMultiplePre",
		28: "AttributeType_CriticalResistPre",
		29: "AttributeType_BaseDamagePre",
		30: "AttributeType_GunDamagePre",
		31: "AttributeType_PhysicsDamagePre",
		32: "AttributeType_IceDamagePre",
		33: "AttributeType_ElectricityDamagePre",
		34: "AttributeType_FireDamagePre",
		35: "AttributeType_EnergyDamagePre",
		36: "AttributeType_WindDamagePre",
		37: "AttributeType_PenetratePre",
		38: "AttributeType_GunDefPre",
		39: "AttributeType_PhysicsDefPre",
		40: "AttributeType_IceDefPre",
		41: "AttributeType_ElectricityDefPre",
		42: "AttributeType_FireDefPre",
		43: "AttributeType_EnergyDefPre",
		44: "AttributeType_WindDefPre",
		45: "AttributeType_GroundDemageAddPre",
		46: "AttributeType_MidairDamageAddPre",
		47: "AttributeType_NearDamageAddPre",
		48: "AttributeType_LongDisDamageAddPre",
		49: "AttributeType_SmallMonsterDamageAddPre",
		50: "AttributeType_EliteMonsterDamageAddPre",
		51: "AttributeType_BossMonsterDamageAddPre",
		52: "AttributeType_DecreaseGunCD",
		53: "AttributeType_DecreaseSkillCD",
		80: "AttributeType_GoldBonusPre",
		81: "AttributeType_ExpBonusPre",
		82: "AttributeType_WarCoinPre",
		83: "AttributeType_Magazine",
		84: "AttributeType_MagazinePre",
	}
	AttributeType_value = map[string]int32{
		"AttributeType_NONE":                     0,
		"AttributeType_Attack":                   1,
		"AttributeType_Hp":                       2,
		"AttributeType_Def":                      3,
		"AttributeType_MoveSpeed":                4,
		"AttributeType_GunAtk":                   5,
		"AttributeType_PhysicsAtk":               6,
		"AttributeType_IceAtk":                   7,
		"AttributeType_ElectricityAtk":           8,
		"AttributeType_FireAtk":                  9,
		"AttributeType_EnergyAtk":                10,
		"AttributeType_WindAtk":                  11,
		"AttributeType_Penetrate":                12,
		"AttributeType_HitPre":                   20,
		"AttributeType_DodgePre":                 21,
		"AttributeType_AttackPre":                22,
		"AttributeType_HpPre":                    23,
		"AttributeType_DefPre":                   24,
		"AttributeType_MoveSpeedPre":             25,
		"AttributeType_CriticalPre":              26,
		"AttributeType_CriticalMultiplePre":      27,
		"AttributeType_CriticalResistPre":        28,
		"AttributeType_BaseDamagePre":            29,
		"AttributeType_GunDamagePre":             30,
		"AttributeType_PhysicsDamagePre":         31,
		"AttributeType_IceDamagePre":             32,
		"AttributeType_ElectricityDamagePre":     33,
		"AttributeType_FireDamagePre":            34,
		"AttributeType_EnergyDamagePre":          35,
		"AttributeType_WindDamagePre":            36,
		"AttributeType_PenetratePre":             37,
		"AttributeType_GunDefPre":                38,
		"AttributeType_PhysicsDefPre":            39,
		"AttributeType_IceDefPre":                40,
		"AttributeType_ElectricityDefPre":        41,
		"AttributeType_FireDefPre":               42,
		"AttributeType_EnergyDefPre":             43,
		"AttributeType_WindDefPre":               44,
		"AttributeType_GroundDemageAddPre":       45,
		"AttributeType_MidairDamageAddPre":       46,
		"AttributeType_NearDamageAddPre":         47,
		"AttributeType_LongDisDamageAddPre":      48,
		"AttributeType_SmallMonsterDamageAddPre": 49,
		"AttributeType_EliteMonsterDamageAddPre": 50,
		"AttributeType_BossMonsterDamageAddPre":  51,
		"AttributeType_DecreaseGunCD":            52,
		"AttributeType_DecreaseSkillCD":          53,
		"AttributeType_GoldBonusPre":             80,
		"AttributeType_ExpBonusPre":              81,
		"AttributeType_WarCoinPre":               82,
		"AttributeType_Magazine":                 83,
		"AttributeType_MagazinePre":              84,
	}
)

func (x AttributeType) Enum() *AttributeType {
	p := new(AttributeType)
	*p = x
	return p
}

func (x AttributeType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AttributeType) Descriptor() protoreflect.EnumDescriptor {
	return file_PublicEnum_proto_enumTypes[7].Descriptor()
}

func (AttributeType) Type() protoreflect.EnumType {
	return &file_PublicEnum_proto_enumTypes[7]
}

func (x AttributeType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AttributeType.Descriptor instead.
func (AttributeType) EnumDescriptor() ([]byte, []int) {
	return file_PublicEnum_proto_rawDescGZIP(), []int{7}
}

//怪物的类型
type MonsterType int32

const (
	MonsterType_MonsterType_None     MonsterType = 0 //无
	MonsterType_MonsterType_Small    MonsterType = 1 //小怪
	MonsterType_MonsterType_Advanced MonsterType = 2 //精英
	MonsterType_MonsterType_Boss     MonsterType = 3 //Boss
)

// Enum value maps for MonsterType.
var (
	MonsterType_name = map[int32]string{
		0: "MonsterType_None",
		1: "MonsterType_Small",
		2: "MonsterType_Advanced",
		3: "MonsterType_Boss",
	}
	MonsterType_value = map[string]int32{
		"MonsterType_None":     0,
		"MonsterType_Small":    1,
		"MonsterType_Advanced": 2,
		"MonsterType_Boss":     3,
	}
)

func (x MonsterType) Enum() *MonsterType {
	p := new(MonsterType)
	*p = x
	return p
}

func (x MonsterType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MonsterType) Descriptor() protoreflect.EnumDescriptor {
	return file_PublicEnum_proto_enumTypes[8].Descriptor()
}

func (MonsterType) Type() protoreflect.EnumType {
	return &file_PublicEnum_proto_enumTypes[8]
}

func (x MonsterType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MonsterType.Descriptor instead.
func (MonsterType) EnumDescriptor() ([]byte, []int) {
	return file_PublicEnum_proto_rawDescGZIP(), []int{8}
}

//战斗的类型或是副本类型
type BattleType int32

const (
	BattleType_BattleType_Common       BattleType = 0   //主线的关卡
	BattleType_BattleType_StandingPile BattleType = 100 //主界面站桩的副本信息
)

// Enum value maps for BattleType.
var (
	BattleType_name = map[int32]string{
		0:   "BattleType_Common",
		100: "BattleType_StandingPile",
	}
	BattleType_value = map[string]int32{
		"BattleType_Common":       0,
		"BattleType_StandingPile": 100,
	}
)

func (x BattleType) Enum() *BattleType {
	p := new(BattleType)
	*p = x
	return p
}

func (x BattleType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BattleType) Descriptor() protoreflect.EnumDescriptor {
	return file_PublicEnum_proto_enumTypes[9].Descriptor()
}

func (BattleType) Type() protoreflect.EnumType {
	return &file_PublicEnum_proto_enumTypes[9]
}

func (x BattleType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BattleType.Descriptor instead.
func (BattleType) EnumDescriptor() ([]byte, []int) {
	return file_PublicEnum_proto_rawDescGZIP(), []int{9}
}

// 战斗状态枚举
type BattleState int32

const (
	BattleState_STATE_NONE               BattleState = 0
	BattleState_STATE_ROUND_START        BattleState = 1 // 回合开始（匹配对手）
	BattleState_STATE_PREPARATION        BattleState = 2 // 准备阶段（包含buff选择、英雄生成、自由操作）
	BattleState_STATE_BATTLE_STARTING    BattleState = 3 // 战斗开始（所有玩家准备完毕或超时）
	BattleState_STATE_BATTLE_IN_PROGRESS BattleState = 4 // 战斗进行中（两场战斗都在进行）
	BattleState_STATE_ROUND_SETTLEMENT   BattleState = 5 // 回合结算（两场战斗都结束）
	BattleState_STATE_ELIMINATION_CHECK  BattleState = 6 // 淘汰检查
	BattleState_STATE_GAME_OVER          BattleState = 7 // 游戏结束
)

// Enum value maps for BattleState.
var (
	BattleState_name = map[int32]string{
		0: "STATE_NONE",
		1: "STATE_ROUND_START",
		2: "STATE_PREPARATION",
		3: "STATE_BATTLE_STARTING",
		4: "STATE_BATTLE_IN_PROGRESS",
		5: "STATE_ROUND_SETTLEMENT",
		6: "STATE_ELIMINATION_CHECK",
		7: "STATE_GAME_OVER",
	}
	BattleState_value = map[string]int32{
		"STATE_NONE":               0,
		"STATE_ROUND_START":        1,
		"STATE_PREPARATION":        2,
		"STATE_BATTLE_STARTING":    3,
		"STATE_BATTLE_IN_PROGRESS": 4,
		"STATE_ROUND_SETTLEMENT":   5,
		"STATE_ELIMINATION_CHECK":  6,
		"STATE_GAME_OVER":          7,
	}
)

func (x BattleState) Enum() *BattleState {
	p := new(BattleState)
	*p = x
	return p
}

func (x BattleState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BattleState) Descriptor() protoreflect.EnumDescriptor {
	return file_PublicEnum_proto_enumTypes[10].Descriptor()
}

func (BattleState) Type() protoreflect.EnumType {
	return &file_PublicEnum_proto_enumTypes[10]
}

func (x BattleState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BattleState.Descriptor instead.
func (BattleState) EnumDescriptor() ([]byte, []int) {
	return file_PublicEnum_proto_rawDescGZIP(), []int{10}
}

type InstanceState int32

const (
	InstanceState_INSTANCE_WAITING            InstanceState = 0 // 等待战斗开始
	InstanceState_INSTANCE_BATTLE_IN_PROGRESS InstanceState = 1 // 战斗进行中
	InstanceState_INSTANCE_BATTLE_FINISHED    InstanceState = 2 // 战斗结束，等待另一场
	InstanceState_INSTANCE_SETTLEMENT         InstanceState = 3 // 结算中
)

// Enum value maps for InstanceState.
var (
	InstanceState_name = map[int32]string{
		0: "INSTANCE_WAITING",
		1: "INSTANCE_BATTLE_IN_PROGRESS",
		2: "INSTANCE_BATTLE_FINISHED",
		3: "INSTANCE_SETTLEMENT",
	}
	InstanceState_value = map[string]int32{
		"INSTANCE_WAITING":            0,
		"INSTANCE_BATTLE_IN_PROGRESS": 1,
		"INSTANCE_BATTLE_FINISHED":    2,
		"INSTANCE_SETTLEMENT":         3,
	}
)

func (x InstanceState) Enum() *InstanceState {
	p := new(InstanceState)
	*p = x
	return p
}

func (x InstanceState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (InstanceState) Descriptor() protoreflect.EnumDescriptor {
	return file_PublicEnum_proto_enumTypes[11].Descriptor()
}

func (InstanceState) Type() protoreflect.EnumType {
	return &file_PublicEnum_proto_enumTypes[11]
}

func (x InstanceState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use InstanceState.Descriptor instead.
func (InstanceState) EnumDescriptor() ([]byte, []int) {
	return file_PublicEnum_proto_rawDescGZIP(), []int{11}
}

type PlayerState int32

const (
	PlayerState_PLAYER_WAITING          PlayerState = 0 // 等待中
	PlayerState_PLAYER_SELECTING_BUFF   PlayerState = 1 // 选择Buff中
	PlayerState_PLAYER_RECEIVING_HEROES PlayerState = 2 // 接收英雄中
	PlayerState_PLAYER_FREE_OPERATION   PlayerState = 3 // 自由操作中
	PlayerState_PLAYER_READY            PlayerState = 4 // 已准备
	PlayerState_PLAYER_IN_BATTLE        PlayerState = 5 // 战斗中
	PlayerState_PLAYER_ELIMINATED       PlayerState = 6 // 已淘汰
)

// Enum value maps for PlayerState.
var (
	PlayerState_name = map[int32]string{
		0: "PLAYER_WAITING",
		1: "PLAYER_SELECTING_BUFF",
		2: "PLAYER_RECEIVING_HEROES",
		3: "PLAYER_FREE_OPERATION",
		4: "PLAYER_READY",
		5: "PLAYER_IN_BATTLE",
		6: "PLAYER_ELIMINATED",
	}
	PlayerState_value = map[string]int32{
		"PLAYER_WAITING":          0,
		"PLAYER_SELECTING_BUFF":   1,
		"PLAYER_RECEIVING_HEROES": 2,
		"PLAYER_FREE_OPERATION":   3,
		"PLAYER_READY":            4,
		"PLAYER_IN_BATTLE":        5,
		"PLAYER_ELIMINATED":       6,
	}
)

func (x PlayerState) Enum() *PlayerState {
	p := new(PlayerState)
	*p = x
	return p
}

func (x PlayerState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PlayerState) Descriptor() protoreflect.EnumDescriptor {
	return file_PublicEnum_proto_enumTypes[12].Descriptor()
}

func (PlayerState) Type() protoreflect.EnumType {
	return &file_PublicEnum_proto_enumTypes[12]
}

func (x PlayerState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PlayerState.Descriptor instead.
func (PlayerState) EnumDescriptor() ([]byte, []int) {
	return file_PublicEnum_proto_rawDescGZIP(), []int{12}
}

// 战斗结算额外奖励机制
type AdRewardType int32

const (
	AdRewardType_BATTLE_ADREWARD_NONE AdRewardType = 0
	AdRewardType_BATTLE_SUPPLY_DROP   AdRewardType = 1 // 对战补给
	AdRewardType_BATTLE_BLESSING      AdRewardType = 2 // 对战庇佑
)

// Enum value maps for AdRewardType.
var (
	AdRewardType_name = map[int32]string{
		0: "BATTLE_ADREWARD_NONE",
		1: "BATTLE_SUPPLY_DROP",
		2: "BATTLE_BLESSING",
	}
	AdRewardType_value = map[string]int32{
		"BATTLE_ADREWARD_NONE": 0,
		"BATTLE_SUPPLY_DROP":   1,
		"BATTLE_BLESSING":      2,
	}
)

func (x AdRewardType) Enum() *AdRewardType {
	p := new(AdRewardType)
	*p = x
	return p
}

func (x AdRewardType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AdRewardType) Descriptor() protoreflect.EnumDescriptor {
	return file_PublicEnum_proto_enumTypes[13].Descriptor()
}

func (AdRewardType) Type() protoreflect.EnumType {
	return &file_PublicEnum_proto_enumTypes[13]
}

func (x AdRewardType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AdRewardType.Descriptor instead.
func (AdRewardType) EnumDescriptor() ([]byte, []int) {
	return file_PublicEnum_proto_rawDescGZIP(), []int{13}
}

//======================战斗相关结束================================
//抽卡类型
type GachaType int32

const (
	GachaType_GachaType_Equip   GachaType = 0 //装备
	GachaType_GachaType_Skill   GachaType = 1 //技能
	GachaType_GachaType_Hallows GachaType = 2 //圣物
	GachaType_GachaType_Pets    GachaType = 3 //宠物
)

// Enum value maps for GachaType.
var (
	GachaType_name = map[int32]string{
		0: "GachaType_Equip",
		1: "GachaType_Skill",
		2: "GachaType_Hallows",
		3: "GachaType_Pets",
	}
	GachaType_value = map[string]int32{
		"GachaType_Equip":   0,
		"GachaType_Skill":   1,
		"GachaType_Hallows": 2,
		"GachaType_Pets":    3,
	}
)

func (x GachaType) Enum() *GachaType {
	p := new(GachaType)
	*p = x
	return p
}

func (x GachaType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GachaType) Descriptor() protoreflect.EnumDescriptor {
	return file_PublicEnum_proto_enumTypes[14].Descriptor()
}

func (GachaType) Type() protoreflect.EnumType {
	return &file_PublicEnum_proto_enumTypes[14]
}

func (x GachaType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GachaType.Descriptor instead.
func (GachaType) EnumDescriptor() ([]byte, []int) {
	return file_PublicEnum_proto_rawDescGZIP(), []int{14}
}

//装备类型
type EquipType int32

const (
	EquipType_Equip_NONE    EquipType = 0 //占位
	EquipType_Equip_Bracer  EquipType = 1 //护臂
	EquipType_Equip_Helmet  EquipType = 2 //头盔
	EquipType_Equip_Shoes   EquipType = 3 //鞋子
	EquipType_Equip_Pants   EquipType = 4 //裤子
	EquipType_Equip_Gloves  EquipType = 5 // 手套
	EquipType_Equip_Clothes EquipType = 6 // 衣服
)

// Enum value maps for EquipType.
var (
	EquipType_name = map[int32]string{
		0: "Equip_NONE",
		1: "Equip_Bracer",
		2: "Equip_Helmet",
		3: "Equip_Shoes",
		4: "Equip_Pants",
		5: "Equip_Gloves",
		6: "Equip_Clothes",
	}
	EquipType_value = map[string]int32{
		"Equip_NONE":    0,
		"Equip_Bracer":  1,
		"Equip_Helmet":  2,
		"Equip_Shoes":   3,
		"Equip_Pants":   4,
		"Equip_Gloves":  5,
		"Equip_Clothes": 6,
	}
)

func (x EquipType) Enum() *EquipType {
	p := new(EquipType)
	*p = x
	return p
}

func (x EquipType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EquipType) Descriptor() protoreflect.EnumDescriptor {
	return file_PublicEnum_proto_enumTypes[15].Descriptor()
}

func (EquipType) Type() protoreflect.EnumType {
	return &file_PublicEnum_proto_enumTypes[15]
}

func (x EquipType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EquipType.Descriptor instead.
func (EquipType) EnumDescriptor() ([]byte, []int) {
	return file_PublicEnum_proto_rawDescGZIP(), []int{15}
}

//大数值公式类型
type DropExpressType int32

const (
	DropExpressType_Express_Normal DropExpressType = 0 //普通计算公式（只返回base值）
	DropExpressType_Express_Stages DropExpressType = 1 //关卡计算公式
)

// Enum value maps for DropExpressType.
var (
	DropExpressType_name = map[int32]string{
		0: "Express_Normal",
		1: "Express_Stages",
	}
	DropExpressType_value = map[string]int32{
		"Express_Normal": 0,
		"Express_Stages": 1,
	}
)

func (x DropExpressType) Enum() *DropExpressType {
	p := new(DropExpressType)
	*p = x
	return p
}

func (x DropExpressType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DropExpressType) Descriptor() protoreflect.EnumDescriptor {
	return file_PublicEnum_proto_enumTypes[16].Descriptor()
}

func (DropExpressType) Type() protoreflect.EnumType {
	return &file_PublicEnum_proto_enumTypes[16]
}

func (x DropExpressType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DropExpressType.Descriptor instead.
func (DropExpressType) EnumDescriptor() ([]byte, []int) {
	return file_PublicEnum_proto_rawDescGZIP(), []int{16}
}

//统一品质类型
type QualityType int32

const (
	QualityType_Quality_Grey    QualityType = 0 //灰
	QualityType_Quality_Green   QualityType = 1 //绿
	QualityType_Quality_Blue    QualityType = 2 //蓝
	QualityType_Quality_Purple  QualityType = 3 //紫
	QualityType_Quality_Gold    QualityType = 4 //金
	QualityType_Quality_Orange  QualityType = 5 //橙
	QualityType_Quality_Red     QualityType = 6 //红
	QualityType_Quality_DeepRed QualityType = 7 //深红
	QualityType_Quality_Args8   QualityType = 8 //预留
)

// Enum value maps for QualityType.
var (
	QualityType_name = map[int32]string{
		0: "Quality_Grey",
		1: "Quality_Green",
		2: "Quality_Blue",
		3: "Quality_Purple",
		4: "Quality_Gold",
		5: "Quality_Orange",
		6: "Quality_Red",
		7: "Quality_DeepRed",
		8: "Quality_Args8",
	}
	QualityType_value = map[string]int32{
		"Quality_Grey":    0,
		"Quality_Green":   1,
		"Quality_Blue":    2,
		"Quality_Purple":  3,
		"Quality_Gold":    4,
		"Quality_Orange":  5,
		"Quality_Red":     6,
		"Quality_DeepRed": 7,
		"Quality_Args8":   8,
	}
)

func (x QualityType) Enum() *QualityType {
	p := new(QualityType)
	*p = x
	return p
}

func (x QualityType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (QualityType) Descriptor() protoreflect.EnumDescriptor {
	return file_PublicEnum_proto_enumTypes[17].Descriptor()
}

func (QualityType) Type() protoreflect.EnumType {
	return &file_PublicEnum_proto_enumTypes[17]
}

func (x QualityType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use QualityType.Descriptor instead.
func (QualityType) EnumDescriptor() ([]byte, []int) {
	return file_PublicEnum_proto_rawDescGZIP(), []int{17}
}

//邮件的类型
type MailType int32

const (
	MailType_MailType_NonReward               MailType = 0  //非奖励类型邮件
	MailType_MailType_Reward                  MailType = 1  // 有奖励邮件
	MailType_MailType_NotifyReward            MailType = 2  //通知奖励类型邮件
	MailType_MailType_PlayerAuth              MailType = 3  //玩家权限控制刷新-全
	MailType_MailType_PlayerAuthInfo          MailType = 4  //玩家权限控制刷新-单或组
	MailType_MailType_PlayerMailRemove        MailType = 5  //删除玩家身上的多个邮件
	MailType_MailType_GiftRMB                 MailType = 6  //道具直购（所有付费都走邮件发放）
	MailType_MailType_GlobalActivityPlayer    MailType = 7  //GlobalServer发送的玩家个人邮件
	MailType_MailType_AD                      MailType = 8  //后台广告回调邮件
	MailType_MailType_GuildOfflineMsg         MailType = 9  //公会操作时对离线玩家通知（写入离线数据）
	MailType_MailType_QuestAward              MailType = 10 //问卷奖励
	MailType_MailType_Activity_OpenServer_End MailType = 11 //开服活动结束
)

// Enum value maps for MailType.
var (
	MailType_name = map[int32]string{
		0:  "MailType_NonReward",
		1:  "MailType_Reward",
		2:  "MailType_NotifyReward",
		3:  "MailType_PlayerAuth",
		4:  "MailType_PlayerAuthInfo",
		5:  "MailType_PlayerMailRemove",
		6:  "MailType_GiftRMB",
		7:  "MailType_GlobalActivityPlayer",
		8:  "MailType_AD",
		9:  "MailType_GuildOfflineMsg",
		10: "MailType_QuestAward",
		11: "MailType_Activity_OpenServer_End",
	}
	MailType_value = map[string]int32{
		"MailType_NonReward":               0,
		"MailType_Reward":                  1,
		"MailType_NotifyReward":            2,
		"MailType_PlayerAuth":              3,
		"MailType_PlayerAuthInfo":          4,
		"MailType_PlayerMailRemove":        5,
		"MailType_GiftRMB":                 6,
		"MailType_GlobalActivityPlayer":    7,
		"MailType_AD":                      8,
		"MailType_GuildOfflineMsg":         9,
		"MailType_QuestAward":              10,
		"MailType_Activity_OpenServer_End": 11,
	}
)

func (x MailType) Enum() *MailType {
	p := new(MailType)
	*p = x
	return p
}

func (x MailType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MailType) Descriptor() protoreflect.EnumDescriptor {
	return file_PublicEnum_proto_enumTypes[18].Descriptor()
}

func (MailType) Type() protoreflect.EnumType {
	return &file_PublicEnum_proto_enumTypes[18]
}

func (x MailType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MailType.Descriptor instead.
func (MailType) EnumDescriptor() ([]byte, []int) {
	return file_PublicEnum_proto_rawDescGZIP(), []int{18}
}

//邮件的打开类型
type MailStateType int32

const (
	MailStateType_MailStateType_UnRead   MailStateType = 0 //未读
	MailStateType_MailStateType_Read     MailStateType = 1 //已读
	MailStateType_MailStateType_GotAward MailStateType = 2 //已领取
)

// Enum value maps for MailStateType.
var (
	MailStateType_name = map[int32]string{
		0: "MailStateType_UnRead",
		1: "MailStateType_Read",
		2: "MailStateType_GotAward",
	}
	MailStateType_value = map[string]int32{
		"MailStateType_UnRead":   0,
		"MailStateType_Read":     1,
		"MailStateType_GotAward": 2,
	}
)

func (x MailStateType) Enum() *MailStateType {
	p := new(MailStateType)
	*p = x
	return p
}

func (x MailStateType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MailStateType) Descriptor() protoreflect.EnumDescriptor {
	return file_PublicEnum_proto_enumTypes[19].Descriptor()
}

func (MailStateType) Type() protoreflect.EnumType {
	return &file_PublicEnum_proto_enumTypes[19]
}

func (x MailStateType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MailStateType.Descriptor instead.
func (MailStateType) EnumDescriptor() ([]byte, []int) {
	return file_PublicEnum_proto_rawDescGZIP(), []int{19}
}

//邮件的打开类型
type MailReasonType int32

const (
	MailReasonType_MailReasonType_TestGM   MailReasonType = 0 // 测试
	MailReasonType_MailReasonType_GMRemote MailReasonType = 1 //GM管理员
	MailReasonType_MailReasonType_System   MailReasonType = 2 //系统邮件
)

// Enum value maps for MailReasonType.
var (
	MailReasonType_name = map[int32]string{
		0: "MailReasonType_TestGM",
		1: "MailReasonType_GMRemote",
		2: "MailReasonType_System",
	}
	MailReasonType_value = map[string]int32{
		"MailReasonType_TestGM":   0,
		"MailReasonType_GMRemote": 1,
		"MailReasonType_System":   2,
	}
)

func (x MailReasonType) Enum() *MailReasonType {
	p := new(MailReasonType)
	*p = x
	return p
}

func (x MailReasonType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MailReasonType) Descriptor() protoreflect.EnumDescriptor {
	return file_PublicEnum_proto_enumTypes[20].Descriptor()
}

func (MailReasonType) Type() protoreflect.EnumType {
	return &file_PublicEnum_proto_enumTypes[20]
}

func (x MailReasonType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MailReasonType.Descriptor instead.
func (MailReasonType) EnumDescriptor() ([]byte, []int) {
	return file_PublicEnum_proto_rawDescGZIP(), []int{20}
}

//旧版新手引导触发类型
type GuideTriggerType int32

const (
	GuideTriggerType_None           GuideTriggerType = 0
	GuideTriggerType_NewPlayer      GuideTriggerType = 1
	GuideTriggerType_OpenUi         GuideTriggerType = 2
	GuideTriggerType_CloseUi        GuideTriggerType = 3
	GuideTriggerType_UnlockFuncId   GuideTriggerType = 4
	GuideTriggerType_PassedMainLine GuideTriggerType = 5
	GuideTriggerType_FinishGuide    GuideTriggerType = 6
)

// Enum value maps for GuideTriggerType.
var (
	GuideTriggerType_name = map[int32]string{
		0: "None",
		1: "NewPlayer",
		2: "OpenUi",
		3: "CloseUi",
		4: "UnlockFuncId",
		5: "PassedMainLine",
		6: "FinishGuide",
	}
	GuideTriggerType_value = map[string]int32{
		"None":           0,
		"NewPlayer":      1,
		"OpenUi":         2,
		"CloseUi":        3,
		"UnlockFuncId":   4,
		"PassedMainLine": 5,
		"FinishGuide":    6,
	}
)

func (x GuideTriggerType) Enum() *GuideTriggerType {
	p := new(GuideTriggerType)
	*p = x
	return p
}

func (x GuideTriggerType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GuideTriggerType) Descriptor() protoreflect.EnumDescriptor {
	return file_PublicEnum_proto_enumTypes[21].Descriptor()
}

func (GuideTriggerType) Type() protoreflect.EnumType {
	return &file_PublicEnum_proto_enumTypes[21]
}

func (x GuideTriggerType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GuideTriggerType.Descriptor instead.
func (GuideTriggerType) EnumDescriptor() ([]byte, []int) {
	return file_PublicEnum_proto_rawDescGZIP(), []int{21}
}

//新手引导的触发类型
type NewGuideTriggerType int32

const (
	NewGuideTriggerType_NewGuideTriggerType_Normal           NewGuideTriggerType = 0 //无效的触发
	NewGuideTriggerType_NewGuideTriggerType_GetCompleteTask  NewGuideTriggerType = 1 //领取主线完成任务
	NewGuideTriggerType_NewGuideTriggerType_MissionFinish    NewGuideTriggerType = 2 //完成任务
	NewGuideTriggerType_NewGuideTriggerType_NewGuildEnd      NewGuideTriggerType = 3 //新手结束触发下一个新手
	NewGuideTriggerType_NewGuideTriggerType_InstanceEnd      NewGuideTriggerType = 4 //金币 经验 主线 成功结束
	NewGuideTriggerType_NewGuideTriggerType_CreateName       NewGuideTriggerType = 5 //起名
	NewGuideTriggerType_NewGuideTriggerType_CooperationStage NewGuideTriggerType = 6 //玩家首次挑战合作舞台挑战胜利触发引导
	NewGuideTriggerType_NewGuideTriggerType_LotteryEnd       NewGuideTriggerType = 7 //抽卡结束
)

// Enum value maps for NewGuideTriggerType.
var (
	NewGuideTriggerType_name = map[int32]string{
		0: "NewGuideTriggerType_Normal",
		1: "NewGuideTriggerType_GetCompleteTask",
		2: "NewGuideTriggerType_MissionFinish",
		3: "NewGuideTriggerType_NewGuildEnd",
		4: "NewGuideTriggerType_InstanceEnd",
		5: "NewGuideTriggerType_CreateName",
		6: "NewGuideTriggerType_CooperationStage",
		7: "NewGuideTriggerType_LotteryEnd",
	}
	NewGuideTriggerType_value = map[string]int32{
		"NewGuideTriggerType_Normal":           0,
		"NewGuideTriggerType_GetCompleteTask":  1,
		"NewGuideTriggerType_MissionFinish":    2,
		"NewGuideTriggerType_NewGuildEnd":      3,
		"NewGuideTriggerType_InstanceEnd":      4,
		"NewGuideTriggerType_CreateName":       5,
		"NewGuideTriggerType_CooperationStage": 6,
		"NewGuideTriggerType_LotteryEnd":       7,
	}
)

func (x NewGuideTriggerType) Enum() *NewGuideTriggerType {
	p := new(NewGuideTriggerType)
	*p = x
	return p
}

func (x NewGuideTriggerType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NewGuideTriggerType) Descriptor() protoreflect.EnumDescriptor {
	return file_PublicEnum_proto_enumTypes[22].Descriptor()
}

func (NewGuideTriggerType) Type() protoreflect.EnumType {
	return &file_PublicEnum_proto_enumTypes[22]
}

func (x NewGuideTriggerType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NewGuideTriggerType.Descriptor instead.
func (NewGuideTriggerType) EnumDescriptor() ([]byte, []int) {
	return file_PublicEnum_proto_rawDescGZIP(), []int{22}
}

//新手引导的功能类型
type NewGuideFunType int32

const (
	NewGuideFunType_NewGuideFunType_Normal     NewGuideFunType = 0 //无效的触发
	NewGuideFunType_NewGuideFunType_ChangeName NewGuideFunType = 1 //起名
)

// Enum value maps for NewGuideFunType.
var (
	NewGuideFunType_name = map[int32]string{
		0: "NewGuideFunType_Normal",
		1: "NewGuideFunType_ChangeName",
	}
	NewGuideFunType_value = map[string]int32{
		"NewGuideFunType_Normal":     0,
		"NewGuideFunType_ChangeName": 1,
	}
)

func (x NewGuideFunType) Enum() *NewGuideFunType {
	p := new(NewGuideFunType)
	*p = x
	return p
}

func (x NewGuideFunType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NewGuideFunType) Descriptor() protoreflect.EnumDescriptor {
	return file_PublicEnum_proto_enumTypes[23].Descriptor()
}

func (NewGuideFunType) Type() protoreflect.EnumType {
	return &file_PublicEnum_proto_enumTypes[23]
}

func (x NewGuideFunType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NewGuideFunType.Descriptor instead.
func (NewGuideFunType) EnumDescriptor() ([]byte, []int) {
	return file_PublicEnum_proto_rawDescGZIP(), []int{23}
}

//签到的类型
type SignInToType int32

const (
	SignInToType_SignInToType_None       SignInToType = 0 //未知的类型
	SignInToType_SignInToType_SevenDay   SignInToType = 1 //七天签到
	SignInToType_SignInToType_EveryDay   SignInToType = 2 //每天签到
	SignInToType_SignInToType_MonthStage SignInToType = 3 //每月阶段奖励
)

// Enum value maps for SignInToType.
var (
	SignInToType_name = map[int32]string{
		0: "SignInToType_None",
		1: "SignInToType_SevenDay",
		2: "SignInToType_EveryDay",
		3: "SignInToType_MonthStage",
	}
	SignInToType_value = map[string]int32{
		"SignInToType_None":       0,
		"SignInToType_SevenDay":   1,
		"SignInToType_EveryDay":   2,
		"SignInToType_MonthStage": 3,
	}
)

func (x SignInToType) Enum() *SignInToType {
	p := new(SignInToType)
	*p = x
	return p
}

func (x SignInToType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SignInToType) Descriptor() protoreflect.EnumDescriptor {
	return file_PublicEnum_proto_enumTypes[24].Descriptor()
}

func (SignInToType) Type() protoreflect.EnumType {
	return &file_PublicEnum_proto_enumTypes[24]
}

func (x SignInToType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SignInToType.Descriptor instead.
func (SignInToType) EnumDescriptor() ([]byte, []int) {
	return file_PublicEnum_proto_rawDescGZIP(), []int{24}
}

//奖励状态
type RewardStatus int32

const (
	RewardStatus_RewardStatus_Doing    RewardStatus = 0 //未完成
	RewardStatus_RewardStatus_Finish   RewardStatus = 1 //已完成
	RewardStatus_RewardStatus_Received RewardStatus = 2 //已领奖
)

// Enum value maps for RewardStatus.
var (
	RewardStatus_name = map[int32]string{
		0: "RewardStatus_Doing",
		1: "RewardStatus_Finish",
		2: "RewardStatus_Received",
	}
	RewardStatus_value = map[string]int32{
		"RewardStatus_Doing":    0,
		"RewardStatus_Finish":   1,
		"RewardStatus_Received": 2,
	}
)

func (x RewardStatus) Enum() *RewardStatus {
	p := new(RewardStatus)
	*p = x
	return p
}

func (x RewardStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RewardStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_PublicEnum_proto_enumTypes[25].Descriptor()
}

func (RewardStatus) Type() protoreflect.EnumType {
	return &file_PublicEnum_proto_enumTypes[25]
}

func (x RewardStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RewardStatus.Descriptor instead.
func (RewardStatus) EnumDescriptor() ([]byte, []int) {
	return file_PublicEnum_proto_rawDescGZIP(), []int{25}
}

//奖励类型
type RewardType int32

const (
	RewardType_REWARD_TYPE_UNKNOWN     RewardType = 0
	RewardType_REWARD_TYPE_RANK        RewardType = 1 // 普通段位奖励
	RewardType_REWARD_TYPE_SEASON_RANK RewardType = 2 // 赛季段位奖励
)

// Enum value maps for RewardType.
var (
	RewardType_name = map[int32]string{
		0: "REWARD_TYPE_UNKNOWN",
		1: "REWARD_TYPE_RANK",
		2: "REWARD_TYPE_SEASON_RANK",
	}
	RewardType_value = map[string]int32{
		"REWARD_TYPE_UNKNOWN":     0,
		"REWARD_TYPE_RANK":        1,
		"REWARD_TYPE_SEASON_RANK": 2,
	}
)

func (x RewardType) Enum() *RewardType {
	p := new(RewardType)
	*p = x
	return p
}

func (x RewardType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RewardType) Descriptor() protoreflect.EnumDescriptor {
	return file_PublicEnum_proto_enumTypes[26].Descriptor()
}

func (RewardType) Type() protoreflect.EnumType {
	return &file_PublicEnum_proto_enumTypes[26]
}

func (x RewardType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RewardType.Descriptor instead.
func (RewardType) EnumDescriptor() ([]byte, []int) {
	return file_PublicEnum_proto_rawDescGZIP(), []int{26}
}

//道具类型
type ItemType int32

const (
	ItemType_Unavailable  ItemType = 0 //不可使用类
	ItemType_Drop         ItemType = 1 //掉落类型
	ItemType_HangUpReward ItemType = 2 //挂机奖励类型
	ItemType_ChooseBox    ItemType = 3 //自选宝箱
	ItemType_Devris       ItemType = 4 //碎片类型
)

// Enum value maps for ItemType.
var (
	ItemType_name = map[int32]string{
		0: "Unavailable",
		1: "Drop",
		2: "HangUpReward",
		3: "ChooseBox",
		4: "Devris",
	}
	ItemType_value = map[string]int32{
		"Unavailable":  0,
		"Drop":         1,
		"HangUpReward": 2,
		"ChooseBox":    3,
		"Devris":       4,
	}
)

func (x ItemType) Enum() *ItemType {
	p := new(ItemType)
	*p = x
	return p
}

func (x ItemType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ItemType) Descriptor() protoreflect.EnumDescriptor {
	return file_PublicEnum_proto_enumTypes[27].Descriptor()
}

func (ItemType) Type() protoreflect.EnumType {
	return &file_PublicEnum_proto_enumTypes[27]
}

func (x ItemType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ItemType.Descriptor instead.
func (ItemType) EnumDescriptor() ([]byte, []int) {
	return file_PublicEnum_proto_rawDescGZIP(), []int{27}
}

type ItemSubType_ChooseBox int32

const (
	ItemSubType_ChooseBox_ChooseBox_Once  ItemSubType_ChooseBox = 0 //只能选一个
	ItemSubType_ChooseBox_ChooseBox_Multi ItemSubType_ChooseBox = 1 //可批量多选
)

// Enum value maps for ItemSubType_ChooseBox.
var (
	ItemSubType_ChooseBox_name = map[int32]string{
		0: "ChooseBox_Once",
		1: "ChooseBox_Multi",
	}
	ItemSubType_ChooseBox_value = map[string]int32{
		"ChooseBox_Once":  0,
		"ChooseBox_Multi": 1,
	}
)

func (x ItemSubType_ChooseBox) Enum() *ItemSubType_ChooseBox {
	p := new(ItemSubType_ChooseBox)
	*p = x
	return p
}

func (x ItemSubType_ChooseBox) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ItemSubType_ChooseBox) Descriptor() protoreflect.EnumDescriptor {
	return file_PublicEnum_proto_enumTypes[28].Descriptor()
}

func (ItemSubType_ChooseBox) Type() protoreflect.EnumType {
	return &file_PublicEnum_proto_enumTypes[28]
}

func (x ItemSubType_ChooseBox) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ItemSubType_ChooseBox.Descriptor instead.
func (ItemSubType_ChooseBox) EnumDescriptor() ([]byte, []int) {
	return file_PublicEnum_proto_rawDescGZIP(), []int{28}
}

//道具子类型 (掉落类型)
type ItemSubType_Drop int32

const (
	ItemSubType_Drop_Drop_Fixed  ItemSubType_Drop = 0 //固定类型
	ItemSubType_Drop_Drop_Random ItemSubType_Drop = 1 //随机类
)

// Enum value maps for ItemSubType_Drop.
var (
	ItemSubType_Drop_name = map[int32]string{
		0: "Drop_Fixed",
		1: "Drop_Random",
	}
	ItemSubType_Drop_value = map[string]int32{
		"Drop_Fixed":  0,
		"Drop_Random": 1,
	}
)

func (x ItemSubType_Drop) Enum() *ItemSubType_Drop {
	p := new(ItemSubType_Drop)
	*p = x
	return p
}

func (x ItemSubType_Drop) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ItemSubType_Drop) Descriptor() protoreflect.EnumDescriptor {
	return file_PublicEnum_proto_enumTypes[29].Descriptor()
}

func (ItemSubType_Drop) Type() protoreflect.EnumType {
	return &file_PublicEnum_proto_enumTypes[29]
}

func (x ItemSubType_Drop) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ItemSubType_Drop.Descriptor instead.
func (ItemSubType_Drop) EnumDescriptor() ([]byte, []int) {
	return file_PublicEnum_proto_rawDescGZIP(), []int{29}
}

//道具子类型 (碎片类型)
type ItemSubType_Devris int32

const (
	ItemSubType_Devris_Devris_Skin    ItemSubType_Devris = 0 //皮肤
	ItemSubType_Devris_Devris_Firearm ItemSubType_Devris = 1 //枪械
	ItemSubType_Devris_Devris_Wall    ItemSubType_Devris = 2 //城墙
	ItemSubType_Devris_Devris_Pet     ItemSubType_Devris = 3 //宠物
)

// Enum value maps for ItemSubType_Devris.
var (
	ItemSubType_Devris_name = map[int32]string{
		0: "Devris_Skin",
		1: "Devris_Firearm",
		2: "Devris_Wall",
		3: "Devris_Pet",
	}
	ItemSubType_Devris_value = map[string]int32{
		"Devris_Skin":    0,
		"Devris_Firearm": 1,
		"Devris_Wall":    2,
		"Devris_Pet":     3,
	}
)

func (x ItemSubType_Devris) Enum() *ItemSubType_Devris {
	p := new(ItemSubType_Devris)
	*p = x
	return p
}

func (x ItemSubType_Devris) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ItemSubType_Devris) Descriptor() protoreflect.EnumDescriptor {
	return file_PublicEnum_proto_enumTypes[30].Descriptor()
}

func (ItemSubType_Devris) Type() protoreflect.EnumType {
	return &file_PublicEnum_proto_enumTypes[30]
}

func (x ItemSubType_Devris) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ItemSubType_Devris.Descriptor instead.
func (ItemSubType_Devris) EnumDescriptor() ([]byte, []int) {
	return file_PublicEnum_proto_rawDescGZIP(), []int{30}
}

//背包对象结构类型
type BagItemType int32

const (
	BagItemType_NormalItem BagItemType = 0 //常规物品类型
)

// Enum value maps for BagItemType.
var (
	BagItemType_name = map[int32]string{
		0: "NormalItem",
	}
	BagItemType_value = map[string]int32{
		"NormalItem": 0,
	}
)

func (x BagItemType) Enum() *BagItemType {
	p := new(BagItemType)
	*p = x
	return p
}

func (x BagItemType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BagItemType) Descriptor() protoreflect.EnumDescriptor {
	return file_PublicEnum_proto_enumTypes[31].Descriptor()
}

func (BagItemType) Type() protoreflect.EnumType {
	return &file_PublicEnum_proto_enumTypes[31]
}

func (x BagItemType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BagItemType.Descriptor instead.
func (BagItemType) EnumDescriptor() ([]byte, []int) {
	return file_PublicEnum_proto_rawDescGZIP(), []int{31}
}

//系统提示
type SystemShowErrorType int32

const (
	SystemShowErrorType_SystemShowErrorType_None    SystemShowErrorType = 0 //没有特殊需求的error
	SystemShowErrorType_SystemShowErrorType_Kick    SystemShowErrorType = 1 //踢人
	SystemShowErrorType_SystemShowErrorType_Replace SystemShowErrorType = 2 //顶号
	SystemShowErrorType_SystemShowErrorType_ChatBan SystemShowErrorType = 3 //禁言
	SystemShowErrorType_SystemShowErrorType_Tips    SystemShowErrorType = 4 //仅弹窗提示
	SystemShowErrorType_SystemShowErrorType_Login   SystemShowErrorType = 5 //登录
)

// Enum value maps for SystemShowErrorType.
var (
	SystemShowErrorType_name = map[int32]string{
		0: "SystemShowErrorType_None",
		1: "SystemShowErrorType_Kick",
		2: "SystemShowErrorType_Replace",
		3: "SystemShowErrorType_ChatBan",
		4: "SystemShowErrorType_Tips",
		5: "SystemShowErrorType_Login",
	}
	SystemShowErrorType_value = map[string]int32{
		"SystemShowErrorType_None":    0,
		"SystemShowErrorType_Kick":    1,
		"SystemShowErrorType_Replace": 2,
		"SystemShowErrorType_ChatBan": 3,
		"SystemShowErrorType_Tips":    4,
		"SystemShowErrorType_Login":   5,
	}
)

func (x SystemShowErrorType) Enum() *SystemShowErrorType {
	p := new(SystemShowErrorType)
	*p = x
	return p
}

func (x SystemShowErrorType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SystemShowErrorType) Descriptor() protoreflect.EnumDescriptor {
	return file_PublicEnum_proto_enumTypes[32].Descriptor()
}

func (SystemShowErrorType) Type() protoreflect.EnumType {
	return &file_PublicEnum_proto_enumTypes[32]
}

func (x SystemShowErrorType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SystemShowErrorType.Descriptor instead.
func (SystemShowErrorType) EnumDescriptor() ([]byte, []int) {
	return file_PublicEnum_proto_rawDescGZIP(), []int{32}
}

//礼包类型
type GiftPacksType int32

const (
	GiftPacksType_GiftPacksType_None      GiftPacksType = 0
	GiftPacksType_GiftPacksType_Limit     GiftPacksType = 1 //限时礼包
	GiftPacksType_GiftPacksType_Weekly    GiftPacksType = 2 //
	GiftPacksType_GiftPacksType_DailyPlus GiftPacksType = 3 //每日特惠礼包
	GiftPacksType_GiftPacksType_WeekCard  GiftPacksType = 4 //周卡礼包
	GiftPacksType_GiftPacksType_Recommend GiftPacksType = 5 //推荐礼包
)

// Enum value maps for GiftPacksType.
var (
	GiftPacksType_name = map[int32]string{
		0: "GiftPacksType_None",
		1: "GiftPacksType_Limit",
		2: "GiftPacksType_Weekly",
		3: "GiftPacksType_DailyPlus",
		4: "GiftPacksType_WeekCard",
		5: "GiftPacksType_Recommend",
	}
	GiftPacksType_value = map[string]int32{
		"GiftPacksType_None":      0,
		"GiftPacksType_Limit":     1,
		"GiftPacksType_Weekly":    2,
		"GiftPacksType_DailyPlus": 3,
		"GiftPacksType_WeekCard":  4,
		"GiftPacksType_Recommend": 5,
	}
)

func (x GiftPacksType) Enum() *GiftPacksType {
	p := new(GiftPacksType)
	*p = x
	return p
}

func (x GiftPacksType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GiftPacksType) Descriptor() protoreflect.EnumDescriptor {
	return file_PublicEnum_proto_enumTypes[33].Descriptor()
}

func (GiftPacksType) Type() protoreflect.EnumType {
	return &file_PublicEnum_proto_enumTypes[33]
}

func (x GiftPacksType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GiftPacksType.Descriptor instead.
func (GiftPacksType) EnumDescriptor() ([]byte, []int) {
	return file_PublicEnum_proto_rawDescGZIP(), []int{33}
}

//礼包购买类型
type GiftPacksBuyType int32

const (
	GiftPacksBuyType_GiftPacksBuyType_Adv     GiftPacksBuyType = 0 //广告购买
	GiftPacksBuyType_GiftPacksBuyType_Rmb     GiftPacksBuyType = 1 //人民币购买
	GiftPacksBuyType_GiftPacksBuyType_Free    GiftPacksBuyType = 2 //免费领取
	GiftPacksBuyType_GiftPacksBuyType_Diamond GiftPacksBuyType = 3 //钻石购买礼包
)

// Enum value maps for GiftPacksBuyType.
var (
	GiftPacksBuyType_name = map[int32]string{
		0: "GiftPacksBuyType_Adv",
		1: "GiftPacksBuyType_Rmb",
		2: "GiftPacksBuyType_Free",
		3: "GiftPacksBuyType_Diamond",
	}
	GiftPacksBuyType_value = map[string]int32{
		"GiftPacksBuyType_Adv":     0,
		"GiftPacksBuyType_Rmb":     1,
		"GiftPacksBuyType_Free":    2,
		"GiftPacksBuyType_Diamond": 3,
	}
)

func (x GiftPacksBuyType) Enum() *GiftPacksBuyType {
	p := new(GiftPacksBuyType)
	*p = x
	return p
}

func (x GiftPacksBuyType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GiftPacksBuyType) Descriptor() protoreflect.EnumDescriptor {
	return file_PublicEnum_proto_enumTypes[34].Descriptor()
}

func (GiftPacksBuyType) Type() protoreflect.EnumType {
	return &file_PublicEnum_proto_enumTypes[34]
}

func (x GiftPacksBuyType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GiftPacksBuyType.Descriptor instead.
func (GiftPacksBuyType) EnumDescriptor() ([]byte, []int) {
	return file_PublicEnum_proto_rawDescGZIP(), []int{34}
}

//月卡类型
type ChargeType int32

const (
	ChargeType_ChargeType_None            ChargeType = 0 //
	ChargeType_ChargeType_Diamond         ChargeType = 1 //钻石档位
	ChargeType_ChargeType_MonthCardNormal ChargeType = 2 //普通
	ChargeType_ChargeType_MonthCardSuper  ChargeType = 3 //超级
	ChargeType_ChargeType_Item            ChargeType = 4 //道具直购
)

// Enum value maps for ChargeType.
var (
	ChargeType_name = map[int32]string{
		0: "ChargeType_None",
		1: "ChargeType_Diamond",
		2: "ChargeType_MonthCardNormal",
		3: "ChargeType_MonthCardSuper",
		4: "ChargeType_Item",
	}
	ChargeType_value = map[string]int32{
		"ChargeType_None":            0,
		"ChargeType_Diamond":         1,
		"ChargeType_MonthCardNormal": 2,
		"ChargeType_MonthCardSuper":  3,
		"ChargeType_Item":            4,
	}
)

func (x ChargeType) Enum() *ChargeType {
	p := new(ChargeType)
	*p = x
	return p
}

func (x ChargeType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ChargeType) Descriptor() protoreflect.EnumDescriptor {
	return file_PublicEnum_proto_enumTypes[35].Descriptor()
}

func (ChargeType) Type() protoreflect.EnumType {
	return &file_PublicEnum_proto_enumTypes[35]
}

func (x ChargeType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ChargeType.Descriptor instead.
func (ChargeType) EnumDescriptor() ([]byte, []int) {
	return file_PublicEnum_proto_rawDescGZIP(), []int{35}
}

//通用阶段宝箱 特殊固定类别
type CommonBoxRewardType int32

const (
	CommonBoxRewardType_CommonBoxReward_None             CommonBoxRewardType = 0 //
	CommonBoxRewardType_CommonBoxReward_DailyMission     CommonBoxRewardType = 1 //每日宝箱
	CommonBoxRewardType_CommonBoxReward_SevenDayActivity CommonBoxRewardType = 2 //七日任务活动宝箱
)

// Enum value maps for CommonBoxRewardType.
var (
	CommonBoxRewardType_name = map[int32]string{
		0: "CommonBoxReward_None",
		1: "CommonBoxReward_DailyMission",
		2: "CommonBoxReward_SevenDayActivity",
	}
	CommonBoxRewardType_value = map[string]int32{
		"CommonBoxReward_None":             0,
		"CommonBoxReward_DailyMission":     1,
		"CommonBoxReward_SevenDayActivity": 2,
	}
)

func (x CommonBoxRewardType) Enum() *CommonBoxRewardType {
	p := new(CommonBoxRewardType)
	*p = x
	return p
}

func (x CommonBoxRewardType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CommonBoxRewardType) Descriptor() protoreflect.EnumDescriptor {
	return file_PublicEnum_proto_enumTypes[36].Descriptor()
}

func (CommonBoxRewardType) Type() protoreflect.EnumType {
	return &file_PublicEnum_proto_enumTypes[36]
}

func (x CommonBoxRewardType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CommonBoxRewardType.Descriptor instead.
func (CommonBoxRewardType) EnumDescriptor() ([]byte, []int) {
	return file_PublicEnum_proto_rawDescGZIP(), []int{36}
}

//=====================战令开始===================================
// 战令类型
type BattlePassType int32

const (
	BattlePassType_BattlePassType_NONE    BattlePassType = 0 // 未购买
	BattlePassType_BattlePassType_BASE    BattlePassType = 1 // 基础战令
	BattlePassType_BattlePassType_ADVANCE BattlePassType = 2 // 进阶战令
)

// Enum value maps for BattlePassType.
var (
	BattlePassType_name = map[int32]string{
		0: "BattlePassType_NONE",
		1: "BattlePassType_BASE",
		2: "BattlePassType_ADVANCE",
	}
	BattlePassType_value = map[string]int32{
		"BattlePassType_NONE":    0,
		"BattlePassType_BASE":    1,
		"BattlePassType_ADVANCE": 2,
	}
)

func (x BattlePassType) Enum() *BattlePassType {
	p := new(BattlePassType)
	*p = x
	return p
}

func (x BattlePassType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BattlePassType) Descriptor() protoreflect.EnumDescriptor {
	return file_PublicEnum_proto_enumTypes[37].Descriptor()
}

func (BattlePassType) Type() protoreflect.EnumType {
	return &file_PublicEnum_proto_enumTypes[37]
}

func (x BattlePassType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BattlePassType.Descriptor instead.
func (BattlePassType) EnumDescriptor() ([]byte, []int) {
	return file_PublicEnum_proto_rawDescGZIP(), []int{37}
}

// 战令任务类型
type BattlePassMissionType int32

const (
	BattlePassMissionType_BattlePassMissionType_NONE   BattlePassMissionType = 0 // 非法
	BattlePassMissionType_BattlePassMissionType_DAILY  BattlePassMissionType = 1 // 每日
	BattlePassMissionType_BattlePassMissionType_WEEKLY BattlePassMissionType = 2 // 每周
)

// Enum value maps for BattlePassMissionType.
var (
	BattlePassMissionType_name = map[int32]string{
		0: "BattlePassMissionType_NONE",
		1: "BattlePassMissionType_DAILY",
		2: "BattlePassMissionType_WEEKLY",
	}
	BattlePassMissionType_value = map[string]int32{
		"BattlePassMissionType_NONE":   0,
		"BattlePassMissionType_DAILY":  1,
		"BattlePassMissionType_WEEKLY": 2,
	}
)

func (x BattlePassMissionType) Enum() *BattlePassMissionType {
	p := new(BattlePassMissionType)
	*p = x
	return p
}

func (x BattlePassMissionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BattlePassMissionType) Descriptor() protoreflect.EnumDescriptor {
	return file_PublicEnum_proto_enumTypes[38].Descriptor()
}

func (BattlePassMissionType) Type() protoreflect.EnumType {
	return &file_PublicEnum_proto_enumTypes[38]
}

func (x BattlePassMissionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BattlePassMissionType.Descriptor instead.
func (BattlePassMissionType) EnumDescriptor() ([]byte, []int) {
	return file_PublicEnum_proto_rawDescGZIP(), []int{38}
}

// 战令奖励领取状态
type AwardState int32

const (
	AwardState_AwardState_UNAVAILABLE AwardState = 0 // 未领取
	AwardState_AwardState_AVAILABLE   AwardState = 1 // 可领取
	AwardState_AwardState_RECEIVED    AwardState = 2 // 已领取
)

// Enum value maps for AwardState.
var (
	AwardState_name = map[int32]string{
		0: "AwardState_UNAVAILABLE",
		1: "AwardState_AVAILABLE",
		2: "AwardState_RECEIVED",
	}
	AwardState_value = map[string]int32{
		"AwardState_UNAVAILABLE": 0,
		"AwardState_AVAILABLE":   1,
		"AwardState_RECEIVED":    2,
	}
)

func (x AwardState) Enum() *AwardState {
	p := new(AwardState)
	*p = x
	return p
}

func (x AwardState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AwardState) Descriptor() protoreflect.EnumDescriptor {
	return file_PublicEnum_proto_enumTypes[39].Descriptor()
}

func (AwardState) Type() protoreflect.EnumType {
	return &file_PublicEnum_proto_enumTypes[39]
}

func (x AwardState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AwardState.Descriptor instead.
func (AwardState) EnumDescriptor() ([]byte, []int) {
	return file_PublicEnum_proto_rawDescGZIP(), []int{39}
}

//=====================战令结束===================================
// 活动类型
type ActivityType int32

const (
	ActivityType_ActivityType_BattlePass   ActivityType = 0 //0.战令
	ActivityType_ActivityType_SevenTask    ActivityType = 1 //1.七日任务（仅占位）
	ActivityType_ActivityType_SevenSign    ActivityType = 2 //2.七日签到（仅占位）
	ActivityType_ActivityType_SeasonBuff_1 ActivityType = 4 //4.赛季buff1
)

// Enum value maps for ActivityType.
var (
	ActivityType_name = map[int32]string{
		0: "ActivityType_BattlePass",
		1: "ActivityType_SevenTask",
		2: "ActivityType_SevenSign",
		4: "ActivityType_SeasonBuff_1",
	}
	ActivityType_value = map[string]int32{
		"ActivityType_BattlePass":   0,
		"ActivityType_SevenTask":    1,
		"ActivityType_SevenSign":    2,
		"ActivityType_SeasonBuff_1": 4,
	}
)

func (x ActivityType) Enum() *ActivityType {
	p := new(ActivityType)
	*p = x
	return p
}

func (x ActivityType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ActivityType) Descriptor() protoreflect.EnumDescriptor {
	return file_PublicEnum_proto_enumTypes[40].Descriptor()
}

func (ActivityType) Type() protoreflect.EnumType {
	return &file_PublicEnum_proto_enumTypes[40]
}

func (x ActivityType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ActivityType.Descriptor instead.
func (ActivityType) EnumDescriptor() ([]byte, []int) {
	return file_PublicEnum_proto_rawDescGZIP(), []int{40}
}

// 活动状态
type ActivityStatus int32

const (
	ActivityStatus_ActivityStatus_NO_OPEN ActivityStatus = 0 //0.未开启
	ActivityStatus_ActivityStatus_START   ActivityStatus = 1 //1.已开始（系统预处理阶段，服务器使用）
	ActivityStatus_ActivityStatus_END     ActivityStatus = 2 //3.已结束（客户端判断此状态为活动正式结束）
)

// Enum value maps for ActivityStatus.
var (
	ActivityStatus_name = map[int32]string{
		0: "ActivityStatus_NO_OPEN",
		1: "ActivityStatus_START",
		2: "ActivityStatus_END",
	}
	ActivityStatus_value = map[string]int32{
		"ActivityStatus_NO_OPEN": 0,
		"ActivityStatus_START":   1,
		"ActivityStatus_END":     2,
	}
)

func (x ActivityStatus) Enum() *ActivityStatus {
	p := new(ActivityStatus)
	*p = x
	return p
}

func (x ActivityStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ActivityStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_PublicEnum_proto_enumTypes[41].Descriptor()
}

func (ActivityStatus) Type() protoreflect.EnumType {
	return &file_PublicEnum_proto_enumTypes[41]
}

func (x ActivityStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ActivityStatus.Descriptor instead.
func (ActivityStatus) EnumDescriptor() ([]byte, []int) {
	return file_PublicEnum_proto_rawDescGZIP(), []int{41}
}

//=====================道具前往获取开始===================================
type ItemAcquisitionState int32

const (
	ItemAcquisitionState_ItemAcquisitionState_NONE                      ItemAcquisitionState = 0  // 0.表示不可用
	ItemAcquisitionState_ItemAcquisitionState_Diamond                   ItemAcquisitionState = 1  // 钻石
	ItemAcquisitionState_ItemAcquisitionState_Star                      ItemAcquisitionState = 2  // 星星
	ItemAcquisitionState_ItemAcquisitionState_Essence                   ItemAcquisitionState = 3  // 神之精华
	ItemAcquisitionState_ItemAcquisitionState_AirPurifier               ItemAcquisitionState = 4  // 空气净化器
	ItemAcquisitionState_ItemAcquisitionState_UpgradeTicket             ItemAcquisitionState = 5  // 建筑升级门票
	ItemAcquisitionState_ItemAcquisitionState_DuplicateTicket           ItemAcquisitionState = 6  // 副本门票
	ItemAcquisitionState_ItemAcquisitionState_Shovel                    ItemAcquisitionState = 7  // 铲子
	ItemAcquisitionState_ItemAcquisitionState_DragonEgg                 ItemAcquisitionState = 8  // 龙蛋
	ItemAcquisitionState_ItemAcquisitionState_MagicCloth                ItemAcquisitionState = 9  // 魔法布料
	ItemAcquisitionState_ItemAcquisitionState_Chisel                    ItemAcquisitionState = 10 // 凿子
	ItemAcquisitionState_ItemAcquisitionState_DragonCompoundSlotUpgrade ItemAcquisitionState = 11 // 龙底座升级材料
	ItemAcquisitionState_ItemAcquisitionState_SlimeRecurit              ItemAcquisitionState = 12 // 史莱姆招募道具
	ItemAcquisitionState_ItemAcquisitionState_DragonFactoryLevelItemRob ItemAcquisitionState = 13 // 悬空工厂升级道具
	ItemAcquisitionState_ItemAcquisitionState_DragonBuildResource       ItemAcquisitionState = 14 // 其他建筑升级道具
	ItemAcquisitionState_ItemAcquisitionState_DragonFactoryTech         ItemAcquisitionState = 15 // 科技点道具获取
	ItemAcquisitionState_ItemAcquisitionState_GuildFactoryItem          ItemAcquisitionState = 16 // 公会科技道具（远古晶石
	ItemAcquisitionState_ItemAcquisitionState_FashionBTSItem            ItemAcquisitionState = 17 // 技能突破道具获取
)

// Enum value maps for ItemAcquisitionState.
var (
	ItemAcquisitionState_name = map[int32]string{
		0:  "ItemAcquisitionState_NONE",
		1:  "ItemAcquisitionState_Diamond",
		2:  "ItemAcquisitionState_Star",
		3:  "ItemAcquisitionState_Essence",
		4:  "ItemAcquisitionState_AirPurifier",
		5:  "ItemAcquisitionState_UpgradeTicket",
		6:  "ItemAcquisitionState_DuplicateTicket",
		7:  "ItemAcquisitionState_Shovel",
		8:  "ItemAcquisitionState_DragonEgg",
		9:  "ItemAcquisitionState_MagicCloth",
		10: "ItemAcquisitionState_Chisel",
		11: "ItemAcquisitionState_DragonCompoundSlotUpgrade",
		12: "ItemAcquisitionState_SlimeRecurit",
		13: "ItemAcquisitionState_DragonFactoryLevelItemRob",
		14: "ItemAcquisitionState_DragonBuildResource",
		15: "ItemAcquisitionState_DragonFactoryTech",
		16: "ItemAcquisitionState_GuildFactoryItem",
		17: "ItemAcquisitionState_FashionBTSItem",
	}
	ItemAcquisitionState_value = map[string]int32{
		"ItemAcquisitionState_NONE":                      0,
		"ItemAcquisitionState_Diamond":                   1,
		"ItemAcquisitionState_Star":                      2,
		"ItemAcquisitionState_Essence":                   3,
		"ItemAcquisitionState_AirPurifier":               4,
		"ItemAcquisitionState_UpgradeTicket":             5,
		"ItemAcquisitionState_DuplicateTicket":           6,
		"ItemAcquisitionState_Shovel":                    7,
		"ItemAcquisitionState_DragonEgg":                 8,
		"ItemAcquisitionState_MagicCloth":                9,
		"ItemAcquisitionState_Chisel":                    10,
		"ItemAcquisitionState_DragonCompoundSlotUpgrade": 11,
		"ItemAcquisitionState_SlimeRecurit":              12,
		"ItemAcquisitionState_DragonFactoryLevelItemRob": 13,
		"ItemAcquisitionState_DragonBuildResource":       14,
		"ItemAcquisitionState_DragonFactoryTech":         15,
		"ItemAcquisitionState_GuildFactoryItem":          16,
		"ItemAcquisitionState_FashionBTSItem":            17,
	}
)

func (x ItemAcquisitionState) Enum() *ItemAcquisitionState {
	p := new(ItemAcquisitionState)
	*p = x
	return p
}

func (x ItemAcquisitionState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ItemAcquisitionState) Descriptor() protoreflect.EnumDescriptor {
	return file_PublicEnum_proto_enumTypes[42].Descriptor()
}

func (ItemAcquisitionState) Type() protoreflect.EnumType {
	return &file_PublicEnum_proto_enumTypes[42]
}

func (x ItemAcquisitionState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ItemAcquisitionState.Descriptor instead.
func (ItemAcquisitionState) EnumDescriptor() ([]byte, []int) {
	return file_PublicEnum_proto_rawDescGZIP(), []int{42}
}

//=====================视频广告的类型开始==================================
// 激励视频
type IronSourceADype int32

const (
	IronSourceADype_IronSourceADype_NONE    IronSourceADype = 0 // 0.表示不可用
	IronSourceADype_IronSourceADype_OffLine IronSourceADype = 1 // 挂机奖励X1
)

// Enum value maps for IronSourceADype.
var (
	IronSourceADype_name = map[int32]string{
		0: "IronSourceADype_NONE",
		1: "IronSourceADype_OffLine",
	}
	IronSourceADype_value = map[string]int32{
		"IronSourceADype_NONE":    0,
		"IronSourceADype_OffLine": 1,
	}
)

func (x IronSourceADype) Enum() *IronSourceADype {
	p := new(IronSourceADype)
	*p = x
	return p
}

func (x IronSourceADype) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (IronSourceADype) Descriptor() protoreflect.EnumDescriptor {
	return file_PublicEnum_proto_enumTypes[43].Descriptor()
}

func (IronSourceADype) Type() protoreflect.EnumType {
	return &file_PublicEnum_proto_enumTypes[43]
}

func (x IronSourceADype) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use IronSourceADype.Descriptor instead.
func (IronSourceADype) EnumDescriptor() ([]byte, []int) {
	return file_PublicEnum_proto_rawDescGZIP(), []int{43}
}

//=====================公会系统开始==================================
//公会成员身份，保持权限递增的顺序
type GuildPosition int32

const (
	GuildPosition_GuildPosition_Normal    GuildPosition = 0 //普通成员
	GuildPosition_GuildPosition_Elite     GuildPosition = 1 //精英成员
	GuildPosition_GuildPosition_Vice      GuildPosition = 2 //长老（副会长）
	GuildPosition_GuildPosition_President GuildPosition = 3 //会长
)

// Enum value maps for GuildPosition.
var (
	GuildPosition_name = map[int32]string{
		0: "GuildPosition_Normal",
		1: "GuildPosition_Elite",
		2: "GuildPosition_Vice",
		3: "GuildPosition_President",
	}
	GuildPosition_value = map[string]int32{
		"GuildPosition_Normal":    0,
		"GuildPosition_Elite":     1,
		"GuildPosition_Vice":      2,
		"GuildPosition_President": 3,
	}
)

func (x GuildPosition) Enum() *GuildPosition {
	p := new(GuildPosition)
	*p = x
	return p
}

func (x GuildPosition) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GuildPosition) Descriptor() protoreflect.EnumDescriptor {
	return file_PublicEnum_proto_enumTypes[44].Descriptor()
}

func (GuildPosition) Type() protoreflect.EnumType {
	return &file_PublicEnum_proto_enumTypes[44]
}

func (x GuildPosition) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GuildPosition.Descriptor instead.
func (GuildPosition) EnumDescriptor() ([]byte, []int) {
	return file_PublicEnum_proto_rawDescGZIP(), []int{44}
}

//公会操作（仅列出需要验证权限的操作）
type GuildOpt int32

const (
	GuildOpt_GuildOpt_None               GuildOpt = 0  // 0.无操作
	GuildOpt_GuildOpt_EditIcon           GuildOpt = 1  // 1.修改旗帜
	GuildOpt_GuildOpt_EditName           GuildOpt = 2  // 2.修改名字
	GuildOpt_GuildOpt_EditNotice         GuildOpt = 3  // 3.修改宣言
	GuildOpt_GuildOpt_EditApply          GuildOpt = 4  // 4.修改申请条件
	GuildOpt_GuildOpt_ApplyMgrList       GuildOpt = 5  // 5.申请列表查看
	GuildOpt_GuildOpt_ApplyMgrAgree      GuildOpt = 6  // 6.同意申请
	GuildOpt_GuildOpt_ApplyMgrRefuse     GuildOpt = 7  // 7.拒绝申请
	GuildOpt_GuildOpt_MemberMgrList      GuildOpt = 8  // 8.成员管理列表查看
	GuildOpt_GuildOpt_MemberMgrPresident GuildOpt = 9  // 9.任命会长
	GuildOpt_GuildOpt_MemberMgrVice      GuildOpt = 10 // 10.任命副会长
	GuildOpt_GuildOpt_MemberMgrElite     GuildOpt = 11 // 11.认命精英
	GuildOpt_GuildOpt_MemberMgrNormal    GuildOpt = 12 // 12.任命为普通成员
	GuildOpt_GuildOpt_MemberMgrKick      GuildOpt = 13 // 13.踢出公会
	GuildOpt_GuildOpt_Quit               GuildOpt = 14 // 14.退出公会
	GuildOpt_GuildOpt_Dismiss            GuildOpt = 15 // 15.解散公会
	GuildOpt_GuildOpt_Impeach            GuildOpt = 16 // 16.弹劾会长
	GuildOpt_GuildOpt_SendWorldInvite    GuildOpt = 17 // 17.发送世界邀请
	GuildOpt_GuildOpt_SendPlayerInvite   GuildOpt = 18 // 18.发送私聊邀请
	GuildOpt_GuildOpt_BargainingNotice   GuildOpt = 19 // 19.提醒砍价
	GuildOpt_GuildOpt_EditAnnouncement   GuildOpt = 20 // 20.修改公告
	GuildOpt_GuildOpt_ApplyMgrRejectAll  GuildOpt = 21 // 21.一键拒绝所有申请
)

// Enum value maps for GuildOpt.
var (
	GuildOpt_name = map[int32]string{
		0:  "GuildOpt_None",
		1:  "GuildOpt_EditIcon",
		2:  "GuildOpt_EditName",
		3:  "GuildOpt_EditNotice",
		4:  "GuildOpt_EditApply",
		5:  "GuildOpt_ApplyMgrList",
		6:  "GuildOpt_ApplyMgrAgree",
		7:  "GuildOpt_ApplyMgrRefuse",
		8:  "GuildOpt_MemberMgrList",
		9:  "GuildOpt_MemberMgrPresident",
		10: "GuildOpt_MemberMgrVice",
		11: "GuildOpt_MemberMgrElite",
		12: "GuildOpt_MemberMgrNormal",
		13: "GuildOpt_MemberMgrKick",
		14: "GuildOpt_Quit",
		15: "GuildOpt_Dismiss",
		16: "GuildOpt_Impeach",
		17: "GuildOpt_SendWorldInvite",
		18: "GuildOpt_SendPlayerInvite",
		19: "GuildOpt_BargainingNotice",
		20: "GuildOpt_EditAnnouncement",
		21: "GuildOpt_ApplyMgrRejectAll",
	}
	GuildOpt_value = map[string]int32{
		"GuildOpt_None":               0,
		"GuildOpt_EditIcon":           1,
		"GuildOpt_EditName":           2,
		"GuildOpt_EditNotice":         3,
		"GuildOpt_EditApply":          4,
		"GuildOpt_ApplyMgrList":       5,
		"GuildOpt_ApplyMgrAgree":      6,
		"GuildOpt_ApplyMgrRefuse":     7,
		"GuildOpt_MemberMgrList":      8,
		"GuildOpt_MemberMgrPresident": 9,
		"GuildOpt_MemberMgrVice":      10,
		"GuildOpt_MemberMgrElite":     11,
		"GuildOpt_MemberMgrNormal":    12,
		"GuildOpt_MemberMgrKick":      13,
		"GuildOpt_Quit":               14,
		"GuildOpt_Dismiss":            15,
		"GuildOpt_Impeach":            16,
		"GuildOpt_SendWorldInvite":    17,
		"GuildOpt_SendPlayerInvite":   18,
		"GuildOpt_BargainingNotice":   19,
		"GuildOpt_EditAnnouncement":   20,
		"GuildOpt_ApplyMgrRejectAll":  21,
	}
)

func (x GuildOpt) Enum() *GuildOpt {
	p := new(GuildOpt)
	*p = x
	return p
}

func (x GuildOpt) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GuildOpt) Descriptor() protoreflect.EnumDescriptor {
	return file_PublicEnum_proto_enumTypes[45].Descriptor()
}

func (GuildOpt) Type() protoreflect.EnumType {
	return &file_PublicEnum_proto_enumTypes[45]
}

func (x GuildOpt) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GuildOpt.Descriptor instead.
func (GuildOpt) EnumDescriptor() ([]byte, []int) {
	return file_PublicEnum_proto_rawDescGZIP(), []int{45}
}

//公会日志类型
type GuildLogType int32

const (
	GuildLogType_GuildLogType_Leave       GuildLogType = 0 //0.离开公会
	GuildLogType_GuildLogType_Join        GuildLogType = 1 //1.加入公会
	GuildLogType_GuildLogType_Impeach1    GuildLogType = 2 //2.弹劾成功
	GuildLogType_GuildLogType_Impeach2    GuildLogType = 3 //3.被弹劾
	GuildLogType_GuildLogType_MgrVice     GuildLogType = 4 //4.任命副会长
	GuildLogType_GuildLogType_MgrNormal   GuildLogType = 5 //5.任命为普通成员
	GuildLogType_GuildLogType_Donate      GuildLogType = 6 //6.捐献增加经验
	GuildLogType_GuildLogType_President   GuildLogType = 7 //7.成为会长
	GuildLogType_GuildLogType_AutoChange1 GuildLogType = 8 //8.转让成功为会长
	GuildLogType_GuildLogType_AutoChange2 GuildLogType = 9 //9.被转让出会长，降为成员
)

// Enum value maps for GuildLogType.
var (
	GuildLogType_name = map[int32]string{
		0: "GuildLogType_Leave",
		1: "GuildLogType_Join",
		2: "GuildLogType_Impeach1",
		3: "GuildLogType_Impeach2",
		4: "GuildLogType_MgrVice",
		5: "GuildLogType_MgrNormal",
		6: "GuildLogType_Donate",
		7: "GuildLogType_President",
		8: "GuildLogType_AutoChange1",
		9: "GuildLogType_AutoChange2",
	}
	GuildLogType_value = map[string]int32{
		"GuildLogType_Leave":       0,
		"GuildLogType_Join":        1,
		"GuildLogType_Impeach1":    2,
		"GuildLogType_Impeach2":    3,
		"GuildLogType_MgrVice":     4,
		"GuildLogType_MgrNormal":   5,
		"GuildLogType_Donate":      6,
		"GuildLogType_President":   7,
		"GuildLogType_AutoChange1": 8,
		"GuildLogType_AutoChange2": 9,
	}
)

func (x GuildLogType) Enum() *GuildLogType {
	p := new(GuildLogType)
	*p = x
	return p
}

func (x GuildLogType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GuildLogType) Descriptor() protoreflect.EnumDescriptor {
	return file_PublicEnum_proto_enumTypes[46].Descriptor()
}

func (GuildLogType) Type() protoreflect.EnumType {
	return &file_PublicEnum_proto_enumTypes[46]
}

func (x GuildLogType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GuildLogType.Descriptor instead.
func (GuildLogType) EnumDescriptor() ([]byte, []int) {
	return file_PublicEnum_proto_rawDescGZIP(), []int{46}
}

// 公会通知
// 用于 G2PGuildGeneralUpdateNtf，告知 Player Actor 其公会相关状态需要更新的具体类型
type GuildUpdateType int32

const (
	GuildUpdateType_GuildUpdateType_UNKNOWN GuildUpdateType = 0 // 未知或默认类型，不应实际使用
	// --- 玩家与公会的从属关系变化 ---
	GuildUpdateType_GuildUpdateType_JOINED_GUILD      GuildUpdateType = 1 // 玩家成功加入了一个新的公会 场景: 申请被批准后；快速加入成功后。
	GuildUpdateType_GuildUpdateType_LEFT_GUILD        GuildUpdateType = 2 // 玩家主动退出了当前公会 场景: 玩家执行退出公会操作成功后。
	GuildUpdateType_GuildUpdateType_KICKED_FROM_GUILD GuildUpdateType = 3 // 玩家被从当前公会踢出 场景: 官员执行踢人操作，目标是此玩家。
	GuildUpdateType_GuildUpdateType_GUILD_DISMISSED   GuildUpdateType = 4 // 玩家所在的公会被解散 (无论是会长主动还是系统自动) 场景: 玩家是某公会成员，该公会被解散。
	// --- 玩家在公会内的状态变化 ---
	GuildUpdateType_GuildUpdateType_POSITION_CHANGED GuildUpdateType = 5 // 玩家在当前公会的职位发生了变化 场景: 玩家被任命为新的职位 (会长、副会长、精英、普通成员)。
	// --- 公会本身的状态变化 (通知成员) ---
	GuildUpdateType_GuildUpdateType_GUILD_LEVEL_UP GuildUpdateType = 6 // 玩家所在的公会等级提升 场景: 公会经验达到升级条件，或GM指令提升等级。
	// --- 玩家申请列表状态变化 ---
	GuildUpdateType_GuildUpdateType_APPLY_PROCESSED_OR_EXPIRED GuildUpdateType = 7 // 玩家之前对某个公会的申请已被处理(同意/拒绝)或已过期 场景: 官员处理了玩家的申请；或玩家的申请记录因超时被系统清理。
)

// Enum value maps for GuildUpdateType.
var (
	GuildUpdateType_name = map[int32]string{
		0: "GuildUpdateType_UNKNOWN",
		1: "GuildUpdateType_JOINED_GUILD",
		2: "GuildUpdateType_LEFT_GUILD",
		3: "GuildUpdateType_KICKED_FROM_GUILD",
		4: "GuildUpdateType_GUILD_DISMISSED",
		5: "GuildUpdateType_POSITION_CHANGED",
		6: "GuildUpdateType_GUILD_LEVEL_UP",
		7: "GuildUpdateType_APPLY_PROCESSED_OR_EXPIRED",
	}
	GuildUpdateType_value = map[string]int32{
		"GuildUpdateType_UNKNOWN":                    0,
		"GuildUpdateType_JOINED_GUILD":               1,
		"GuildUpdateType_LEFT_GUILD":                 2,
		"GuildUpdateType_KICKED_FROM_GUILD":          3,
		"GuildUpdateType_GUILD_DISMISSED":            4,
		"GuildUpdateType_POSITION_CHANGED":           5,
		"GuildUpdateType_GUILD_LEVEL_UP":             6,
		"GuildUpdateType_APPLY_PROCESSED_OR_EXPIRED": 7,
	}
)

func (x GuildUpdateType) Enum() *GuildUpdateType {
	p := new(GuildUpdateType)
	*p = x
	return p
}

func (x GuildUpdateType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GuildUpdateType) Descriptor() protoreflect.EnumDescriptor {
	return file_PublicEnum_proto_enumTypes[47].Descriptor()
}

func (GuildUpdateType) Type() protoreflect.EnumType {
	return &file_PublicEnum_proto_enumTypes[47]
}

func (x GuildUpdateType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GuildUpdateType.Descriptor instead.
func (GuildUpdateType) EnumDescriptor() ([]byte, []int) {
	return file_PublicEnum_proto_rawDescGZIP(), []int{47}
}

// 公会申请记录的状态
type GuildApplicationStatus int32

const (
	GuildApplicationStatus_APPLICATION_STATUS_UNKNOWN  GuildApplicationStatus = 0 // 未知或无效状态。
	GuildApplicationStatus_APPLICATION_STATUS_PENDING  GuildApplicationStatus = 1 // 申请已提交，等待联盟官员审批。
	GuildApplicationStatus_APPLICATION_STATUS_APPROVED GuildApplicationStatus = 2 // 申请已被联盟官员批准。
	GuildApplicationStatus_APPLICATION_STATUS_REJECTED GuildApplicationStatus = 3 // 申请已被联盟官员拒绝。
	GuildApplicationStatus_APPLICATION_STATUS_EXPIRED  GuildApplicationStatus = 4 // 申请因超时（例如24小时未被处理）而自动失效。玩家未能加入联盟。
)

// Enum value maps for GuildApplicationStatus.
var (
	GuildApplicationStatus_name = map[int32]string{
		0: "APPLICATION_STATUS_UNKNOWN",
		1: "APPLICATION_STATUS_PENDING",
		2: "APPLICATION_STATUS_APPROVED",
		3: "APPLICATION_STATUS_REJECTED",
		4: "APPLICATION_STATUS_EXPIRED",
	}
	GuildApplicationStatus_value = map[string]int32{
		"APPLICATION_STATUS_UNKNOWN":  0,
		"APPLICATION_STATUS_PENDING":  1,
		"APPLICATION_STATUS_APPROVED": 2,
		"APPLICATION_STATUS_REJECTED": 3,
		"APPLICATION_STATUS_EXPIRED":  4,
	}
)

func (x GuildApplicationStatus) Enum() *GuildApplicationStatus {
	p := new(GuildApplicationStatus)
	*p = x
	return p
}

func (x GuildApplicationStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GuildApplicationStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_PublicEnum_proto_enumTypes[48].Descriptor()
}

func (GuildApplicationStatus) Type() protoreflect.EnumType {
	return &file_PublicEnum_proto_enumTypes[48]
}

func (x GuildApplicationStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GuildApplicationStatus.Descriptor instead.
func (GuildApplicationStatus) EnumDescriptor() ([]byte, []int) {
	return file_PublicEnum_proto_rawDescGZIP(), []int{48}
}

// 公会内部通知类型
type GuildSystemInternalActionType int32

const (
	GuildSystemInternalActionType_INTERNAL_ACTION_UNKNOWN                       GuildSystemInternalActionType = 0
	GuildSystemInternalActionType_INTERNAL_ACTION_DISMISS_GUILD_MEMBERS         GuildSystemInternalActionType = 1 // 公会解散，通知成员
	GuildSystemInternalActionType_INTERNAL_ACTION_GUILD_LEVEL_UP_NOTIFY_MEMBERS GuildSystemInternalActionType = 2 // 联盟升级，通知成员
)

// Enum value maps for GuildSystemInternalActionType.
var (
	GuildSystemInternalActionType_name = map[int32]string{
		0: "INTERNAL_ACTION_UNKNOWN",
		1: "INTERNAL_ACTION_DISMISS_GUILD_MEMBERS",
		2: "INTERNAL_ACTION_GUILD_LEVEL_UP_NOTIFY_MEMBERS",
	}
	GuildSystemInternalActionType_value = map[string]int32{
		"INTERNAL_ACTION_UNKNOWN":                       0,
		"INTERNAL_ACTION_DISMISS_GUILD_MEMBERS":         1,
		"INTERNAL_ACTION_GUILD_LEVEL_UP_NOTIFY_MEMBERS": 2,
	}
)

func (x GuildSystemInternalActionType) Enum() *GuildSystemInternalActionType {
	p := new(GuildSystemInternalActionType)
	*p = x
	return p
}

func (x GuildSystemInternalActionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GuildSystemInternalActionType) Descriptor() protoreflect.EnumDescriptor {
	return file_PublicEnum_proto_enumTypes[49].Descriptor()
}

func (GuildSystemInternalActionType) Type() protoreflect.EnumType {
	return &file_PublicEnum_proto_enumTypes[49]
}

func (x GuildSystemInternalActionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GuildSystemInternalActionType.Descriptor instead.
func (GuildSystemInternalActionType) EnumDescriptor() ([]byte, []int) {
	return file_PublicEnum_proto_rawDescGZIP(), []int{49}
}

//=====================公会系统结束===================================
//=====================聊天系统结束===================================
//聊天类型
type ChatType int32

const (
	ChatType_ChatType_World   ChatType = 0
	ChatType_ChatType_Guild   ChatType = 1
	ChatType_ChatType_Private ChatType = 2
)

// Enum value maps for ChatType.
var (
	ChatType_name = map[int32]string{
		0: "ChatType_World",
		1: "ChatType_Guild",
		2: "ChatType_Private",
	}
	ChatType_value = map[string]int32{
		"ChatType_World":   0,
		"ChatType_Guild":   1,
		"ChatType_Private": 2,
	}
)

func (x ChatType) Enum() *ChatType {
	p := new(ChatType)
	*p = x
	return p
}

func (x ChatType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ChatType) Descriptor() protoreflect.EnumDescriptor {
	return file_PublicEnum_proto_enumTypes[50].Descriptor()
}

func (ChatType) Type() protoreflect.EnumType {
	return &file_PublicEnum_proto_enumTypes[50]
}

func (x ChatType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ChatType.Descriptor instead.
func (ChatType) EnumDescriptor() ([]byte, []int) {
	return file_PublicEnum_proto_rawDescGZIP(), []int{50}
}

//聊天信息类型
type ChatMsgType int32

const (
	ChatMsgType_ChatMsgType_Text        ChatMsgType = 0 //普通文本
	ChatMsgType_ChatMsgType_GuildInvite ChatMsgType = 1 //公会邀请信息
	ChatMsgType_ChatMsgType_System      ChatMsgType = 2 //系统信息
)

// Enum value maps for ChatMsgType.
var (
	ChatMsgType_name = map[int32]string{
		0: "ChatMsgType_Text",
		1: "ChatMsgType_GuildInvite",
		2: "ChatMsgType_System",
	}
	ChatMsgType_value = map[string]int32{
		"ChatMsgType_Text":        0,
		"ChatMsgType_GuildInvite": 1,
		"ChatMsgType_System":      2,
	}
)

func (x ChatMsgType) Enum() *ChatMsgType {
	p := new(ChatMsgType)
	*p = x
	return p
}

func (x ChatMsgType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ChatMsgType) Descriptor() protoreflect.EnumDescriptor {
	return file_PublicEnum_proto_enumTypes[51].Descriptor()
}

func (ChatMsgType) Type() protoreflect.EnumType {
	return &file_PublicEnum_proto_enumTypes[51]
}

func (x ChatMsgType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ChatMsgType.Descriptor instead.
func (ChatMsgType) EnumDescriptor() ([]byte, []int) {
	return file_PublicEnum_proto_rawDescGZIP(), []int{51}
}

//战斗内触发逻辑打开窗口类型
type InBattleShowWindowType int32

const (
	InBattleShowWindowType_UI_Select_NormalPop InBattleShowWindowType = 0 //三选一buff
	InBattleShowWindowType_UI_Select_ShopPop   InBattleShowWindowType = 1 //商店购买buff
	InBattleShowWindowType_UI_Select_LuckyPop  InBattleShowWindowType = 2 //击杀boss或精英怪物掉落buff
)

// Enum value maps for InBattleShowWindowType.
var (
	InBattleShowWindowType_name = map[int32]string{
		0: "UI_Select_NormalPop",
		1: "UI_Select_ShopPop",
		2: "UI_Select_LuckyPop",
	}
	InBattleShowWindowType_value = map[string]int32{
		"UI_Select_NormalPop": 0,
		"UI_Select_ShopPop":   1,
		"UI_Select_LuckyPop":  2,
	}
)

func (x InBattleShowWindowType) Enum() *InBattleShowWindowType {
	p := new(InBattleShowWindowType)
	*p = x
	return p
}

func (x InBattleShowWindowType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (InBattleShowWindowType) Descriptor() protoreflect.EnumDescriptor {
	return file_PublicEnum_proto_enumTypes[52].Descriptor()
}

func (InBattleShowWindowType) Type() protoreflect.EnumType {
	return &file_PublicEnum_proto_enumTypes[52]
}

func (x InBattleShowWindowType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use InBattleShowWindowType.Descriptor instead.
func (InBattleShowWindowType) EnumDescriptor() ([]byte, []int) {
	return file_PublicEnum_proto_rawDescGZIP(), []int{52}
}

//全局广播事件类型
type GlobalBroadcastEventType int32

const (
	GlobalBroadcastEventType_GlobalBroadcastEventType_None                   GlobalBroadcastEventType = 0 //默认无处理
	GlobalBroadcastEventType_GlobalBroadcastEventType_BanPlayerGuildBossRank GlobalBroadcastEventType = 1 //全局封禁玩家所在公会的BOSS排行信息，伤害置为0
	GlobalBroadcastEventType_GlobalBroadcastEventType_ChgPlayerName          GlobalBroadcastEventType = 2 //强制修改玩家名称
	GlobalBroadcastEventType_GlobalBroadcastEventType_ChgGuildNameAndNotice  GlobalBroadcastEventType = 3 //强制修改公会名称和公告信息
)

// Enum value maps for GlobalBroadcastEventType.
var (
	GlobalBroadcastEventType_name = map[int32]string{
		0: "GlobalBroadcastEventType_None",
		1: "GlobalBroadcastEventType_BanPlayerGuildBossRank",
		2: "GlobalBroadcastEventType_ChgPlayerName",
		3: "GlobalBroadcastEventType_ChgGuildNameAndNotice",
	}
	GlobalBroadcastEventType_value = map[string]int32{
		"GlobalBroadcastEventType_None":                   0,
		"GlobalBroadcastEventType_BanPlayerGuildBossRank": 1,
		"GlobalBroadcastEventType_ChgPlayerName":          2,
		"GlobalBroadcastEventType_ChgGuildNameAndNotice":  3,
	}
)

func (x GlobalBroadcastEventType) Enum() *GlobalBroadcastEventType {
	p := new(GlobalBroadcastEventType)
	*p = x
	return p
}

func (x GlobalBroadcastEventType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GlobalBroadcastEventType) Descriptor() protoreflect.EnumDescriptor {
	return file_PublicEnum_proto_enumTypes[53].Descriptor()
}

func (GlobalBroadcastEventType) Type() protoreflect.EnumType {
	return &file_PublicEnum_proto_enumTypes[53]
}

func (x GlobalBroadcastEventType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GlobalBroadcastEventType.Descriptor instead.
func (GlobalBroadcastEventType) EnumDescriptor() ([]byte, []int) {
	return file_PublicEnum_proto_rawDescGZIP(), []int{53}
}

//=====================七日签到开始===================================
type SignInState int32

const (
	SignInState_NoSignIn        SignInState = 0 //未签到
	SignInState_SignedInNoRe    SignInState = 1 //已签到未领取
	SignInState_SignedInAndReed SignInState = 2 //已签到已领取
)

// Enum value maps for SignInState.
var (
	SignInState_name = map[int32]string{
		0: "NoSignIn",
		1: "SignedInNoRe",
		2: "SignedInAndReed",
	}
	SignInState_value = map[string]int32{
		"NoSignIn":        0,
		"SignedInNoRe":    1,
		"SignedInAndReed": 2,
	}
)

func (x SignInState) Enum() *SignInState {
	p := new(SignInState)
	*p = x
	return p
}

func (x SignInState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SignInState) Descriptor() protoreflect.EnumDescriptor {
	return file_PublicEnum_proto_enumTypes[54].Descriptor()
}

func (SignInState) Type() protoreflect.EnumType {
	return &file_PublicEnum_proto_enumTypes[54]
}

func (x SignInState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SignInState.Descriptor instead.
func (SignInState) EnumDescriptor() ([]byte, []int) {
	return file_PublicEnum_proto_rawDescGZIP(), []int{54}
}

//=====================七日签到结束===================================
//======================支付开始=========================================
type PaymentModuleType int32

const (
	PaymentModuleType_Shop           PaymentModuleType = 0 // 商店
	PaymentModuleType_FirstCharge    PaymentModuleType = 1 // 首充
	PaymentModuleType_MonthlyCard    PaymentModuleType = 2 // 月卡
	PaymentModuleType_GradedFund     PaymentModuleType = 3 // 基金
	PaymentModuleType_MonthlyCardNew PaymentModuleType = 4 // 月卡2.0
	PaymentModuleType_TimeShop       PaymentModuleType = 5 // 限时商店(日、周、月礼包)
)

// Enum value maps for PaymentModuleType.
var (
	PaymentModuleType_name = map[int32]string{
		0: "Shop",
		1: "FirstCharge",
		2: "MonthlyCard",
		3: "GradedFund",
		4: "MonthlyCardNew",
		5: "TimeShop",
	}
	PaymentModuleType_value = map[string]int32{
		"Shop":           0,
		"FirstCharge":    1,
		"MonthlyCard":    2,
		"GradedFund":     3,
		"MonthlyCardNew": 4,
		"TimeShop":       5,
	}
)

func (x PaymentModuleType) Enum() *PaymentModuleType {
	p := new(PaymentModuleType)
	*p = x
	return p
}

func (x PaymentModuleType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PaymentModuleType) Descriptor() protoreflect.EnumDescriptor {
	return file_PublicEnum_proto_enumTypes[55].Descriptor()
}

func (PaymentModuleType) Type() protoreflect.EnumType {
	return &file_PublicEnum_proto_enumTypes[55]
}

func (x PaymentModuleType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PaymentModuleType.Descriptor instead.
func (PaymentModuleType) EnumDescriptor() ([]byte, []int) {
	return file_PublicEnum_proto_rawDescGZIP(), []int{55}
}

//======================任务开始=========================================
//任务状态
type MissionState int32

const (
	MissionState_MissionState_Unfinished MissionState = 0 //未完成
	MissionState_MissionState_Finished   MissionState = 1 //已完成
	MissionState_MissionState_Received   MissionState = 2 //已领取
)

// Enum value maps for MissionState.
var (
	MissionState_name = map[int32]string{
		0: "MissionState_Unfinished",
		1: "MissionState_Finished",
		2: "MissionState_Received",
	}
	MissionState_value = map[string]int32{
		"MissionState_Unfinished": 0,
		"MissionState_Finished":   1,
		"MissionState_Received":   2,
	}
)

func (x MissionState) Enum() *MissionState {
	p := new(MissionState)
	*p = x
	return p
}

func (x MissionState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MissionState) Descriptor() protoreflect.EnumDescriptor {
	return file_PublicEnum_proto_enumTypes[56].Descriptor()
}

func (MissionState) Type() protoreflect.EnumType {
	return &file_PublicEnum_proto_enumTypes[56]
}

func (x MissionState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MissionState.Descriptor instead.
func (MissionState) EnumDescriptor() ([]byte, []int) {
	return file_PublicEnum_proto_rawDescGZIP(), []int{56}
}

//任务类型
type MissionType int32

const (
	MissionType_MissionType_Main        MissionType = 0 //主线任务
	MissionType_MissionType_Daily       MissionType = 1 //日常任务
	MissionType_MissionType_SevenDay    MissionType = 2 //七日任务
	MissionType_MissionType_Achievement MissionType = 3 //成就任务
	MissionType_MissionType_HeavenlyDao MissionType = 4 //天道修为
)

// Enum value maps for MissionType.
var (
	MissionType_name = map[int32]string{
		0: "MissionType_Main",
		1: "MissionType_Daily",
		2: "MissionType_SevenDay",
		3: "MissionType_Achievement",
		4: "MissionType_HeavenlyDao",
	}
	MissionType_value = map[string]int32{
		"MissionType_Main":        0,
		"MissionType_Daily":       1,
		"MissionType_SevenDay":    2,
		"MissionType_Achievement": 3,
		"MissionType_HeavenlyDao": 4,
	}
)

func (x MissionType) Enum() *MissionType {
	p := new(MissionType)
	*p = x
	return p
}

func (x MissionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MissionType) Descriptor() protoreflect.EnumDescriptor {
	return file_PublicEnum_proto_enumTypes[57].Descriptor()
}

func (MissionType) Type() protoreflect.EnumType {
	return &file_PublicEnum_proto_enumTypes[57]
}

func (x MissionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MissionType.Descriptor instead.
func (MissionType) EnumDescriptor() ([]byte, []int) {
	return file_PublicEnum_proto_rawDescGZIP(), []int{57}
}

//=====================千抽开始===================================
//千抽状态
type DBGachaBonusState int32

const (
	DBGachaBonusState_NoPull        DBGachaBonusState = 0 //未抽卡
	DBGachaBonusState_PulledNoRe    DBGachaBonusState = 1 //已抽未领取
	DBGachaBonusState_PulledAndReed DBGachaBonusState = 2 //已抽已领取
)

// Enum value maps for DBGachaBonusState.
var (
	DBGachaBonusState_name = map[int32]string{
		0: "NoPull",
		1: "PulledNoRe",
		2: "PulledAndReed",
	}
	DBGachaBonusState_value = map[string]int32{
		"NoPull":        0,
		"PulledNoRe":    1,
		"PulledAndReed": 2,
	}
)

func (x DBGachaBonusState) Enum() *DBGachaBonusState {
	p := new(DBGachaBonusState)
	*p = x
	return p
}

func (x DBGachaBonusState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DBGachaBonusState) Descriptor() protoreflect.EnumDescriptor {
	return file_PublicEnum_proto_enumTypes[58].Descriptor()
}

func (DBGachaBonusState) Type() protoreflect.EnumType {
	return &file_PublicEnum_proto_enumTypes[58]
}

func (x DBGachaBonusState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DBGachaBonusState.Descriptor instead.
func (DBGachaBonusState) EnumDescriptor() ([]byte, []int) {
	return file_PublicEnum_proto_rawDescGZIP(), []int{58}
}

// 错误码
type ServerResultCode int32

const (
	ServerResultCode_ResultCode_SUCCES              ServerResultCode = 0 // 成功
	ServerResultCode_ResultCode_Error               ServerResultCode = 1 //  服务端报错
	ServerResultCode_ResultCode_Currency_Not_Enough ServerResultCode = 2 // 金币不足
	ServerResultCode_ResultCode_Item_Not_Enough     ServerResultCode = 3 // 道具不足
	ServerResultCode_ResultCode_PARAM_ERROR         ServerResultCode = 4 // 客户端参数错误
	ServerResultCode_ResultCode_CONFIG_NOT_CONTAINS ServerResultCode = 5 // 配置不存在错误
)

// Enum value maps for ServerResultCode.
var (
	ServerResultCode_name = map[int32]string{
		0: "ResultCode_SUCCES",
		1: "ResultCode_Error",
		2: "ResultCode_Currency_Not_Enough",
		3: "ResultCode_Item_Not_Enough",
		4: "ResultCode_PARAM_ERROR",
		5: "ResultCode_CONFIG_NOT_CONTAINS",
	}
	ServerResultCode_value = map[string]int32{
		"ResultCode_SUCCES":              0,
		"ResultCode_Error":               1,
		"ResultCode_Currency_Not_Enough": 2,
		"ResultCode_Item_Not_Enough":     3,
		"ResultCode_PARAM_ERROR":         4,
		"ResultCode_CONFIG_NOT_CONTAINS": 5,
	}
)

func (x ServerResultCode) Enum() *ServerResultCode {
	p := new(ServerResultCode)
	*p = x
	return p
}

func (x ServerResultCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ServerResultCode) Descriptor() protoreflect.EnumDescriptor {
	return file_PublicEnum_proto_enumTypes[59].Descriptor()
}

func (ServerResultCode) Type() protoreflect.EnumType {
	return &file_PublicEnum_proto_enumTypes[59]
}

func (x ServerResultCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ServerResultCode.Descriptor instead.
func (ServerResultCode) EnumDescriptor() ([]byte, []int) {
	return file_PublicEnum_proto_rawDescGZIP(), []int{59}
}

//=======================功能预告奖励================================
type FuncPreviewstate int32

const (
	FuncPreviewstate_Lock    FuncPreviewstate = 0 // 未解锁
	FuncPreviewstate_Award   FuncPreviewstate = 1 // 可领奖
	FuncPreviewstate_Awarded FuncPreviewstate = 2 //已领奖
)

// Enum value maps for FuncPreviewstate.
var (
	FuncPreviewstate_name = map[int32]string{
		0: "Lock",
		1: "Award",
		2: "Awarded",
	}
	FuncPreviewstate_value = map[string]int32{
		"Lock":    0,
		"Award":   1,
		"Awarded": 2,
	}
)

func (x FuncPreviewstate) Enum() *FuncPreviewstate {
	p := new(FuncPreviewstate)
	*p = x
	return p
}

func (x FuncPreviewstate) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FuncPreviewstate) Descriptor() protoreflect.EnumDescriptor {
	return file_PublicEnum_proto_enumTypes[60].Descriptor()
}

func (FuncPreviewstate) Type() protoreflect.EnumType {
	return &file_PublicEnum_proto_enumTypes[60]
}

func (x FuncPreviewstate) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FuncPreviewstate.Descriptor instead.
func (FuncPreviewstate) EnumDescriptor() ([]byte, []int) {
	return file_PublicEnum_proto_rawDescGZIP(), []int{60}
}

//=================== 宝物系统开始 ========================
// 宝物抽卡的消耗类型
type TreasureGachaCostType int32

const (
	TreasureGachaCostType_COST_TYPE_NONE TreasureGachaCostType = 0
	TreasureGachaCostType_COST_TYPE_ITEM TreasureGachaCostType = 1 // 消耗道具 (例如: 宝物抽卡券)
	TreasureGachaCostType_COST_TYPE_AD   TreasureGachaCostType = 2 // 观看广告
)

// Enum value maps for TreasureGachaCostType.
var (
	TreasureGachaCostType_name = map[int32]string{
		0: "COST_TYPE_NONE",
		1: "COST_TYPE_ITEM",
		2: "COST_TYPE_AD",
	}
	TreasureGachaCostType_value = map[string]int32{
		"COST_TYPE_NONE": 0,
		"COST_TYPE_ITEM": 1,
		"COST_TYPE_AD":   2,
	}
)

func (x TreasureGachaCostType) Enum() *TreasureGachaCostType {
	p := new(TreasureGachaCostType)
	*p = x
	return p
}

func (x TreasureGachaCostType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TreasureGachaCostType) Descriptor() protoreflect.EnumDescriptor {
	return file_PublicEnum_proto_enumTypes[61].Descriptor()
}

func (TreasureGachaCostType) Type() protoreflect.EnumType {
	return &file_PublicEnum_proto_enumTypes[61]
}

func (x TreasureGachaCostType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TreasureGachaCostType.Descriptor instead.
func (TreasureGachaCostType) EnumDescriptor() ([]byte, []int) {
	return file_PublicEnum_proto_rawDescGZIP(), []int{61}
}

var File_PublicEnum_proto protoreflect.FileDescriptor

var file_PublicEnum_proto_rawDesc = []byte{
	0x0a, 0x10, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2a, 0x64, 0x0a, 0x0b, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x42, 0x79, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x15, 0x0a, 0x11, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x42, 0x79, 0x54, 0x79, 0x70, 0x65,
	0x5f, 0x4f, 0x74, 0x68, 0x65, 0x72, 0x10, 0x00, 0x12, 0x1c, 0x0a, 0x18, 0x4c, 0x6f, 0x67, 0x69,
	0x6e, 0x42, 0x79, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x51, 0x51, 0x47, 0x61, 0x6d, 0x65, 0x43, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x10, 0x01, 0x12, 0x20, 0x0a, 0x1c, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x42,
	0x79, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x57, 0x65, 0x43, 0x68, 0x61, 0x74, 0x47, 0x61, 0x6d, 0x65,
	0x43, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x10, 0x02, 0x2a, 0x72, 0x0a, 0x09, 0x4d, 0x6f, 0x6e, 0x65,
	0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x0e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x54, 0x79,
	0x70, 0x65, 0x5f, 0x4e, 0x6f, 0x6e, 0x65, 0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e, 0x4d, 0x6f, 0x6e,
	0x65, 0x79, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x47, 0x6f, 0x6c, 0x64, 0x10, 0x01, 0x12, 0x15, 0x0a,
	0x11, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x44, 0x69, 0x61, 0x6d, 0x6f,
	0x6e, 0x64, 0x10, 0x02, 0x12, 0x13, 0x0a, 0x0f, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x54, 0x79, 0x70,
	0x65, 0x5f, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x10, 0x03, 0x12, 0x11, 0x0a, 0x0d, 0x4d, 0x6f, 0x6e,
	0x65, 0x79, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x45, 0x78, 0x70, 0x10, 0x04, 0x2a, 0x89, 0x01, 0x0a,
	0x0d, 0x41, 0x77, 0x61, 0x72, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x17,
	0x0a, 0x13, 0x41, 0x77, 0x61, 0x72, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x5f,
	0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x10, 0x00, 0x12, 0x16, 0x0a, 0x12, 0x41, 0x77, 0x61, 0x72, 0x64,
	0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x49, 0x74, 0x65, 0x6d, 0x10, 0x01, 0x12,
	0x17, 0x0a, 0x13, 0x41, 0x77, 0x61, 0x72, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65,
	0x5f, 0x45, 0x71, 0x75, 0x69, 0x70, 0x10, 0x02, 0x12, 0x17, 0x0a, 0x13, 0x41, 0x77, 0x61, 0x72,
	0x64, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x10,
	0x03, 0x12, 0x15, 0x0a, 0x11, 0x41, 0x77, 0x61, 0x72, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79,
	0x70, 0x65, 0x5f, 0x47, 0x65, 0x6d, 0x10, 0x04, 0x2a, 0x56, 0x0a, 0x0c, 0x46, 0x75, 0x6e, 0x63,
	0x4c, 0x6f, 0x63, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x15, 0x0a, 0x11, 0x46, 0x75, 0x6e, 0x63,
	0x4c, 0x6f, 0x63, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x4c, 0x6f, 0x63, 0x6b, 0x10, 0x00, 0x12,
	0x17, 0x0a, 0x13, 0x46, 0x75, 0x6e, 0x63, 0x4c, 0x6f, 0x63, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x5f,
	0x55, 0x6e, 0x4c, 0x6f, 0x63, 0x6b, 0x10, 0x01, 0x12, 0x16, 0x0a, 0x12, 0x46, 0x75, 0x6e, 0x63,
	0x4c, 0x6f, 0x63, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x10, 0x02,
	0x2a, 0x43, 0x0a, 0x0d, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x44, 0x72, 0x6f, 0x70, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x19, 0x0a, 0x15, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x44, 0x72, 0x6f, 0x70, 0x54, 0x79,
	0x70, 0x65, 0x5f, 0x4d, 0x6f, 0x6e, 0x73, 0x74, 0x65, 0x72, 0x10, 0x00, 0x12, 0x17, 0x0a, 0x13,
	0x53, 0x63, 0x65, 0x6e, 0x65, 0x44, 0x72, 0x6f, 0x70, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x48, 0x6f,
	0x72, 0x73, 0x65, 0x10, 0x01, 0x2a, 0xe1, 0x01, 0x0a, 0x0a, 0x52, 0x65, 0x64, 0x44, 0x6f, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x13, 0x0a, 0x0f, 0x52, 0x65, 0x64, 0x44, 0x6f, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x5f, 0x4e, 0x6f, 0x6e, 0x65, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x52, 0x65, 0x64,
	0x44, 0x6f, 0x74, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x10, 0x01, 0x12,
	0x13, 0x0a, 0x0f, 0x52, 0x65, 0x64, 0x44, 0x6f, 0x74, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x4d, 0x61,
	0x69, 0x6c, 0x10, 0x02, 0x12, 0x15, 0x0a, 0x11, 0x52, 0x65, 0x64, 0x44, 0x6f, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x5f, 0x46, 0x72, 0x69, 0x65, 0x6e, 0x64, 0x10, 0x03, 0x12, 0x1a, 0x0a, 0x16, 0x52,
	0x65, 0x64, 0x44, 0x6f, 0x74, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x46, 0x75, 0x6e, 0x63, 0x50, 0x72,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x10, 0x04, 0x12, 0x1a, 0x0a, 0x16, 0x52, 0x65, 0x64, 0x44, 0x6f,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x48, 0x65, 0x61, 0x76, 0x65, 0x6e, 0x6c, 0x79, 0x44, 0x61,
	0x6f, 0x10, 0x05, 0x12, 0x1c, 0x0a, 0x18, 0x52, 0x65, 0x64, 0x44, 0x6f, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x5f, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x50, 0x6c, 0x75, 0x73, 0x47, 0x69, 0x66, 0x74, 0x10,
	0x06, 0x12, 0x12, 0x0a, 0x0e, 0x52, 0x65, 0x64, 0x44, 0x6f, 0x74, 0x54, 0x79, 0x70, 0x65, 0x5f,
	0x42, 0x61, 0x67, 0x10, 0x07, 0x12, 0x12, 0x0a, 0x0e, 0x52, 0x65, 0x64, 0x44, 0x6f, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x5f, 0x4d, 0x41, 0x58, 0x10, 0x08, 0x2a, 0x2e, 0x0a, 0x0b, 0x45, 0x47, 0x65,
	0x6e, 0x64, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x44, 0x65, 0x66, 0x61,
	0x75, 0x6c, 0x74, 0x10, 0x00, 0x12, 0x07, 0x0a, 0x03, 0x4d, 0x61, 0x6e, 0x10, 0x01, 0x12, 0x09,
	0x0a, 0x05, 0x57, 0x6f, 0x6d, 0x61, 0x6e, 0x10, 0x02, 0x2a, 0x84, 0x0d, 0x0a, 0x0d, 0x41, 0x74,
	0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x12, 0x41,
	0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x4e, 0x4f, 0x4e,
	0x45, 0x10, 0x00, 0x12, 0x18, 0x0a, 0x14, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x5f, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x10, 0x01, 0x12, 0x14, 0x0a,
	0x10, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x48,
	0x70, 0x10, 0x02, 0x12, 0x15, 0x0a, 0x11, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x5f, 0x44, 0x65, 0x66, 0x10, 0x03, 0x12, 0x1b, 0x0a, 0x17, 0x41, 0x74,
	0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x4d, 0x6f, 0x76, 0x65,
	0x53, 0x70, 0x65, 0x65, 0x64, 0x10, 0x04, 0x12, 0x18, 0x0a, 0x14, 0x41, 0x74, 0x74, 0x72, 0x69,
	0x62, 0x75, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x47, 0x75, 0x6e, 0x41, 0x74, 0x6b, 0x10,
	0x05, 0x12, 0x1c, 0x0a, 0x18, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x5f, 0x50, 0x68, 0x79, 0x73, 0x69, 0x63, 0x73, 0x41, 0x74, 0x6b, 0x10, 0x06, 0x12,
	0x18, 0x0a, 0x14, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x5f, 0x49, 0x63, 0x65, 0x41, 0x74, 0x6b, 0x10, 0x07, 0x12, 0x20, 0x0a, 0x1c, 0x41, 0x74, 0x74,
	0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x45, 0x6c, 0x65, 0x63, 0x74,
	0x72, 0x69, 0x63, 0x69, 0x74, 0x79, 0x41, 0x74, 0x6b, 0x10, 0x08, 0x12, 0x19, 0x0a, 0x15, 0x41,
	0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x46, 0x69, 0x72,
	0x65, 0x41, 0x74, 0x6b, 0x10, 0x09, 0x12, 0x1b, 0x0a, 0x17, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62,
	0x75, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x45, 0x6e, 0x65, 0x72, 0x67, 0x79, 0x41, 0x74,
	0x6b, 0x10, 0x0a, 0x12, 0x19, 0x0a, 0x15, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x5f, 0x57, 0x69, 0x6e, 0x64, 0x41, 0x74, 0x6b, 0x10, 0x0b, 0x12, 0x1b,
	0x0a, 0x17, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f,
	0x50, 0x65, 0x6e, 0x65, 0x74, 0x72, 0x61, 0x74, 0x65, 0x10, 0x0c, 0x12, 0x18, 0x0a, 0x14, 0x41,
	0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x48, 0x69, 0x74,
	0x50, 0x72, 0x65, 0x10, 0x14, 0x12, 0x1a, 0x0a, 0x16, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75,
	0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x44, 0x6f, 0x64, 0x67, 0x65, 0x50, 0x72, 0x65, 0x10,
	0x15, 0x12, 0x1b, 0x0a, 0x17, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x5f, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x50, 0x72, 0x65, 0x10, 0x16, 0x12, 0x17,
	0x0a, 0x13, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f,
	0x48, 0x70, 0x50, 0x72, 0x65, 0x10, 0x17, 0x12, 0x18, 0x0a, 0x14, 0x41, 0x74, 0x74, 0x72, 0x69,
	0x62, 0x75, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x44, 0x65, 0x66, 0x50, 0x72, 0x65, 0x10,
	0x18, 0x12, 0x1e, 0x0a, 0x1a, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x5f, 0x4d, 0x6f, 0x76, 0x65, 0x53, 0x70, 0x65, 0x65, 0x64, 0x50, 0x72, 0x65, 0x10,
	0x19, 0x12, 0x1d, 0x0a, 0x19, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x5f, 0x43, 0x72, 0x69, 0x74, 0x69, 0x63, 0x61, 0x6c, 0x50, 0x72, 0x65, 0x10, 0x1a,
	0x12, 0x25, 0x0a, 0x21, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x5f, 0x43, 0x72, 0x69, 0x74, 0x69, 0x63, 0x61, 0x6c, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x70,
	0x6c, 0x65, 0x50, 0x72, 0x65, 0x10, 0x1b, 0x12, 0x23, 0x0a, 0x1f, 0x41, 0x74, 0x74, 0x72, 0x69,
	0x62, 0x75, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x43, 0x72, 0x69, 0x74, 0x69, 0x63, 0x61,
	0x6c, 0x52, 0x65, 0x73, 0x69, 0x73, 0x74, 0x50, 0x72, 0x65, 0x10, 0x1c, 0x12, 0x1f, 0x0a, 0x1b,
	0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x42, 0x61,
	0x73, 0x65, 0x44, 0x61, 0x6d, 0x61, 0x67, 0x65, 0x50, 0x72, 0x65, 0x10, 0x1d, 0x12, 0x1e, 0x0a,
	0x1a, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x47,
	0x75, 0x6e, 0x44, 0x61, 0x6d, 0x61, 0x67, 0x65, 0x50, 0x72, 0x65, 0x10, 0x1e, 0x12, 0x22, 0x0a,
	0x1e, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x50,
	0x68, 0x79, 0x73, 0x69, 0x63, 0x73, 0x44, 0x61, 0x6d, 0x61, 0x67, 0x65, 0x50, 0x72, 0x65, 0x10,
	0x1f, 0x12, 0x1e, 0x0a, 0x1a, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x5f, 0x49, 0x63, 0x65, 0x44, 0x61, 0x6d, 0x61, 0x67, 0x65, 0x50, 0x72, 0x65, 0x10,
	0x20, 0x12, 0x26, 0x0a, 0x22, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x5f, 0x45, 0x6c, 0x65, 0x63, 0x74, 0x72, 0x69, 0x63, 0x69, 0x74, 0x79, 0x44, 0x61,
	0x6d, 0x61, 0x67, 0x65, 0x50, 0x72, 0x65, 0x10, 0x21, 0x12, 0x1f, 0x0a, 0x1b, 0x41, 0x74, 0x74,
	0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x46, 0x69, 0x72, 0x65, 0x44,
	0x61, 0x6d, 0x61, 0x67, 0x65, 0x50, 0x72, 0x65, 0x10, 0x22, 0x12, 0x21, 0x0a, 0x1d, 0x41, 0x74,
	0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x45, 0x6e, 0x65, 0x72,
	0x67, 0x79, 0x44, 0x61, 0x6d, 0x61, 0x67, 0x65, 0x50, 0x72, 0x65, 0x10, 0x23, 0x12, 0x1f, 0x0a,
	0x1b, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x57,
	0x69, 0x6e, 0x64, 0x44, 0x61, 0x6d, 0x61, 0x67, 0x65, 0x50, 0x72, 0x65, 0x10, 0x24, 0x12, 0x1e,
	0x0a, 0x1a, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f,
	0x50, 0x65, 0x6e, 0x65, 0x74, 0x72, 0x61, 0x74, 0x65, 0x50, 0x72, 0x65, 0x10, 0x25, 0x12, 0x1b,
	0x0a, 0x17, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f,
	0x47, 0x75, 0x6e, 0x44, 0x65, 0x66, 0x50, 0x72, 0x65, 0x10, 0x26, 0x12, 0x1f, 0x0a, 0x1b, 0x41,
	0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x50, 0x68, 0x79,
	0x73, 0x69, 0x63, 0x73, 0x44, 0x65, 0x66, 0x50, 0x72, 0x65, 0x10, 0x27, 0x12, 0x1b, 0x0a, 0x17,
	0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x49, 0x63,
	0x65, 0x44, 0x65, 0x66, 0x50, 0x72, 0x65, 0x10, 0x28, 0x12, 0x23, 0x0a, 0x1f, 0x41, 0x74, 0x74,
	0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x45, 0x6c, 0x65, 0x63, 0x74,
	0x72, 0x69, 0x63, 0x69, 0x74, 0x79, 0x44, 0x65, 0x66, 0x50, 0x72, 0x65, 0x10, 0x29, 0x12, 0x1c,
	0x0a, 0x18, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f,
	0x46, 0x69, 0x72, 0x65, 0x44, 0x65, 0x66, 0x50, 0x72, 0x65, 0x10, 0x2a, 0x12, 0x1e, 0x0a, 0x1a,
	0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x45, 0x6e,
	0x65, 0x72, 0x67, 0x79, 0x44, 0x65, 0x66, 0x50, 0x72, 0x65, 0x10, 0x2b, 0x12, 0x1c, 0x0a, 0x18,
	0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x57, 0x69,
	0x6e, 0x64, 0x44, 0x65, 0x66, 0x50, 0x72, 0x65, 0x10, 0x2c, 0x12, 0x24, 0x0a, 0x20, 0x41, 0x74,
	0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x47, 0x72, 0x6f, 0x75,
	0x6e, 0x64, 0x44, 0x65, 0x6d, 0x61, 0x67, 0x65, 0x41, 0x64, 0x64, 0x50, 0x72, 0x65, 0x10, 0x2d,
	0x12, 0x24, 0x0a, 0x20, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x5f, 0x4d, 0x69, 0x64, 0x61, 0x69, 0x72, 0x44, 0x61, 0x6d, 0x61, 0x67, 0x65, 0x41, 0x64,
	0x64, 0x50, 0x72, 0x65, 0x10, 0x2e, 0x12, 0x22, 0x0a, 0x1e, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62,
	0x75, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x4e, 0x65, 0x61, 0x72, 0x44, 0x61, 0x6d, 0x61,
	0x67, 0x65, 0x41, 0x64, 0x64, 0x50, 0x72, 0x65, 0x10, 0x2f, 0x12, 0x25, 0x0a, 0x21, 0x41, 0x74,
	0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x4c, 0x6f, 0x6e, 0x67,
	0x44, 0x69, 0x73, 0x44, 0x61, 0x6d, 0x61, 0x67, 0x65, 0x41, 0x64, 0x64, 0x50, 0x72, 0x65, 0x10,
	0x30, 0x12, 0x2a, 0x0a, 0x26, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x5f, 0x53, 0x6d, 0x61, 0x6c, 0x6c, 0x4d, 0x6f, 0x6e, 0x73, 0x74, 0x65, 0x72, 0x44,
	0x61, 0x6d, 0x61, 0x67, 0x65, 0x41, 0x64, 0x64, 0x50, 0x72, 0x65, 0x10, 0x31, 0x12, 0x2a, 0x0a,
	0x26, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x45,
	0x6c, 0x69, 0x74, 0x65, 0x4d, 0x6f, 0x6e, 0x73, 0x74, 0x65, 0x72, 0x44, 0x61, 0x6d, 0x61, 0x67,
	0x65, 0x41, 0x64, 0x64, 0x50, 0x72, 0x65, 0x10, 0x32, 0x12, 0x29, 0x0a, 0x25, 0x41, 0x74, 0x74,
	0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x42, 0x6f, 0x73, 0x73, 0x4d,
	0x6f, 0x6e, 0x73, 0x74, 0x65, 0x72, 0x44, 0x61, 0x6d, 0x61, 0x67, 0x65, 0x41, 0x64, 0x64, 0x50,
	0x72, 0x65, 0x10, 0x33, 0x12, 0x1f, 0x0a, 0x1b, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x44, 0x65, 0x63, 0x72, 0x65, 0x61, 0x73, 0x65, 0x47, 0x75,
	0x6e, 0x43, 0x44, 0x10, 0x34, 0x12, 0x21, 0x0a, 0x1d, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75,
	0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x44, 0x65, 0x63, 0x72, 0x65, 0x61, 0x73, 0x65, 0x53,
	0x6b, 0x69, 0x6c, 0x6c, 0x43, 0x44, 0x10, 0x35, 0x12, 0x1e, 0x0a, 0x1a, 0x41, 0x74, 0x74, 0x72,
	0x69, 0x62, 0x75, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x47, 0x6f, 0x6c, 0x64, 0x42, 0x6f,
	0x6e, 0x75, 0x73, 0x50, 0x72, 0x65, 0x10, 0x50, 0x12, 0x1d, 0x0a, 0x19, 0x41, 0x74, 0x74, 0x72,
	0x69, 0x62, 0x75, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x45, 0x78, 0x70, 0x42, 0x6f, 0x6e,
	0x75, 0x73, 0x50, 0x72, 0x65, 0x10, 0x51, 0x12, 0x1c, 0x0a, 0x18, 0x41, 0x74, 0x74, 0x72, 0x69,
	0x62, 0x75, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x57, 0x61, 0x72, 0x43, 0x6f, 0x69, 0x6e,
	0x50, 0x72, 0x65, 0x10, 0x52, 0x12, 0x1a, 0x0a, 0x16, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75,
	0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x4d, 0x61, 0x67, 0x61, 0x7a, 0x69, 0x6e, 0x65, 0x10,
	0x53, 0x12, 0x1d, 0x0a, 0x19, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x5f, 0x4d, 0x61, 0x67, 0x61, 0x7a, 0x69, 0x6e, 0x65, 0x50, 0x72, 0x65, 0x10, 0x54,
	0x2a, 0x6a, 0x0a, 0x0b, 0x4d, 0x6f, 0x6e, 0x73, 0x74, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x14, 0x0a, 0x10, 0x4d, 0x6f, 0x6e, 0x73, 0x74, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x4e,
	0x6f, 0x6e, 0x65, 0x10, 0x00, 0x12, 0x15, 0x0a, 0x11, 0x4d, 0x6f, 0x6e, 0x73, 0x74, 0x65, 0x72,
	0x54, 0x79, 0x70, 0x65, 0x5f, 0x53, 0x6d, 0x61, 0x6c, 0x6c, 0x10, 0x01, 0x12, 0x18, 0x0a, 0x14,
	0x4d, 0x6f, 0x6e, 0x73, 0x74, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x41, 0x64, 0x76, 0x61,
	0x6e, 0x63, 0x65, 0x64, 0x10, 0x02, 0x12, 0x14, 0x0a, 0x10, 0x4d, 0x6f, 0x6e, 0x73, 0x74, 0x65,
	0x72, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x42, 0x6f, 0x73, 0x73, 0x10, 0x03, 0x2a, 0x40, 0x0a, 0x0a,
	0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x15, 0x0a, 0x11, 0x42, 0x61,
	0x74, 0x74, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x10,
	0x00, 0x12, 0x1b, 0x0a, 0x17, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f,
	0x53, 0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x50, 0x69, 0x6c, 0x65, 0x10, 0x64, 0x2a, 0xd2,
	0x01, 0x0a, 0x0b, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x0e,
	0x0a, 0x0a, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x4e, 0x4f, 0x4e, 0x45, 0x10, 0x00, 0x12, 0x15,
	0x0a, 0x11, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x52, 0x4f, 0x55, 0x4e, 0x44, 0x5f, 0x53, 0x54,
	0x41, 0x52, 0x54, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x50,
	0x52, 0x45, 0x50, 0x41, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x02, 0x12, 0x19, 0x0a, 0x15,
	0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x42, 0x41, 0x54, 0x54, 0x4c, 0x45, 0x5f, 0x53, 0x54, 0x41,
	0x52, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x03, 0x12, 0x1c, 0x0a, 0x18, 0x53, 0x54, 0x41, 0x54, 0x45,
	0x5f, 0x42, 0x41, 0x54, 0x54, 0x4c, 0x45, 0x5f, 0x49, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52,
	0x45, 0x53, 0x53, 0x10, 0x04, 0x12, 0x1a, 0x0a, 0x16, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x52,
	0x4f, 0x55, 0x4e, 0x44, 0x5f, 0x53, 0x45, 0x54, 0x54, 0x4c, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x10,
	0x05, 0x12, 0x1b, 0x0a, 0x17, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x45, 0x4c, 0x49, 0x4d, 0x49,
	0x4e, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x10, 0x06, 0x12, 0x13,
	0x0a, 0x0f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x47, 0x41, 0x4d, 0x45, 0x5f, 0x4f, 0x56, 0x45,
	0x52, 0x10, 0x07, 0x2a, 0x7d, 0x0a, 0x0d, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e, 0x53, 0x54, 0x41, 0x4e, 0x43, 0x45,
	0x5f, 0x57, 0x41, 0x49, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x00, 0x12, 0x1f, 0x0a, 0x1b, 0x49, 0x4e,
	0x53, 0x54, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x42, 0x41, 0x54, 0x54, 0x4c, 0x45, 0x5f, 0x49, 0x4e,
	0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x10, 0x01, 0x12, 0x1c, 0x0a, 0x18, 0x49,
	0x4e, 0x53, 0x54, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x42, 0x41, 0x54, 0x54, 0x4c, 0x45, 0x5f, 0x46,
	0x49, 0x4e, 0x49, 0x53, 0x48, 0x45, 0x44, 0x10, 0x02, 0x12, 0x17, 0x0a, 0x13, 0x49, 0x4e, 0x53,
	0x54, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x53, 0x45, 0x54, 0x54, 0x4c, 0x45, 0x4d, 0x45, 0x4e, 0x54,
	0x10, 0x03, 0x2a, 0xb3, 0x01, 0x0a, 0x0b, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x12, 0x12, 0x0a, 0x0e, 0x50, 0x4c, 0x41, 0x59, 0x45, 0x52, 0x5f, 0x57, 0x41, 0x49,
	0x54, 0x49, 0x4e, 0x47, 0x10, 0x00, 0x12, 0x19, 0x0a, 0x15, 0x50, 0x4c, 0x41, 0x59, 0x45, 0x52,
	0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x42, 0x55, 0x46, 0x46, 0x10,
	0x01, 0x12, 0x1b, 0x0a, 0x17, 0x50, 0x4c, 0x41, 0x59, 0x45, 0x52, 0x5f, 0x52, 0x45, 0x43, 0x45,
	0x49, 0x56, 0x49, 0x4e, 0x47, 0x5f, 0x48, 0x45, 0x52, 0x4f, 0x45, 0x53, 0x10, 0x02, 0x12, 0x19,
	0x0a, 0x15, 0x50, 0x4c, 0x41, 0x59, 0x45, 0x52, 0x5f, 0x46, 0x52, 0x45, 0x45, 0x5f, 0x4f, 0x50,
	0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x03, 0x12, 0x10, 0x0a, 0x0c, 0x50, 0x4c, 0x41,
	0x59, 0x45, 0x52, 0x5f, 0x52, 0x45, 0x41, 0x44, 0x59, 0x10, 0x04, 0x12, 0x14, 0x0a, 0x10, 0x50,
	0x4c, 0x41, 0x59, 0x45, 0x52, 0x5f, 0x49, 0x4e, 0x5f, 0x42, 0x41, 0x54, 0x54, 0x4c, 0x45, 0x10,
	0x05, 0x12, 0x15, 0x0a, 0x11, 0x50, 0x4c, 0x41, 0x59, 0x45, 0x52, 0x5f, 0x45, 0x4c, 0x49, 0x4d,
	0x49, 0x4e, 0x41, 0x54, 0x45, 0x44, 0x10, 0x06, 0x2a, 0x55, 0x0a, 0x0c, 0x41, 0x64, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x14, 0x42, 0x41, 0x54, 0x54,
	0x4c, 0x45, 0x5f, 0x41, 0x44, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x4e, 0x4f, 0x4e, 0x45,
	0x10, 0x00, 0x12, 0x16, 0x0a, 0x12, 0x42, 0x41, 0x54, 0x54, 0x4c, 0x45, 0x5f, 0x53, 0x55, 0x50,
	0x50, 0x4c, 0x59, 0x5f, 0x44, 0x52, 0x4f, 0x50, 0x10, 0x01, 0x12, 0x13, 0x0a, 0x0f, 0x42, 0x41,
	0x54, 0x54, 0x4c, 0x45, 0x5f, 0x42, 0x4c, 0x45, 0x53, 0x53, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x2a,
	0x60, 0x0a, 0x09, 0x47, 0x61, 0x63, 0x68, 0x61, 0x54, 0x79, 0x70, 0x65, 0x12, 0x13, 0x0a, 0x0f,
	0x47, 0x61, 0x63, 0x68, 0x61, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x45, 0x71, 0x75, 0x69, 0x70, 0x10,
	0x00, 0x12, 0x13, 0x0a, 0x0f, 0x47, 0x61, 0x63, 0x68, 0x61, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x53,
	0x6b, 0x69, 0x6c, 0x6c, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11, 0x47, 0x61, 0x63, 0x68, 0x61, 0x54,
	0x79, 0x70, 0x65, 0x5f, 0x48, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x73, 0x10, 0x02, 0x12, 0x12, 0x0a,
	0x0e, 0x47, 0x61, 0x63, 0x68, 0x61, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x50, 0x65, 0x74, 0x73, 0x10,
	0x03, 0x2a, 0x86, 0x01, 0x0a, 0x09, 0x45, 0x71, 0x75, 0x69, 0x70, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x0e, 0x0a, 0x0a, 0x45, 0x71, 0x75, 0x69, 0x70, 0x5f, 0x4e, 0x4f, 0x4e, 0x45, 0x10, 0x00, 0x12,
	0x10, 0x0a, 0x0c, 0x45, 0x71, 0x75, 0x69, 0x70, 0x5f, 0x42, 0x72, 0x61, 0x63, 0x65, 0x72, 0x10,
	0x01, 0x12, 0x10, 0x0a, 0x0c, 0x45, 0x71, 0x75, 0x69, 0x70, 0x5f, 0x48, 0x65, 0x6c, 0x6d, 0x65,
	0x74, 0x10, 0x02, 0x12, 0x0f, 0x0a, 0x0b, 0x45, 0x71, 0x75, 0x69, 0x70, 0x5f, 0x53, 0x68, 0x6f,
	0x65, 0x73, 0x10, 0x03, 0x12, 0x0f, 0x0a, 0x0b, 0x45, 0x71, 0x75, 0x69, 0x70, 0x5f, 0x50, 0x61,
	0x6e, 0x74, 0x73, 0x10, 0x04, 0x12, 0x10, 0x0a, 0x0c, 0x45, 0x71, 0x75, 0x69, 0x70, 0x5f, 0x47,
	0x6c, 0x6f, 0x76, 0x65, 0x73, 0x10, 0x05, 0x12, 0x11, 0x0a, 0x0d, 0x45, 0x71, 0x75, 0x69, 0x70,
	0x5f, 0x43, 0x6c, 0x6f, 0x74, 0x68, 0x65, 0x73, 0x10, 0x06, 0x2a, 0x39, 0x0a, 0x0f, 0x44, 0x72,
	0x6f, 0x70, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a,
	0x0e, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x4e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x10,
	0x00, 0x12, 0x12, 0x0a, 0x0e, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x53, 0x74, 0x61,
	0x67, 0x65, 0x73, 0x10, 0x01, 0x2a, 0xb7, 0x01, 0x0a, 0x0b, 0x51, 0x75, 0x61, 0x6c, 0x69, 0x74,
	0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x0c, 0x51, 0x75, 0x61, 0x6c, 0x69, 0x74, 0x79,
	0x5f, 0x47, 0x72, 0x65, 0x79, 0x10, 0x00, 0x12, 0x11, 0x0a, 0x0d, 0x51, 0x75, 0x61, 0x6c, 0x69,
	0x74, 0x79, 0x5f, 0x47, 0x72, 0x65, 0x65, 0x6e, 0x10, 0x01, 0x12, 0x10, 0x0a, 0x0c, 0x51, 0x75,
	0x61, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x42, 0x6c, 0x75, 0x65, 0x10, 0x02, 0x12, 0x12, 0x0a, 0x0e,
	0x51, 0x75, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x50, 0x75, 0x72, 0x70, 0x6c, 0x65, 0x10, 0x03,
	0x12, 0x10, 0x0a, 0x0c, 0x51, 0x75, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x47, 0x6f, 0x6c, 0x64,
	0x10, 0x04, 0x12, 0x12, 0x0a, 0x0e, 0x51, 0x75, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x4f, 0x72,
	0x61, 0x6e, 0x67, 0x65, 0x10, 0x05, 0x12, 0x0f, 0x0a, 0x0b, 0x51, 0x75, 0x61, 0x6c, 0x69, 0x74,
	0x79, 0x5f, 0x52, 0x65, 0x64, 0x10, 0x06, 0x12, 0x13, 0x0a, 0x0f, 0x51, 0x75, 0x61, 0x6c, 0x69,
	0x74, 0x79, 0x5f, 0x44, 0x65, 0x65, 0x70, 0x52, 0x65, 0x64, 0x10, 0x07, 0x12, 0x11, 0x0a, 0x0d,
	0x51, 0x75, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x41, 0x72, 0x67, 0x73, 0x38, 0x10, 0x08, 0x2a,
	0xce, 0x02, 0x0a, 0x08, 0x4d, 0x61, 0x69, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x12,
	0x4d, 0x61, 0x69, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x4e, 0x6f, 0x6e, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x4d, 0x61, 0x69, 0x6c, 0x54, 0x79, 0x70, 0x65,
	0x5f, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x10, 0x01, 0x12, 0x19, 0x0a, 0x15, 0x4d, 0x61, 0x69,
	0x6c, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x10, 0x02, 0x12, 0x17, 0x0a, 0x13, 0x4d, 0x61, 0x69, 0x6c, 0x54, 0x79, 0x70, 0x65,
	0x5f, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x41, 0x75, 0x74, 0x68, 0x10, 0x03, 0x12, 0x1b, 0x0a,
	0x17, 0x4d, 0x61, 0x69, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x41, 0x75, 0x74, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x10, 0x04, 0x12, 0x1d, 0x0a, 0x19, 0x4d, 0x61,
	0x69, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x4d, 0x61, 0x69,
	0x6c, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x10, 0x05, 0x12, 0x14, 0x0a, 0x10, 0x4d, 0x61, 0x69,
	0x6c, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x47, 0x69, 0x66, 0x74, 0x52, 0x4d, 0x42, 0x10, 0x06, 0x12,
	0x21, 0x0a, 0x1d, 0x4d, 0x61, 0x69, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x47, 0x6c, 0x6f, 0x62,
	0x61, 0x6c, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x10, 0x07, 0x12, 0x0f, 0x0a, 0x0b, 0x4d, 0x61, 0x69, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x41,
	0x44, 0x10, 0x08, 0x12, 0x1c, 0x0a, 0x18, 0x4d, 0x61, 0x69, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x5f,
	0x47, 0x75, 0x69, 0x6c, 0x64, 0x4f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x4d, 0x73, 0x67, 0x10,
	0x09, 0x12, 0x17, 0x0a, 0x13, 0x4d, 0x61, 0x69, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x51, 0x75,
	0x65, 0x73, 0x74, 0x41, 0x77, 0x61, 0x72, 0x64, 0x10, 0x0a, 0x12, 0x24, 0x0a, 0x20, 0x4d, 0x61,
	0x69, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x5f,
	0x4f, 0x70, 0x65, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x5f, 0x45, 0x6e, 0x64, 0x10, 0x0b,
	0x2a, 0x5d, 0x0a, 0x0d, 0x4d, 0x61, 0x69, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x18, 0x0a, 0x14, 0x4d, 0x61, 0x69, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x5f, 0x55, 0x6e, 0x52, 0x65, 0x61, 0x64, 0x10, 0x00, 0x12, 0x16, 0x0a, 0x12, 0x4d,
	0x61, 0x69, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x52, 0x65, 0x61,
	0x64, 0x10, 0x01, 0x12, 0x1a, 0x0a, 0x16, 0x4d, 0x61, 0x69, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x5f, 0x47, 0x6f, 0x74, 0x41, 0x77, 0x61, 0x72, 0x64, 0x10, 0x02, 0x2a,
	0x63, 0x0a, 0x0e, 0x4d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x19, 0x0a, 0x15, 0x4d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x5f, 0x54, 0x65, 0x73, 0x74, 0x47, 0x4d, 0x10, 0x00, 0x12, 0x1b, 0x0a, 0x17,
	0x4d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x47,
	0x4d, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x10, 0x01, 0x12, 0x19, 0x0a, 0x15, 0x4d, 0x61, 0x69,
	0x6c, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x53, 0x79, 0x73, 0x74,
	0x65, 0x6d, 0x10, 0x02, 0x2a, 0x7b, 0x0a, 0x10, 0x47, 0x75, 0x69, 0x64, 0x65, 0x54, 0x72, 0x69,
	0x67, 0x67, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x08, 0x0a, 0x04, 0x4e, 0x6f, 0x6e, 0x65,
	0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x4e, 0x65, 0x77, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x10,
	0x01, 0x12, 0x0a, 0x0a, 0x06, 0x4f, 0x70, 0x65, 0x6e, 0x55, 0x69, 0x10, 0x02, 0x12, 0x0b, 0x0a,
	0x07, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x55, 0x69, 0x10, 0x03, 0x12, 0x10, 0x0a, 0x0c, 0x55, 0x6e,
	0x6c, 0x6f, 0x63, 0x6b, 0x46, 0x75, 0x6e, 0x63, 0x49, 0x64, 0x10, 0x04, 0x12, 0x12, 0x0a, 0x0e,
	0x50, 0x61, 0x73, 0x73, 0x65, 0x64, 0x4d, 0x61, 0x69, 0x6e, 0x4c, 0x69, 0x6e, 0x65, 0x10, 0x05,
	0x12, 0x0f, 0x0a, 0x0b, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x47, 0x75, 0x69, 0x64, 0x65, 0x10,
	0x06, 0x2a, 0xc1, 0x02, 0x0a, 0x13, 0x4e, 0x65, 0x77, 0x47, 0x75, 0x69, 0x64, 0x65, 0x54, 0x72,
	0x69, 0x67, 0x67, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x1a, 0x4e, 0x65, 0x77,
	0x47, 0x75, 0x69, 0x64, 0x65, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65,
	0x5f, 0x4e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x10, 0x00, 0x12, 0x27, 0x0a, 0x23, 0x4e, 0x65, 0x77,
	0x47, 0x75, 0x69, 0x64, 0x65, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65,
	0x5f, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x54, 0x61, 0x73, 0x6b,
	0x10, 0x01, 0x12, 0x25, 0x0a, 0x21, 0x4e, 0x65, 0x77, 0x47, 0x75, 0x69, 0x64, 0x65, 0x54, 0x72,
	0x69, 0x67, 0x67, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x10, 0x02, 0x12, 0x23, 0x0a, 0x1f, 0x4e, 0x65, 0x77,
	0x47, 0x75, 0x69, 0x64, 0x65, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65,
	0x5f, 0x4e, 0x65, 0x77, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x45, 0x6e, 0x64, 0x10, 0x03, 0x12, 0x23,
	0x0a, 0x1f, 0x4e, 0x65, 0x77, 0x47, 0x75, 0x69, 0x64, 0x65, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65,
	0x72, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x45, 0x6e,
	0x64, 0x10, 0x04, 0x12, 0x22, 0x0a, 0x1e, 0x4e, 0x65, 0x77, 0x47, 0x75, 0x69, 0x64, 0x65, 0x54,
	0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x10, 0x05, 0x12, 0x28, 0x0a, 0x24, 0x4e, 0x65, 0x77, 0x47, 0x75,
	0x69, 0x64, 0x65, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x43,
	0x6f, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x67, 0x65, 0x10,
	0x06, 0x12, 0x22, 0x0a, 0x1e, 0x4e, 0x65, 0x77, 0x47, 0x75, 0x69, 0x64, 0x65, 0x54, 0x72, 0x69,
	0x67, 0x67, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79,
	0x45, 0x6e, 0x64, 0x10, 0x07, 0x2a, 0x4d, 0x0a, 0x0f, 0x4e, 0x65, 0x77, 0x47, 0x75, 0x69, 0x64,
	0x65, 0x46, 0x75, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x16, 0x4e, 0x65, 0x77, 0x47,
	0x75, 0x69, 0x64, 0x65, 0x46, 0x75, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x4e, 0x6f, 0x72, 0x6d,
	0x61, 0x6c, 0x10, 0x00, 0x12, 0x1e, 0x0a, 0x1a, 0x4e, 0x65, 0x77, 0x47, 0x75, 0x69, 0x64, 0x65,
	0x46, 0x75, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x10, 0x01, 0x2a, 0x78, 0x0a, 0x0c, 0x53, 0x69, 0x67, 0x6e, 0x49, 0x6e, 0x54, 0x6f,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x15, 0x0a, 0x11, 0x53, 0x69, 0x67, 0x6e, 0x49, 0x6e, 0x54, 0x6f,
	0x54, 0x79, 0x70, 0x65, 0x5f, 0x4e, 0x6f, 0x6e, 0x65, 0x10, 0x00, 0x12, 0x19, 0x0a, 0x15, 0x53,
	0x69, 0x67, 0x6e, 0x49, 0x6e, 0x54, 0x6f, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x53, 0x65, 0x76, 0x65,
	0x6e, 0x44, 0x61, 0x79, 0x10, 0x01, 0x12, 0x19, 0x0a, 0x15, 0x53, 0x69, 0x67, 0x6e, 0x49, 0x6e,
	0x54, 0x6f, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x45, 0x76, 0x65, 0x72, 0x79, 0x44, 0x61, 0x79, 0x10,
	0x02, 0x12, 0x1b, 0x0a, 0x17, 0x53, 0x69, 0x67, 0x6e, 0x49, 0x6e, 0x54, 0x6f, 0x54, 0x79, 0x70,
	0x65, 0x5f, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x53, 0x74, 0x61, 0x67, 0x65, 0x10, 0x03, 0x2a, 0x5a,
	0x0a, 0x0c, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16,
	0x0a, 0x12, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x44,
	0x6f, 0x69, 0x6e, 0x67, 0x10, 0x00, 0x12, 0x17, 0x0a, 0x13, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x10, 0x01, 0x12,
	0x19, 0x0a, 0x15, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f,
	0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x64, 0x10, 0x02, 0x2a, 0x58, 0x0a, 0x0a, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x17, 0x0a, 0x13, 0x52, 0x45, 0x57, 0x41,
	0x52, 0x44, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10,
	0x00, 0x12, 0x14, 0x0a, 0x10, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x52, 0x41, 0x4e, 0x4b, 0x10, 0x01, 0x12, 0x1b, 0x0a, 0x17, 0x52, 0x45, 0x57, 0x41, 0x52,
	0x44, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x52, 0x41,
	0x4e, 0x4b, 0x10, 0x02, 0x2a, 0x52, 0x0a, 0x08, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x0f, 0x0a, 0x0b, 0x55, 0x6e, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x10,
	0x00, 0x12, 0x08, 0x0a, 0x04, 0x44, 0x72, 0x6f, 0x70, 0x10, 0x01, 0x12, 0x10, 0x0a, 0x0c, 0x48,
	0x61, 0x6e, 0x67, 0x55, 0x70, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x10, 0x02, 0x12, 0x0d, 0x0a,
	0x09, 0x43, 0x68, 0x6f, 0x6f, 0x73, 0x65, 0x42, 0x6f, 0x78, 0x10, 0x03, 0x12, 0x0a, 0x0a, 0x06,
	0x44, 0x65, 0x76, 0x72, 0x69, 0x73, 0x10, 0x04, 0x2a, 0x40, 0x0a, 0x15, 0x49, 0x74, 0x65, 0x6d,
	0x53, 0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x43, 0x68, 0x6f, 0x6f, 0x73, 0x65, 0x42, 0x6f,
	0x78, 0x12, 0x12, 0x0a, 0x0e, 0x43, 0x68, 0x6f, 0x6f, 0x73, 0x65, 0x42, 0x6f, 0x78, 0x5f, 0x4f,
	0x6e, 0x63, 0x65, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x43, 0x68, 0x6f, 0x6f, 0x73, 0x65, 0x42,
	0x6f, 0x78, 0x5f, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x10, 0x01, 0x2a, 0x33, 0x0a, 0x10, 0x49, 0x74,
	0x65, 0x6d, 0x53, 0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x44, 0x72, 0x6f, 0x70, 0x12, 0x0e,
	0x0a, 0x0a, 0x44, 0x72, 0x6f, 0x70, 0x5f, 0x46, 0x69, 0x78, 0x65, 0x64, 0x10, 0x00, 0x12, 0x0f,
	0x0a, 0x0b, 0x44, 0x72, 0x6f, 0x70, 0x5f, 0x52, 0x61, 0x6e, 0x64, 0x6f, 0x6d, 0x10, 0x01, 0x2a,
	0x5a, 0x0a, 0x12, 0x49, 0x74, 0x65, 0x6d, 0x53, 0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x44,
	0x65, 0x76, 0x72, 0x69, 0x73, 0x12, 0x0f, 0x0a, 0x0b, 0x44, 0x65, 0x76, 0x72, 0x69, 0x73, 0x5f,
	0x53, 0x6b, 0x69, 0x6e, 0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e, 0x44, 0x65, 0x76, 0x72, 0x69, 0x73,
	0x5f, 0x46, 0x69, 0x72, 0x65, 0x61, 0x72, 0x6d, 0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x44, 0x65,
	0x76, 0x72, 0x69, 0x73, 0x5f, 0x57, 0x61, 0x6c, 0x6c, 0x10, 0x02, 0x12, 0x0e, 0x0a, 0x0a, 0x44,
	0x65, 0x76, 0x72, 0x69, 0x73, 0x5f, 0x50, 0x65, 0x74, 0x10, 0x03, 0x2a, 0x1d, 0x0a, 0x0b, 0x42,
	0x61, 0x67, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a, 0x0a, 0x4e, 0x6f,
	0x72, 0x6d, 0x61, 0x6c, 0x49, 0x74, 0x65, 0x6d, 0x10, 0x00, 0x2a, 0xd0, 0x01, 0x0a, 0x13, 0x53,
	0x79, 0x73, 0x74, 0x65, 0x6d, 0x53, 0x68, 0x6f, 0x77, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x1c, 0x0a, 0x18, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x53, 0x68, 0x6f, 0x77,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x4e, 0x6f, 0x6e, 0x65, 0x10, 0x00,
	0x12, 0x1c, 0x0a, 0x18, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x53, 0x68, 0x6f, 0x77, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x4b, 0x69, 0x63, 0x6b, 0x10, 0x01, 0x12, 0x1f,
	0x0a, 0x1b, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x53, 0x68, 0x6f, 0x77, 0x45, 0x72, 0x72, 0x6f,
	0x72, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x10, 0x02, 0x12,
	0x1f, 0x0a, 0x1b, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x53, 0x68, 0x6f, 0x77, 0x45, 0x72, 0x72,
	0x6f, 0x72, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x43, 0x68, 0x61, 0x74, 0x42, 0x61, 0x6e, 0x10, 0x03,
	0x12, 0x1c, 0x0a, 0x18, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x53, 0x68, 0x6f, 0x77, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x54, 0x69, 0x70, 0x73, 0x10, 0x04, 0x12, 0x1d,
	0x0a, 0x19, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x53, 0x68, 0x6f, 0x77, 0x45, 0x72, 0x72, 0x6f,
	0x72, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x10, 0x05, 0x2a, 0xb0, 0x01,
	0x0a, 0x0d, 0x47, 0x69, 0x66, 0x74, 0x50, 0x61, 0x63, 0x6b, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x16, 0x0a, 0x12, 0x47, 0x69, 0x66, 0x74, 0x50, 0x61, 0x63, 0x6b, 0x73, 0x54, 0x79, 0x70, 0x65,
	0x5f, 0x4e, 0x6f, 0x6e, 0x65, 0x10, 0x00, 0x12, 0x17, 0x0a, 0x13, 0x47, 0x69, 0x66, 0x74, 0x50,
	0x61, 0x63, 0x6b, 0x73, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x10, 0x01,
	0x12, 0x18, 0x0a, 0x14, 0x47, 0x69, 0x66, 0x74, 0x50, 0x61, 0x63, 0x6b, 0x73, 0x54, 0x79, 0x70,
	0x65, 0x5f, 0x57, 0x65, 0x65, 0x6b, 0x6c, 0x79, 0x10, 0x02, 0x12, 0x1b, 0x0a, 0x17, 0x47, 0x69,
	0x66, 0x74, 0x50, 0x61, 0x63, 0x6b, 0x73, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x44, 0x61, 0x69, 0x6c,
	0x79, 0x50, 0x6c, 0x75, 0x73, 0x10, 0x03, 0x12, 0x1a, 0x0a, 0x16, 0x47, 0x69, 0x66, 0x74, 0x50,
	0x61, 0x63, 0x6b, 0x73, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x57, 0x65, 0x65, 0x6b, 0x43, 0x61, 0x72,
	0x64, 0x10, 0x04, 0x12, 0x1b, 0x0a, 0x17, 0x47, 0x69, 0x66, 0x74, 0x50, 0x61, 0x63, 0x6b, 0x73,
	0x54, 0x79, 0x70, 0x65, 0x5f, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x10, 0x05,
	0x2a, 0x7f, 0x0a, 0x10, 0x47, 0x69, 0x66, 0x74, 0x50, 0x61, 0x63, 0x6b, 0x73, 0x42, 0x75, 0x79,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x14, 0x47, 0x69, 0x66, 0x74, 0x50, 0x61, 0x63, 0x6b,
	0x73, 0x42, 0x75, 0x79, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x41, 0x64, 0x76, 0x10, 0x00, 0x12, 0x18,
	0x0a, 0x14, 0x47, 0x69, 0x66, 0x74, 0x50, 0x61, 0x63, 0x6b, 0x73, 0x42, 0x75, 0x79, 0x54, 0x79,
	0x70, 0x65, 0x5f, 0x52, 0x6d, 0x62, 0x10, 0x01, 0x12, 0x19, 0x0a, 0x15, 0x47, 0x69, 0x66, 0x74,
	0x50, 0x61, 0x63, 0x6b, 0x73, 0x42, 0x75, 0x79, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x46, 0x72, 0x65,
	0x65, 0x10, 0x02, 0x12, 0x1c, 0x0a, 0x18, 0x47, 0x69, 0x66, 0x74, 0x50, 0x61, 0x63, 0x6b, 0x73,
	0x42, 0x75, 0x79, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x44, 0x69, 0x61, 0x6d, 0x6f, 0x6e, 0x64, 0x10,
	0x03, 0x2a, 0x8d, 0x01, 0x0a, 0x0a, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x13, 0x0a, 0x0f, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x4e,
	0x6f, 0x6e, 0x65, 0x10, 0x00, 0x12, 0x16, 0x0a, 0x12, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x5f, 0x44, 0x69, 0x61, 0x6d, 0x6f, 0x6e, 0x64, 0x10, 0x01, 0x12, 0x1e, 0x0a,
	0x1a, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x4d, 0x6f, 0x6e, 0x74,
	0x68, 0x43, 0x61, 0x72, 0x64, 0x4e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x10, 0x02, 0x12, 0x1d, 0x0a,
	0x19, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x4d, 0x6f, 0x6e, 0x74,
	0x68, 0x43, 0x61, 0x72, 0x64, 0x53, 0x75, 0x70, 0x65, 0x72, 0x10, 0x03, 0x12, 0x13, 0x0a, 0x0f,
	0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x49, 0x74, 0x65, 0x6d, 0x10,
	0x04, 0x2a, 0x77, 0x0a, 0x13, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x42, 0x6f, 0x78, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x14, 0x43, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x42, 0x6f, 0x78, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x4e, 0x6f, 0x6e, 0x65,
	0x10, 0x00, 0x12, 0x20, 0x0a, 0x1c, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x42, 0x6f, 0x78, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x4d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x10, 0x01, 0x12, 0x24, 0x0a, 0x20, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x42, 0x6f,
	0x78, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x53, 0x65, 0x76, 0x65, 0x6e, 0x44, 0x61, 0x79,
	0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x10, 0x02, 0x2a, 0x5e, 0x0a, 0x0e, 0x42, 0x61,
	0x74, 0x74, 0x6c, 0x65, 0x50, 0x61, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x17, 0x0a, 0x13,
	0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x50, 0x61, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x4e,
	0x4f, 0x4e, 0x45, 0x10, 0x00, 0x12, 0x17, 0x0a, 0x13, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x50,
	0x61, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x42, 0x41, 0x53, 0x45, 0x10, 0x01, 0x12, 0x1a,
	0x0a, 0x16, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x50, 0x61, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65,
	0x5f, 0x41, 0x44, 0x56, 0x41, 0x4e, 0x43, 0x45, 0x10, 0x02, 0x2a, 0x7a, 0x0a, 0x15, 0x42, 0x61,
	0x74, 0x74, 0x6c, 0x65, 0x50, 0x61, 0x73, 0x73, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x1a, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x50, 0x61, 0x73,
	0x73, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x4e, 0x4f, 0x4e,
	0x45, 0x10, 0x00, 0x12, 0x1f, 0x0a, 0x1b, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x50, 0x61, 0x73,
	0x73, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x44, 0x41, 0x49,
	0x4c, 0x59, 0x10, 0x01, 0x12, 0x20, 0x0a, 0x1c, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x50, 0x61,
	0x73, 0x73, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x57, 0x45,
	0x45, 0x4b, 0x4c, 0x59, 0x10, 0x02, 0x2a, 0x5b, 0x0a, 0x0a, 0x41, 0x77, 0x61, 0x72, 0x64, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x12, 0x1a, 0x0a, 0x16, 0x41, 0x77, 0x61, 0x72, 0x64, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x5f, 0x55, 0x4e, 0x41, 0x56, 0x41, 0x49, 0x4c, 0x41, 0x42, 0x4c, 0x45, 0x10, 0x00,
	0x12, 0x18, 0x0a, 0x14, 0x41, 0x77, 0x61, 0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x41,
	0x56, 0x41, 0x49, 0x4c, 0x41, 0x42, 0x4c, 0x45, 0x10, 0x01, 0x12, 0x17, 0x0a, 0x13, 0x41, 0x77,
	0x61, 0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x52, 0x45, 0x43, 0x45, 0x49, 0x56, 0x45,
	0x44, 0x10, 0x02, 0x2a, 0x82, 0x01, 0x0a, 0x0c, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x17, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79,
	0x54, 0x79, 0x70, 0x65, 0x5f, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x50, 0x61, 0x73, 0x73, 0x10,
	0x00, 0x12, 0x1a, 0x0a, 0x16, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70,
	0x65, 0x5f, 0x53, 0x65, 0x76, 0x65, 0x6e, 0x54, 0x61, 0x73, 0x6b, 0x10, 0x01, 0x12, 0x1a, 0x0a,
	0x16, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x53, 0x65,
	0x76, 0x65, 0x6e, 0x53, 0x69, 0x67, 0x6e, 0x10, 0x02, 0x12, 0x1d, 0x0a, 0x19, 0x41, 0x63, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x53, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x42, 0x75, 0x66, 0x66, 0x5f, 0x31, 0x10, 0x04, 0x2a, 0x5e, 0x0a, 0x0e, 0x41, 0x63, 0x74, 0x69,
	0x76, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1a, 0x0a, 0x16, 0x41, 0x63,
	0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x4e, 0x4f, 0x5f,
	0x4f, 0x50, 0x45, 0x4e, 0x10, 0x00, 0x12, 0x18, 0x0a, 0x14, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x53, 0x54, 0x41, 0x52, 0x54, 0x10, 0x01,
	0x12, 0x16, 0x0a, 0x12, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x5f, 0x45, 0x4e, 0x44, 0x10, 0x02, 0x2a, 0xd8, 0x05, 0x0a, 0x14, 0x49, 0x74, 0x65,
	0x6d, 0x41, 0x63, 0x71, 0x75, 0x69, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x12, 0x1d, 0x0a, 0x19, 0x49, 0x74, 0x65, 0x6d, 0x41, 0x63, 0x71, 0x75, 0x69, 0x73, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x4e, 0x4f, 0x4e, 0x45, 0x10, 0x00,
	0x12, 0x20, 0x0a, 0x1c, 0x49, 0x74, 0x65, 0x6d, 0x41, 0x63, 0x71, 0x75, 0x69, 0x73, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x44, 0x69, 0x61, 0x6d, 0x6f, 0x6e, 0x64,
	0x10, 0x01, 0x12, 0x1d, 0x0a, 0x19, 0x49, 0x74, 0x65, 0x6d, 0x41, 0x63, 0x71, 0x75, 0x69, 0x73,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x53, 0x74, 0x61, 0x72, 0x10,
	0x02, 0x12, 0x20, 0x0a, 0x1c, 0x49, 0x74, 0x65, 0x6d, 0x41, 0x63, 0x71, 0x75, 0x69, 0x73, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x45, 0x73, 0x73, 0x65, 0x6e, 0x63,
	0x65, 0x10, 0x03, 0x12, 0x24, 0x0a, 0x20, 0x49, 0x74, 0x65, 0x6d, 0x41, 0x63, 0x71, 0x75, 0x69,
	0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x41, 0x69, 0x72, 0x50,
	0x75, 0x72, 0x69, 0x66, 0x69, 0x65, 0x72, 0x10, 0x04, 0x12, 0x26, 0x0a, 0x22, 0x49, 0x74, 0x65,
	0x6d, 0x41, 0x63, 0x71, 0x75, 0x69, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x5f, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x10,
	0x05, 0x12, 0x28, 0x0a, 0x24, 0x49, 0x74, 0x65, 0x6d, 0x41, 0x63, 0x71, 0x75, 0x69, 0x73, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x44, 0x75, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x74, 0x65, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x10, 0x06, 0x12, 0x1f, 0x0a, 0x1b, 0x49,
	0x74, 0x65, 0x6d, 0x41, 0x63, 0x71, 0x75, 0x69, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x5f, 0x53, 0x68, 0x6f, 0x76, 0x65, 0x6c, 0x10, 0x07, 0x12, 0x22, 0x0a, 0x1e,
	0x49, 0x74, 0x65, 0x6d, 0x41, 0x63, 0x71, 0x75, 0x69, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x5f, 0x44, 0x72, 0x61, 0x67, 0x6f, 0x6e, 0x45, 0x67, 0x67, 0x10, 0x08,
	0x12, 0x23, 0x0a, 0x1f, 0x49, 0x74, 0x65, 0x6d, 0x41, 0x63, 0x71, 0x75, 0x69, 0x73, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x4d, 0x61, 0x67, 0x69, 0x63, 0x43, 0x6c,
	0x6f, 0x74, 0x68, 0x10, 0x09, 0x12, 0x1f, 0x0a, 0x1b, 0x49, 0x74, 0x65, 0x6d, 0x41, 0x63, 0x71,
	0x75, 0x69, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x43, 0x68,
	0x69, 0x73, 0x65, 0x6c, 0x10, 0x0a, 0x12, 0x32, 0x0a, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x41, 0x63,
	0x71, 0x75, 0x69, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x44,
	0x72, 0x61, 0x67, 0x6f, 0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x75, 0x6e, 0x64, 0x53, 0x6c, 0x6f,
	0x74, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x10, 0x0b, 0x12, 0x25, 0x0a, 0x21, 0x49, 0x74,
	0x65, 0x6d, 0x41, 0x63, 0x71, 0x75, 0x69, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x5f, 0x53, 0x6c, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x10,
	0x0c, 0x12, 0x32, 0x0a, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x41, 0x63, 0x71, 0x75, 0x69, 0x73, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x44, 0x72, 0x61, 0x67, 0x6f, 0x6e,
	0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x49, 0x74, 0x65, 0x6d,
	0x52, 0x6f, 0x62, 0x10, 0x0d, 0x12, 0x2c, 0x0a, 0x28, 0x49, 0x74, 0x65, 0x6d, 0x41, 0x63, 0x71,
	0x75, 0x69, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x44, 0x72,
	0x61, 0x67, 0x6f, 0x6e, 0x42, 0x75, 0x69, 0x6c, 0x64, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x10, 0x0e, 0x12, 0x2a, 0x0a, 0x26, 0x49, 0x74, 0x65, 0x6d, 0x41, 0x63, 0x71, 0x75, 0x69,
	0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x44, 0x72, 0x61, 0x67,
	0x6f, 0x6e, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x54, 0x65, 0x63, 0x68, 0x10, 0x0f, 0x12,
	0x29, 0x0a, 0x25, 0x49, 0x74, 0x65, 0x6d, 0x41, 0x63, 0x71, 0x75, 0x69, 0x73, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x46, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x10, 0x10, 0x12, 0x27, 0x0a, 0x23, 0x49, 0x74,
	0x65, 0x6d, 0x41, 0x63, 0x71, 0x75, 0x69, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x5f, 0x46, 0x61, 0x73, 0x68, 0x69, 0x6f, 0x6e, 0x42, 0x54, 0x53, 0x49, 0x74, 0x65,
	0x6d, 0x10, 0x11, 0x2a, 0x48, 0x0a, 0x0f, 0x49, 0x72, 0x6f, 0x6e, 0x53, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x41, 0x44, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x14, 0x49, 0x72, 0x6f, 0x6e, 0x53, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x41, 0x44, 0x79, 0x70, 0x65, 0x5f, 0x4e, 0x4f, 0x4e, 0x45, 0x10, 0x00,
	0x12, 0x1b, 0x0a, 0x17, 0x49, 0x72, 0x6f, 0x6e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x41, 0x44,
	0x79, 0x70, 0x65, 0x5f, 0x4f, 0x66, 0x66, 0x4c, 0x69, 0x6e, 0x65, 0x10, 0x01, 0x2a, 0x77, 0x0a,
	0x0d, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x18,
	0x0a, 0x14, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x4e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x10, 0x00, 0x12, 0x17, 0x0a, 0x13, 0x47, 0x75, 0x69, 0x6c,
	0x64, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x45, 0x6c, 0x69, 0x74, 0x65, 0x10,
	0x01, 0x12, 0x16, 0x0a, 0x12, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x56, 0x69, 0x63, 0x65, 0x10, 0x02, 0x12, 0x1b, 0x0a, 0x17, 0x47, 0x75, 0x69,
	0x6c, 0x64, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x50, 0x72, 0x65, 0x73, 0x69,
	0x64, 0x65, 0x6e, 0x74, 0x10, 0x03, 0x2a, 0xda, 0x04, 0x0a, 0x08, 0x47, 0x75, 0x69, 0x6c, 0x64,
	0x4f, 0x70, 0x74, 0x12, 0x11, 0x0a, 0x0d, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x4f, 0x70, 0x74, 0x5f,
	0x4e, 0x6f, 0x6e, 0x65, 0x10, 0x00, 0x12, 0x15, 0x0a, 0x11, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x4f,
	0x70, 0x74, 0x5f, 0x45, 0x64, 0x69, 0x74, 0x49, 0x63, 0x6f, 0x6e, 0x10, 0x01, 0x12, 0x15, 0x0a,
	0x11, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x4f, 0x70, 0x74, 0x5f, 0x45, 0x64, 0x69, 0x74, 0x4e, 0x61,
	0x6d, 0x65, 0x10, 0x02, 0x12, 0x17, 0x0a, 0x13, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x4f, 0x70, 0x74,
	0x5f, 0x45, 0x64, 0x69, 0x74, 0x4e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x10, 0x03, 0x12, 0x16, 0x0a,
	0x12, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x4f, 0x70, 0x74, 0x5f, 0x45, 0x64, 0x69, 0x74, 0x41, 0x70,
	0x70, 0x6c, 0x79, 0x10, 0x04, 0x12, 0x19, 0x0a, 0x15, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x4f, 0x70,
	0x74, 0x5f, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x4d, 0x67, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x10, 0x05,
	0x12, 0x1a, 0x0a, 0x16, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x4f, 0x70, 0x74, 0x5f, 0x41, 0x70, 0x70,
	0x6c, 0x79, 0x4d, 0x67, 0x72, 0x41, 0x67, 0x72, 0x65, 0x65, 0x10, 0x06, 0x12, 0x1b, 0x0a, 0x17,
	0x47, 0x75, 0x69, 0x6c, 0x64, 0x4f, 0x70, 0x74, 0x5f, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x4d, 0x67,
	0x72, 0x52, 0x65, 0x66, 0x75, 0x73, 0x65, 0x10, 0x07, 0x12, 0x1a, 0x0a, 0x16, 0x47, 0x75, 0x69,
	0x6c, 0x64, 0x4f, 0x70, 0x74, 0x5f, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x4d, 0x67, 0x72, 0x4c,
	0x69, 0x73, 0x74, 0x10, 0x08, 0x12, 0x1f, 0x0a, 0x1b, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x4f, 0x70,
	0x74, 0x5f, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x4d, 0x67, 0x72, 0x50, 0x72, 0x65, 0x73, 0x69,
	0x64, 0x65, 0x6e, 0x74, 0x10, 0x09, 0x12, 0x1a, 0x0a, 0x16, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x4f,
	0x70, 0x74, 0x5f, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x4d, 0x67, 0x72, 0x56, 0x69, 0x63, 0x65,
	0x10, 0x0a, 0x12, 0x1b, 0x0a, 0x17, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x4f, 0x70, 0x74, 0x5f, 0x4d,
	0x65, 0x6d, 0x62, 0x65, 0x72, 0x4d, 0x67, 0x72, 0x45, 0x6c, 0x69, 0x74, 0x65, 0x10, 0x0b, 0x12,
	0x1c, 0x0a, 0x18, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x4f, 0x70, 0x74, 0x5f, 0x4d, 0x65, 0x6d, 0x62,
	0x65, 0x72, 0x4d, 0x67, 0x72, 0x4e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x10, 0x0c, 0x12, 0x1a, 0x0a,
	0x16, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x4f, 0x70, 0x74, 0x5f, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72,
	0x4d, 0x67, 0x72, 0x4b, 0x69, 0x63, 0x6b, 0x10, 0x0d, 0x12, 0x11, 0x0a, 0x0d, 0x47, 0x75, 0x69,
	0x6c, 0x64, 0x4f, 0x70, 0x74, 0x5f, 0x51, 0x75, 0x69, 0x74, 0x10, 0x0e, 0x12, 0x14, 0x0a, 0x10,
	0x47, 0x75, 0x69, 0x6c, 0x64, 0x4f, 0x70, 0x74, 0x5f, 0x44, 0x69, 0x73, 0x6d, 0x69, 0x73, 0x73,
	0x10, 0x0f, 0x12, 0x14, 0x0a, 0x10, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x4f, 0x70, 0x74, 0x5f, 0x49,
	0x6d, 0x70, 0x65, 0x61, 0x63, 0x68, 0x10, 0x10, 0x12, 0x1c, 0x0a, 0x18, 0x47, 0x75, 0x69, 0x6c,
	0x64, 0x4f, 0x70, 0x74, 0x5f, 0x53, 0x65, 0x6e, 0x64, 0x57, 0x6f, 0x72, 0x6c, 0x64, 0x49, 0x6e,
	0x76, 0x69, 0x74, 0x65, 0x10, 0x11, 0x12, 0x1d, 0x0a, 0x19, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x4f,
	0x70, 0x74, 0x5f, 0x53, 0x65, 0x6e, 0x64, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x76,
	0x69, 0x74, 0x65, 0x10, 0x12, 0x12, 0x1d, 0x0a, 0x19, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x4f, 0x70,
	0x74, 0x5f, 0x42, 0x61, 0x72, 0x67, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x4e, 0x6f, 0x74, 0x69,
	0x63, 0x65, 0x10, 0x13, 0x12, 0x1d, 0x0a, 0x19, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x4f, 0x70, 0x74,
	0x5f, 0x45, 0x64, 0x69, 0x74, 0x41, 0x6e, 0x6e, 0x6f, 0x75, 0x6e, 0x63, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x10, 0x14, 0x12, 0x1e, 0x0a, 0x1a, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x4f, 0x70, 0x74, 0x5f,
	0x41, 0x70, 0x70, 0x6c, 0x79, 0x4d, 0x67, 0x72, 0x52, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x41, 0x6c,
	0x6c, 0x10, 0x15, 0x2a, 0x9a, 0x02, 0x0a, 0x0c, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x4c, 0x6f, 0x67,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x12, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x4c, 0x6f, 0x67,
	0x54, 0x79, 0x70, 0x65, 0x5f, 0x4c, 0x65, 0x61, 0x76, 0x65, 0x10, 0x00, 0x12, 0x15, 0x0a, 0x11,
	0x47, 0x75, 0x69, 0x6c, 0x64, 0x4c, 0x6f, 0x67, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x4a, 0x6f, 0x69,
	0x6e, 0x10, 0x01, 0x12, 0x19, 0x0a, 0x15, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x4c, 0x6f, 0x67, 0x54,
	0x79, 0x70, 0x65, 0x5f, 0x49, 0x6d, 0x70, 0x65, 0x61, 0x63, 0x68, 0x31, 0x10, 0x02, 0x12, 0x19,
	0x0a, 0x15, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x4c, 0x6f, 0x67, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x49,
	0x6d, 0x70, 0x65, 0x61, 0x63, 0x68, 0x32, 0x10, 0x03, 0x12, 0x18, 0x0a, 0x14, 0x47, 0x75, 0x69,
	0x6c, 0x64, 0x4c, 0x6f, 0x67, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x4d, 0x67, 0x72, 0x56, 0x69, 0x63,
	0x65, 0x10, 0x04, 0x12, 0x1a, 0x0a, 0x16, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x4c, 0x6f, 0x67, 0x54,
	0x79, 0x70, 0x65, 0x5f, 0x4d, 0x67, 0x72, 0x4e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x10, 0x05, 0x12,
	0x17, 0x0a, 0x13, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x4c, 0x6f, 0x67, 0x54, 0x79, 0x70, 0x65, 0x5f,
	0x44, 0x6f, 0x6e, 0x61, 0x74, 0x65, 0x10, 0x06, 0x12, 0x1a, 0x0a, 0x16, 0x47, 0x75, 0x69, 0x6c,
	0x64, 0x4c, 0x6f, 0x67, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x50, 0x72, 0x65, 0x73, 0x69, 0x64, 0x65,
	0x6e, 0x74, 0x10, 0x07, 0x12, 0x1c, 0x0a, 0x18, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x4c, 0x6f, 0x67,
	0x54, 0x79, 0x70, 0x65, 0x5f, 0x41, 0x75, 0x74, 0x6f, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x31,
	0x10, 0x08, 0x12, 0x1c, 0x0a, 0x18, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x4c, 0x6f, 0x67, 0x54, 0x79,
	0x70, 0x65, 0x5f, 0x41, 0x75, 0x74, 0x6f, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x32, 0x10, 0x09,
	0x2a, 0xb6, 0x02, 0x0a, 0x0f, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x17, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10,
	0x00, 0x12, 0x20, 0x0a, 0x1c, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x5f, 0x4a, 0x4f, 0x49, 0x4e, 0x45, 0x44, 0x5f, 0x47, 0x55, 0x49, 0x4c,
	0x44, 0x10, 0x01, 0x12, 0x1e, 0x0a, 0x1a, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x4c, 0x45, 0x46, 0x54, 0x5f, 0x47, 0x55, 0x49, 0x4c,
	0x44, 0x10, 0x02, 0x12, 0x25, 0x0a, 0x21, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x4b, 0x49, 0x43, 0x4b, 0x45, 0x44, 0x5f, 0x46, 0x52,
	0x4f, 0x4d, 0x5f, 0x47, 0x55, 0x49, 0x4c, 0x44, 0x10, 0x03, 0x12, 0x23, 0x0a, 0x1f, 0x47, 0x75,
	0x69, 0x6c, 0x64, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x47, 0x55,
	0x49, 0x4c, 0x44, 0x5f, 0x44, 0x49, 0x53, 0x4d, 0x49, 0x53, 0x53, 0x45, 0x44, 0x10, 0x04, 0x12,
	0x24, 0x0a, 0x20, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x5f, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x48, 0x41, 0x4e,
	0x47, 0x45, 0x44, 0x10, 0x05, 0x12, 0x22, 0x0a, 0x1e, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x47, 0x55, 0x49, 0x4c, 0x44, 0x5f, 0x4c,
	0x45, 0x56, 0x45, 0x4c, 0x5f, 0x55, 0x50, 0x10, 0x06, 0x12, 0x2e, 0x0a, 0x2a, 0x47, 0x75, 0x69,
	0x6c, 0x64, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x41, 0x50, 0x50,
	0x4c, 0x59, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x45, 0x44, 0x5f, 0x4f, 0x52, 0x5f,
	0x45, 0x58, 0x50, 0x49, 0x52, 0x45, 0x44, 0x10, 0x07, 0x2a, 0xba, 0x01, 0x0a, 0x16, 0x47, 0x75,
	0x69, 0x6c, 0x64, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x1e, 0x0a, 0x1a, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f,
	0x57, 0x4e, 0x10, 0x00, 0x12, 0x1e, 0x0a, 0x1a, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x45, 0x4e, 0x44, 0x49,
	0x4e, 0x47, 0x10, 0x01, 0x12, 0x1f, 0x0a, 0x1b, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x41, 0x50, 0x50, 0x52, 0x4f,
	0x56, 0x45, 0x44, 0x10, 0x02, 0x12, 0x1f, 0x0a, 0x1b, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x52, 0x45, 0x4a, 0x45,
	0x43, 0x54, 0x45, 0x44, 0x10, 0x03, 0x12, 0x1e, 0x0a, 0x1a, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x45, 0x58, 0x50,
	0x49, 0x52, 0x45, 0x44, 0x10, 0x04, 0x2a, 0x9a, 0x01, 0x0a, 0x1d, 0x47, 0x75, 0x69, 0x6c, 0x64,
	0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x17, 0x49, 0x4e, 0x54, 0x45,
	0x52, 0x4e, 0x41, 0x4c, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x4b, 0x4e,
	0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x29, 0x0a, 0x25, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41,
	0x4c, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x49, 0x53, 0x4d, 0x49, 0x53, 0x53,
	0x5f, 0x47, 0x55, 0x49, 0x4c, 0x44, 0x5f, 0x4d, 0x45, 0x4d, 0x42, 0x45, 0x52, 0x53, 0x10, 0x01,
	0x12, 0x31, 0x0a, 0x2d, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x5f, 0x41, 0x43, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x47, 0x55, 0x49, 0x4c, 0x44, 0x5f, 0x4c, 0x45, 0x56, 0x45, 0x4c, 0x5f,
	0x55, 0x50, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x59, 0x5f, 0x4d, 0x45, 0x4d, 0x42, 0x45, 0x52,
	0x53, 0x10, 0x02, 0x2a, 0x48, 0x0a, 0x08, 0x43, 0x68, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x12, 0x0a, 0x0e, 0x43, 0x68, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x57, 0x6f, 0x72, 0x6c,
	0x64, 0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e, 0x43, 0x68, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x5f,
	0x47, 0x75, 0x69, 0x6c, 0x64, 0x10, 0x01, 0x12, 0x14, 0x0a, 0x10, 0x43, 0x68, 0x61, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x5f, 0x50, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x10, 0x02, 0x2a, 0x58, 0x0a,
	0x0b, 0x43, 0x68, 0x61, 0x74, 0x4d, 0x73, 0x67, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x10,
	0x43, 0x68, 0x61, 0x74, 0x4d, 0x73, 0x67, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x54, 0x65, 0x78, 0x74,
	0x10, 0x00, 0x12, 0x1b, 0x0a, 0x17, 0x43, 0x68, 0x61, 0x74, 0x4d, 0x73, 0x67, 0x54, 0x79, 0x70,
	0x65, 0x5f, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x10, 0x01, 0x12,
	0x16, 0x0a, 0x12, 0x43, 0x68, 0x61, 0x74, 0x4d, 0x73, 0x67, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x53,
	0x79, 0x73, 0x74, 0x65, 0x6d, 0x10, 0x02, 0x2a, 0x60, 0x0a, 0x16, 0x49, 0x6e, 0x42, 0x61, 0x74,
	0x74, 0x6c, 0x65, 0x53, 0x68, 0x6f, 0x77, 0x57, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x17, 0x0a, 0x13, 0x55, 0x49, 0x5f, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x5f, 0x4e,
	0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x50, 0x6f, 0x70, 0x10, 0x00, 0x12, 0x15, 0x0a, 0x11, 0x55, 0x49,
	0x5f, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x5f, 0x53, 0x68, 0x6f, 0x70, 0x50, 0x6f, 0x70, 0x10,
	0x01, 0x12, 0x16, 0x0a, 0x12, 0x55, 0x49, 0x5f, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x5f, 0x4c,
	0x75, 0x63, 0x6b, 0x79, 0x50, 0x6f, 0x70, 0x10, 0x02, 0x2a, 0xd2, 0x01, 0x0a, 0x18, 0x47, 0x6c,
	0x6f, 0x62, 0x61, 0x6c, 0x42, 0x72, 0x6f, 0x61, 0x64, 0x63, 0x61, 0x73, 0x74, 0x45, 0x76, 0x65,
	0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x1d, 0x47, 0x6c, 0x6f, 0x62, 0x61, 0x6c,
	0x42, 0x72, 0x6f, 0x61, 0x64, 0x63, 0x61, 0x73, 0x74, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x5f, 0x4e, 0x6f, 0x6e, 0x65, 0x10, 0x00, 0x12, 0x33, 0x0a, 0x2f, 0x47, 0x6c, 0x6f,
	0x62, 0x61, 0x6c, 0x42, 0x72, 0x6f, 0x61, 0x64, 0x63, 0x61, 0x73, 0x74, 0x45, 0x76, 0x65, 0x6e,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x42, 0x61, 0x6e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x47,
	0x75, 0x69, 0x6c, 0x64, 0x42, 0x6f, 0x73, 0x73, 0x52, 0x61, 0x6e, 0x6b, 0x10, 0x01, 0x12, 0x2a,
	0x0a, 0x26, 0x47, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x42, 0x72, 0x6f, 0x61, 0x64, 0x63, 0x61, 0x73,
	0x74, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x43, 0x68, 0x67, 0x50, 0x6c,
	0x61, 0x79, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x10, 0x02, 0x12, 0x32, 0x0a, 0x2e, 0x47, 0x6c,
	0x6f, 0x62, 0x61, 0x6c, 0x42, 0x72, 0x6f, 0x61, 0x64, 0x63, 0x61, 0x73, 0x74, 0x45, 0x76, 0x65,
	0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x43, 0x68, 0x67, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x4e,
	0x61, 0x6d, 0x65, 0x41, 0x6e, 0x64, 0x4e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x10, 0x03, 0x2a, 0x42,
	0x0a, 0x0b, 0x53, 0x69, 0x67, 0x6e, 0x49, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x0c, 0x0a,
	0x08, 0x4e, 0x6f, 0x53, 0x69, 0x67, 0x6e, 0x49, 0x6e, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x53,
	0x69, 0x67, 0x6e, 0x65, 0x64, 0x49, 0x6e, 0x4e, 0x6f, 0x52, 0x65, 0x10, 0x01, 0x12, 0x13, 0x0a,
	0x0f, 0x53, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x49, 0x6e, 0x41, 0x6e, 0x64, 0x52, 0x65, 0x65, 0x64,
	0x10, 0x02, 0x2a, 0x71, 0x0a, 0x11, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64,
	0x75, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x08, 0x0a, 0x04, 0x53, 0x68, 0x6f, 0x70, 0x10,
	0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x46, 0x69, 0x72, 0x73, 0x74, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65,
	0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x43, 0x61, 0x72,
	0x64, 0x10, 0x02, 0x12, 0x0e, 0x0a, 0x0a, 0x47, 0x72, 0x61, 0x64, 0x65, 0x64, 0x46, 0x75, 0x6e,
	0x64, 0x10, 0x03, 0x12, 0x12, 0x0a, 0x0e, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x43, 0x61,
	0x72, 0x64, 0x4e, 0x65, 0x77, 0x10, 0x04, 0x12, 0x0c, 0x0a, 0x08, 0x54, 0x69, 0x6d, 0x65, 0x53,
	0x68, 0x6f, 0x70, 0x10, 0x05, 0x2a, 0x61, 0x0a, 0x0c, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1b, 0x0a, 0x17, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x55, 0x6e, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x65, 0x64,
	0x10, 0x00, 0x12, 0x19, 0x0a, 0x15, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x5f, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x65, 0x64, 0x10, 0x01, 0x12, 0x19, 0x0a,
	0x15, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x52, 0x65,
	0x63, 0x65, 0x69, 0x76, 0x65, 0x64, 0x10, 0x02, 0x2a, 0x8e, 0x01, 0x0a, 0x0b, 0x4d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x4d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x4d, 0x61, 0x69, 0x6e, 0x10, 0x00, 0x12, 0x15,
	0x0a, 0x11, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x44, 0x61,
	0x69, 0x6c, 0x79, 0x10, 0x01, 0x12, 0x18, 0x0a, 0x14, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x5f, 0x53, 0x65, 0x76, 0x65, 0x6e, 0x44, 0x61, 0x79, 0x10, 0x02, 0x12,
	0x1b, 0x0a, 0x17, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x41,
	0x63, 0x68, 0x69, 0x65, 0x76, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x10, 0x03, 0x12, 0x1b, 0x0a, 0x17,
	0x4d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x48, 0x65, 0x61, 0x76,
	0x65, 0x6e, 0x6c, 0x79, 0x44, 0x61, 0x6f, 0x10, 0x04, 0x2a, 0x42, 0x0a, 0x11, 0x44, 0x42, 0x47,
	0x61, 0x63, 0x68, 0x61, 0x42, 0x6f, 0x6e, 0x75, 0x73, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x0a,
	0x0a, 0x06, 0x4e, 0x6f, 0x50, 0x75, 0x6c, 0x6c, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x50, 0x75,
	0x6c, 0x6c, 0x65, 0x64, 0x4e, 0x6f, 0x52, 0x65, 0x10, 0x01, 0x12, 0x11, 0x0a, 0x0d, 0x50, 0x75,
	0x6c, 0x6c, 0x65, 0x64, 0x41, 0x6e, 0x64, 0x52, 0x65, 0x65, 0x64, 0x10, 0x02, 0x2a, 0xc3, 0x01,
	0x0a, 0x10, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x15, 0x0a, 0x11, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x43, 0x6f, 0x64, 0x65,
	0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x5f, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x10, 0x01, 0x12,
	0x22, 0x0a, 0x1e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x5f, 0x43, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x4e, 0x6f, 0x74, 0x5f, 0x45, 0x6e, 0x6f, 0x75, 0x67,
	0x68, 0x10, 0x02, 0x12, 0x1e, 0x0a, 0x1a, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x43, 0x6f, 0x64,
	0x65, 0x5f, 0x49, 0x74, 0x65, 0x6d, 0x5f, 0x4e, 0x6f, 0x74, 0x5f, 0x45, 0x6e, 0x6f, 0x75, 0x67,
	0x68, 0x10, 0x03, 0x12, 0x1a, 0x0a, 0x16, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x43, 0x6f, 0x64,
	0x65, 0x5f, 0x50, 0x41, 0x52, 0x41, 0x4d, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x04, 0x12,
	0x22, 0x0a, 0x1e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x5f, 0x43, 0x4f,
	0x4e, 0x46, 0x49, 0x47, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x41, 0x49, 0x4e,
	0x53, 0x10, 0x05, 0x2a, 0x34, 0x0a, 0x10, 0x46, 0x75, 0x6e, 0x63, 0x50, 0x72, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x08, 0x0a, 0x04, 0x4c, 0x6f, 0x63, 0x6b, 0x10,
	0x00, 0x12, 0x09, 0x0a, 0x05, 0x41, 0x77, 0x61, 0x72, 0x64, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07,
	0x41, 0x77, 0x61, 0x72, 0x64, 0x65, 0x64, 0x10, 0x02, 0x2a, 0x51, 0x0a, 0x15, 0x54, 0x72, 0x65,
	0x61, 0x73, 0x75, 0x72, 0x65, 0x47, 0x61, 0x63, 0x68, 0x61, 0x43, 0x6f, 0x73, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x12, 0x0a, 0x0e, 0x43, 0x4f, 0x53, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x4e, 0x4f, 0x4e, 0x45, 0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e, 0x43, 0x4f, 0x53, 0x54, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x49, 0x54, 0x45, 0x4d, 0x10, 0x01, 0x12, 0x10, 0x0a, 0x0c, 0x43, 0x4f,
	0x53, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x44, 0x10, 0x02, 0x42, 0x35, 0x5a, 0x27,
	0x6c, 0x69, 0x74, 0x65, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6c, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73,
	0x2f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0xaa, 0x02, 0x09, 0x47, 0x61, 0x6d, 0x65, 0x2e, 0x43,
	0x6f, 0x72, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_PublicEnum_proto_rawDescOnce sync.Once
	file_PublicEnum_proto_rawDescData = file_PublicEnum_proto_rawDesc
)

func file_PublicEnum_proto_rawDescGZIP() []byte {
	file_PublicEnum_proto_rawDescOnce.Do(func() {
		file_PublicEnum_proto_rawDescData = protoimpl.X.CompressGZIP(file_PublicEnum_proto_rawDescData)
	})
	return file_PublicEnum_proto_rawDescData
}

var file_PublicEnum_proto_enumTypes = make([]protoimpl.EnumInfo, 62)
var file_PublicEnum_proto_goTypes = []interface{}{
	(LoginByType)(0),                   // 0: LoginByType
	(MoneyType)(0),                     // 1: MoneyType
	(AwardItemType)(0),                 // 2: AwardItemType
	(FuncLockType)(0),                  // 3: FuncLockType
	(SceneDropType)(0),                 // 4: SceneDropType
	(RedDotType)(0),                    // 5: RedDotType
	(EGenderType)(0),                   // 6: EGenderType
	(AttributeType)(0),                 // 7: AttributeType
	(MonsterType)(0),                   // 8: MonsterType
	(BattleType)(0),                    // 9: BattleType
	(BattleState)(0),                   // 10: BattleState
	(InstanceState)(0),                 // 11: InstanceState
	(PlayerState)(0),                   // 12: PlayerState
	(AdRewardType)(0),                  // 13: AdRewardType
	(GachaType)(0),                     // 14: GachaType
	(EquipType)(0),                     // 15: EquipType
	(DropExpressType)(0),               // 16: DropExpressType
	(QualityType)(0),                   // 17: QualityType
	(MailType)(0),                      // 18: MailType
	(MailStateType)(0),                 // 19: MailStateType
	(MailReasonType)(0),                // 20: MailReasonType
	(GuideTriggerType)(0),              // 21: GuideTriggerType
	(NewGuideTriggerType)(0),           // 22: NewGuideTriggerType
	(NewGuideFunType)(0),               // 23: NewGuideFunType
	(SignInToType)(0),                  // 24: SignInToType
	(RewardStatus)(0),                  // 25: RewardStatus
	(RewardType)(0),                    // 26: RewardType
	(ItemType)(0),                      // 27: ItemType
	(ItemSubType_ChooseBox)(0),         // 28: ItemSubType_ChooseBox
	(ItemSubType_Drop)(0),              // 29: ItemSubType_Drop
	(ItemSubType_Devris)(0),            // 30: ItemSubType_Devris
	(BagItemType)(0),                   // 31: BagItemType
	(SystemShowErrorType)(0),           // 32: SystemShowErrorType
	(GiftPacksType)(0),                 // 33: GiftPacksType
	(GiftPacksBuyType)(0),              // 34: GiftPacksBuyType
	(ChargeType)(0),                    // 35: ChargeType
	(CommonBoxRewardType)(0),           // 36: CommonBoxRewardType
	(BattlePassType)(0),                // 37: BattlePassType
	(BattlePassMissionType)(0),         // 38: BattlePassMissionType
	(AwardState)(0),                    // 39: AwardState
	(ActivityType)(0),                  // 40: ActivityType
	(ActivityStatus)(0),                // 41: ActivityStatus
	(ItemAcquisitionState)(0),          // 42: ItemAcquisitionState
	(IronSourceADype)(0),               // 43: IronSourceADype
	(GuildPosition)(0),                 // 44: GuildPosition
	(GuildOpt)(0),                      // 45: GuildOpt
	(GuildLogType)(0),                  // 46: GuildLogType
	(GuildUpdateType)(0),               // 47: GuildUpdateType
	(GuildApplicationStatus)(0),        // 48: GuildApplicationStatus
	(GuildSystemInternalActionType)(0), // 49: GuildSystemInternalActionType
	(ChatType)(0),                      // 50: ChatType
	(ChatMsgType)(0),                   // 51: ChatMsgType
	(InBattleShowWindowType)(0),        // 52: InBattleShowWindowType
	(GlobalBroadcastEventType)(0),      // 53: GlobalBroadcastEventType
	(SignInState)(0),                   // 54: SignInState
	(PaymentModuleType)(0),             // 55: PaymentModuleType
	(MissionState)(0),                  // 56: MissionState
	(MissionType)(0),                   // 57: MissionType
	(DBGachaBonusState)(0),             // 58: DBGachaBonusState
	(ServerResultCode)(0),              // 59: ServerResultCode
	(FuncPreviewstate)(0),              // 60: FuncPreviewstate
	(TreasureGachaCostType)(0),         // 61: TreasureGachaCostType
}
var file_PublicEnum_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_PublicEnum_proto_init() }
func file_PublicEnum_proto_init() {
	if File_PublicEnum_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_PublicEnum_proto_rawDesc,
			NumEnums:      62,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_PublicEnum_proto_goTypes,
		DependencyIndexes: file_PublicEnum_proto_depIdxs,
		EnumInfos:         file_PublicEnum_proto_enumTypes,
	}.Build()
	File_PublicEnum_proto = out.File
	file_PublicEnum_proto_rawDesc = nil
	file_PublicEnum_proto_goTypes = nil
	file_PublicEnum_proto_depIdxs = nil
}
