﻿//*********************************************************
// Framework
// Author:  Jasen
// Date  :  2022-11-15
//*********************************************************

using System;
using System.IO;

namespace Aurora.Framework
{
    public interface IPacketDispatcher
    {
        public void OnRead(Packet packet, Session session);

        public void OnSend(Packet packet, Session session);

        public void Dispatch(Packet packet, Session session = null);

        public void DispatchLater(Packet packet);

        public void DiscardPacket(Packet packet);
    }

    public class PacketDispatcherManager : Singleton<PacketDispatcherManager>
    {
        private IPacketDispatcher m_pDispatcher = null;
        public IPacketDispatcher Dispatcher
        {
            get { return m_pDispatcher; }
        }

        public void SetDispatcher(IPacketDispatcher dispatcher)
        {
            m_pDispatcher = dispatcher;
        }

        public static void OnRead(Packet packet, Session session)
        {
            Instance.Dispatcher.OnRead(packet, session);
        }

        public static void OnSend(Packet packet, Session session)
        {
            Instance.Dispatcher.OnSend(packet, session);
        }

        public static void Dispatch(Packet packet, Session session = null)
        {
            Instance.Dispatcher.Dispatch(packet, session);
        }

        public static void DispatchLater(Packet packet)
        {
            Instance.Dispatcher.DispatchLater(packet);
        }

        public static void DiscardPacket(Packet packet)
        {
            Instance.Dispatcher.DiscardPacket(packet);
        }
    }
}
