﻿/*
 * @description: 线程管理
 * @Auther: Kimsee
 * @Date: 2023-05-17 14:32
 * 
*/

using System.Collections.Concurrent;
using Aurora.Framework;
using Aurora.Framework;
using YamlDotNet.Core;

namespace BattleServer.Server
{
    public enum ThreadType
    {
        MAIN_THREAD,
        COMMON_THREAD,
        EXCLUSIVE_THREAD
    }

    public class ThreadManager : GlobalManager<ThreadManager>
    {
        private ThreadObject m_MainThread = null;

        private List<ThreadData> m_CommonThreadDataList = new List<ThreadData>();

        private List<ThreadObject> m_ThreadList = new List<ThreadObject>();

        private ConcurrentQueue<Tuple<WorkUnitBase, ThreadType>> m_StartQueue = new ConcurrentQueue<Tuple<WorkUnitBase, ThreadType>>();

        private ConcurrentQueue<Tuple<WorkUnitBase, ThreadType>> m_StopQueue = new ConcurrentQueue<Tuple<WorkUnitBase, ThreadType>>();

        private List<ThreadObject> m_NeedDestroyThreadList = new List<ThreadObject>();

        private List<WorkUnitBase> m_HomelessWorkUnitList = new List<WorkUnitBase>();

        private sealed class ReferenceTotalMonitor
        {
            private string m_strTypeName;
            private int m_nTotal;
            private ValueMonitor m_Monitor;

            public ReferenceTotalMonitor(Type type)
            {
                const ulong nMinBaseLine = 64;
                const ulong nMaxIncreaseAmount = 1024;

                m_strTypeName = type.IsNested ? type.FullName : type.Name;
                m_nTotal = 0;
                m_Monitor = new ValueMonitor(nMinBaseLine, nMaxIncreaseAmount);
            }

            public void Reset()
            {
                m_nTotal = 0;
            }

            public void Add(int nCount)
            {
                m_nTotal += nCount;
            }

            public void Check()
            {
                if (m_Monitor.CheckCross(m_nTotal, out bool bAbove, out ulong nCrossLine))
                {
                    if (bAbove)
                    {
                        Log.Warning($"[Memory] {m_strTypeName} Reference Using Total Recently Rise Above {nCrossLine} !");
                    }
                    else
                    {
                        Log.Info($"[Memory] {m_strTypeName} Reference Using Total Recently Fall Below {nCrossLine} !");
                    }
                }
            }
        }

        private Dictionary<Type, ReferenceTotalMonitor> m_ReferenceTotalMonitorList = new Dictionary<Type, ReferenceTotalMonitor>();
        private Dictionary<Type, int> m_RemainReferenceCountList = new Dictionary<Type, int>();

        private ushort m_nCommonThreadIndex = 0;
        private byte m_nCommonThreadCount = 0;
        private ushort m_nCommonThreadNormalCount = 0;
        private int m_nTimeInMSFromLastCheckUnresponsiveness = 0;
        private int m_nTimeInMSFromLastLoadBalancing = 0;
        private int m_nTimeInMSFromLastCheckReferenceTotal = 0;
        private bool m_bNeedTransferWorkUnit = false;

        private static int s_nMaxDeltaTimeInMS = 200;
        private static int s_nCheckUnresponsivenessIntervalInMS = 2000;
        private static byte s_nMaxUnresponsiveCount = 30;
        private static int s_nLoadBalancingIntervalInMS = 10000;
        private static int s_nCheckReferenceTotalIntervalInMS = 10000;

        public ThreadManager()
        {
            LogName = $"GlobalManager_ThreadManager";
        }

        public override void OnDestroy()
        {
            Release();

            base.OnDestroy();
        }

        public void Init(ThreadObject mainThread, byte nCommonThreadCount)
        {
            Log.Info("ThreadManager init begin.");
            {
                m_MainThread = mainThread;
                m_ThreadList.Add(m_MainThread);

                m_nCommonThreadIndex = 0;
                m_nCommonThreadCount = nCommonThreadCount;

                for (int nIndex = 0; nIndex < m_nCommonThreadCount; ++nIndex)
                {
                    CreateCommonThread();
                }

                m_nCommonThreadNormalCount = m_nCommonThreadCount;
                m_nTimeInMSFromLastCheckUnresponsiveness = 0;
                m_nTimeInMSFromLastLoadBalancing = 0;
                m_nTimeInMSFromLastCheckReferenceTotal = 0;
                m_bNeedTransferWorkUnit = false;
            }
            Log.Info("ThreadManager init end.");
        }

        public void Release()
        {
            m_StartQueue.Clear();
            m_StopQueue.Clear();
            m_NeedDestroyThreadList.Clear();
            m_HomelessWorkUnitList.Clear();
            m_ReferenceTotalMonitorList.Clear();
            m_RemainReferenceCountList.Clear();
            m_CommonThreadDataList.Clear();

            foreach (ThreadObject threadObject in m_ThreadList)
            {
                if (threadObject is not MainThread)
                {
                    threadObject.Stop();
                }
            }
            m_ThreadList.Clear();

            m_MainThread = null;
        }

        private void CreateCommonThread()
        {
            ThreadObject threadObject = new ThreadObject();
            m_CommonThreadDataList.Add(new ThreadData(threadObject));
            m_ThreadList.Add(threadObject);
            string strUniqueName = $"CommonThread_{m_nCommonThreadIndex++}";
            threadObject.Start(strUniqueName);
        }

        private ThreadObject CreateExclusiveThread(string strOwnerName)
        {
            ThreadObject threadObject = new ThreadObject();
            m_ThreadList.Add(threadObject);
            string strUniqueName = $"ExclusiveThread_{strOwnerName}";
            threadObject.Start(strUniqueName);

            return threadObject;
        }

        private void DestroyCommonThread(ThreadObject threadObject)
        {
            foreach (ThreadData threadData in m_CommonThreadDataList)
            {
                if (threadData.m_ThreadObject == threadObject)
                {
                    RecordThreadReferenceCount(threadObject);
                    threadObject.Stop();
                    m_ThreadList.Remove(threadObject);
                    m_CommonThreadDataList.Remove(threadData);
                    return;
                }
            }
        }

        private void DestroyExclusiveThread(ThreadObject threadObject)
        {
            RecordThreadReferenceCount(threadObject);
            threadObject.Stop();
            m_ThreadList.Remove(threadObject);
        }

        public override void Tick(int nDeltaTime)
        {
            base.Tick(nDeltaTime);

            while (m_StartQueue.TryDequeue(out Tuple<WorkUnitBase, ThreadType> tuple))
            {
                WorkUnitBase workUnit = tuple.Item1;
                ThreadType eThreadType = tuple.Item2;

                if (workUnit.State == WorkUnitBase.EState.INITIAL)
                {
                    ThreadObject threadObject = null;
                    {
                        if (eThreadType == ThreadType.COMMON_THREAD)
                        {
                            threadObject = GetIdleThread();
                            if (threadObject == null)
                            {
                                Log.Error($"Can Not Find Idle Thread For New WorkUnit {workUnit.Owner.LogName}!");
                            }
                        }
                        else if (eThreadType == ThreadType.EXCLUSIVE_THREAD)
                        {
                            threadObject = CreateExclusiveThread(workUnit.Owner.LogName);
                            if (threadObject == null)
                            {
                                Log.Error($"Can Not Create Exclusive Thread For New WorkUnit {workUnit.Owner.LogName}!");
                            }
                        }
                    }

                    if (threadObject != null)
                    {
                        workUnit.Start();
                        threadObject.AddWorkUnit(workUnit);
                    }
                }
                else
                {
                    Log.Error($"WorkUnit {workUnit.ID} Can Not Start Twice!");
                }
            }

            while (m_StopQueue.TryDequeue(out Tuple<WorkUnitBase, ThreadType> tuple))
            {
                WorkUnitBase workUnit = tuple.Item1;
                ThreadType eThreadType = tuple.Item2;

                WorkUnitBase.EState eWorkUnitState = workUnit.State;
                if (eWorkUnitState == WorkUnitBase.EState.RUN)
                {
                    ThreadObject threadObject = workUnit.OwnThreadObject;

                    threadObject.RemoveWorkUnit(workUnit, true);

                    if (eThreadType == ThreadType.EXCLUSIVE_THREAD)
                    {
                        DestroyExclusiveThread(threadObject);
                    }
                }
                else if (eWorkUnitState == WorkUnitBase.EState.SHUT_DOWN)
                {
                    Log.Error($"WorkUnit {workUnit.ID} Can Not Stop Twice!");
                }
                else
                {
                    m_StopQueue.Enqueue(tuple);
                    break;
                }
            }

            if (nDeltaTime <= s_nMaxDeltaTimeInMS)
            {
                m_nTimeInMSFromLastCheckUnresponsiveness += nDeltaTime;
                if (m_nTimeInMSFromLastCheckUnresponsiveness >= s_nCheckUnresponsivenessIntervalInMS)
                {
                    m_nTimeInMSFromLastCheckUnresponsiveness = 0;
                    CheckUnresponsiveness();
                }

                m_nTimeInMSFromLastLoadBalancing += nDeltaTime;
                if (m_nTimeInMSFromLastLoadBalancing >= s_nLoadBalancingIntervalInMS)
                {
                    m_nTimeInMSFromLastLoadBalancing = 0;
                    LoadBalancing();
                }

                m_nTimeInMSFromLastCheckReferenceTotal += nDeltaTime;
                if (m_nTimeInMSFromLastCheckReferenceTotal >= s_nCheckReferenceTotalIntervalInMS)
                {
                    m_nTimeInMSFromLastCheckReferenceTotal = 0;
                    CheckReferenceTotal();
                }
            }
            else
            {
                m_nTimeInMSFromLastCheckUnresponsiveness = 0;
                m_nTimeInMSFromLastLoadBalancing = 0;
                m_nTimeInMSFromLastCheckReferenceTotal = 0;
            }
        }

        public override void OnStart()
        {
            base.OnStart();
        }

        public override void OnStop()
        {
            base.OnStop();
        }

        // Caution! Please Do Not Start The Same WorkUnit On Different Threads Or Multiple Times.
        public void StartWorkUnit(WorkUnitBase workUnit, ThreadType eThreadType)
        {
            if (workUnit != null)
            {
                if (workUnit.State == WorkUnitBase.EState.INITIAL)
                {
                    if (eThreadType == ThreadType.MAIN_THREAD)
                    {
                        if (m_MainThread != null)
                        {
                            workUnit.Start();
                            m_MainThread.AddWorkUnit(workUnit);
                        }
                        else
                        {
                            Log.Error($"Main Thread Is Not Found!");
                        }
                    }
                    else
                    {
                        m_StartQueue.Enqueue(new Tuple<WorkUnitBase, ThreadType>(workUnit, eThreadType));
                    }
                }
                else
                {
                    Log.Error($"WorkUnit {workUnit.ID} Can Not Start Twice!");
                }
            }
        }

        // Caution! Please Do Not Stop The Same WorkUnit On Different Threads Or Multiple Times.
        public void StopWorkUnit(WorkUnitBase workUnit, ThreadType eThreadType)
        {
            if (workUnit != null)
            {
                if (workUnit.State != WorkUnitBase.EState.SHUT_DOWN)
                {
                    if (eThreadType == ThreadType.MAIN_THREAD)
                    {
                        if (m_MainThread != null)
                        {
                            m_MainThread.RemoveWorkUnit(workUnit);
                        }
                        else
                        {
                            Log.Error($"Main Thread Is Not Found!");
                        }
                    }
                    else
                    {
                        m_StopQueue.Enqueue(new Tuple<WorkUnitBase, ThreadType>(workUnit, eThreadType));
                    }
                }
                else
                {
                    Log.Error($"WorkUnit {workUnit.ID} Can Not Stop Twice!");
                }
            }
        }

        private ThreadObject GetIdleThread()
        {
            ThreadObject threadObject = null;
            if (m_nCommonThreadNormalCount > 0)
            {
                int nThreadIndex = (int)(RandomGenerator.RandUInt32() % m_nCommonThreadNormalCount);
                ThreadData threadData = m_CommonThreadDataList[nThreadIndex];
                if (threadData.m_eState == ThreadObject.EState.NORMAL)
                {
                    threadObject = threadData.m_ThreadObject;
                }
                else
                {
                    Log.Error("Selected Thread Is Not In The State Of Normal!");
                }
            }
            else
            {
                Log.Error("There Is No Common Thread In The State Of Normal!");
            }
            return threadObject;
        }

        private void Transfer(WorkUnitBase workUnit, ThreadObject sourceThread, ThreadObject targetThread)
        {
            if (targetThread != sourceThread)
            {
                if (workUnit.State == WorkUnitBase.EState.RUN)
                {
                    if (workUnit.OwnThreadObject == sourceThread)
                    {
                        sourceThread.RemoveWorkUnit(workUnit);
                        targetThread.AddWorkUnit(workUnit);

                        long nWorkUnitLoadInMicroseconds = WorkUnitBase.TicksToMicroseconds(workUnit.RecentTotalTicks / WorkUnitBase.PerformanceSampleHoldCount);
                        long nSourceThreadLoadInMicroseconds = WorkUnitBase.TicksToMicroseconds(sourceThread.RecentTotalTicks / WorkUnitBase.PerformanceSampleHoldCount);
                        long nTargetThreadLoadInMicroseconds = WorkUnitBase.TicksToMicroseconds(targetThread.RecentTotalTicks / WorkUnitBase.PerformanceSampleHoldCount);
                        Log.Warning($"[LoadBalancing] WorkUnit {workUnit.ID} (Load = {nWorkUnitLoadInMicroseconds} Microseconds) Is Transfered From Thread {sourceThread.ID} (Load = {nSourceThreadLoadInMicroseconds} Microseconds) To Thread {targetThread.ID} (Load = {nTargetThreadLoadInMicroseconds} Microseconds).");
                    }
                }
            }
        }

        private sealed class ThreadData
        {
            public ThreadObject m_ThreadObject;
            public ThreadObject.EState m_eState;
            public long m_nRecentTicks;
            public ushort m_nRunSerialNumber;
            public byte m_nUnresponsiveCount;

            public ThreadData(ThreadObject threadObject)
            {
                m_ThreadObject = threadObject;
                m_eState = m_ThreadObject.State;
                m_nRecentTicks = m_ThreadObject.RecentTotalTicks;
                m_nRunSerialNumber = m_ThreadObject.RunSerialNumber;
                m_nUnresponsiveCount = 0;
            }

            public void Refresh()
            {
                m_eState = m_ThreadObject.State;
                m_nRecentTicks = m_ThreadObject.RecentTotalTicks;
            }

            public static int Compare(ThreadData x, ThreadData y)
            {
                if ((x.m_eState == ThreadObject.EState.NORMAL) && (y.m_eState == ThreadObject.EState.NORMAL))
                {
                    if (x.m_nRecentTicks < y.m_nRecentTicks)
                    {
                        return -1;
                    }
                    else if (x.m_nRecentTicks > y.m_nRecentTicks)
                    {
                        return 1;
                    }
                    else
                    {
                        return 0;
                    }
                }
                else if (x.m_eState == ThreadObject.EState.NORMAL)
                {
                    return -1;
                }
                else if (y.m_eState == ThreadObject.EState.NORMAL)
                {
                    return 1;
                }
                else
                {
                    return 0;
                }
            }
        }

        private void RefreshCommonThreadData()
        {
            ushort nCommonThreadNormalCount = 0;
            foreach (ThreadData threadData in m_CommonThreadDataList)
            {
                threadData.Refresh();
                if (threadData.m_eState == ThreadObject.EState.NORMAL)
                {
                    ++nCommonThreadNormalCount;
                }
            }
            while (nCommonThreadNormalCount < m_nCommonThreadCount)
            {
                CreateCommonThread();
                ++nCommonThreadNormalCount;
            }
            m_nCommonThreadNormalCount = nCommonThreadNormalCount;
            m_CommonThreadDataList.Sort(ThreadData.Compare);
        }

        //[PerformanceAspect]
        private void CheckUnresponsiveness()
        {
            m_NeedDestroyThreadList.Clear();
            m_HomelessWorkUnitList.Clear();

            foreach (ThreadData threadData in m_CommonThreadDataList)
            {
                ThreadObject threadObject = threadData.m_ThreadObject;
                if (threadObject != null)
                {
                    if (threadObject.RunSerialNumber == threadData.m_nRunSerialNumber)
                    {
                        if (threadData.m_eState == ThreadObject.EState.NORMAL)
                        {
                            threadObject.TryToRescueOtherWorkUnits(threadData.m_nRunSerialNumber, m_HomelessWorkUnitList);
                        }
                        else if (threadData.m_eState == ThreadObject.EState.TEMPORARILY_UNRESPONSIVE)
                        {
                            if (++threadData.m_nUnresponsiveCount >= s_nMaxUnresponsiveCount)
                            {
                                if (threadObject.TryToAbortThread())
                                {
                                    m_NeedDestroyThreadList.Add(threadObject);
                                }
                            }
                        }
                    }
                    else
                    {
                        threadData.m_nRunSerialNumber = threadObject.RunSerialNumber;
                        threadData.m_nUnresponsiveCount = 0;
                    }
                }
            }

            foreach (ThreadObject threadObject in m_NeedDestroyThreadList)
            {
                DestroyCommonThread(threadObject);
            }

            RefreshCommonThreadData();

            foreach (WorkUnitBase workUnit in m_HomelessWorkUnitList)
            {
                ThreadObject idleThreadObject = GetIdleThread();
                if (idleThreadObject != null)
                {
                    idleThreadObject.AddWorkUnit(workUnit);
                }
                else
                {
                    Log.Error($"Can Not Find Idle Thread For Homeless WorkUnit {workUnit.ID}!");
                }
            }
        }

        //[PerformanceAspect]
        private void LoadBalancing()
        {
            RefreshCommonThreadData();

            if (m_nCommonThreadNormalCount > 1)
            {
                bool bNeedTransfer = false;
                {
                    ushort nLowIndex = 0;
                    ushort nHighIndex = (ushort)(m_nCommonThreadNormalCount - 1);
                    while (nLowIndex < nHighIndex)
                    {
                        ThreadObject lowLoadThread = m_CommonThreadDataList[nLowIndex].m_ThreadObject;
                        ThreadObject highLoadThread = m_CommonThreadDataList[nHighIndex].m_ThreadObject;
                        ThreadObject.EState lowLoadThreadState = m_CommonThreadDataList[nLowIndex].m_eState;
                        ThreadObject.EState highLoadThreadState = m_CommonThreadDataList[nHighIndex].m_eState;
                        if ((lowLoadThread != null) && (highLoadThread != null) && (lowLoadThread != highLoadThread))
                        {
                            if ((lowLoadThreadState == ThreadObject.EState.NORMAL) && (highLoadThreadState == ThreadObject.EState.NORMAL))
                            {
                                long nLowTotalTicks = lowLoadThread.RecentTotalTicks;
                                long nHighTotalTicks = highLoadThread.RecentTotalTicks;

                                long nToleranceTicks = 0;
                                {
                                    const float fMinToleranceRatio = 0.05f;
                                    nToleranceTicks = (long)(nHighTotalTicks * fMinToleranceRatio);

                                    long nMinToleranceTimeInMicroseconds = (5 * 1000) * WorkUnitBase.PerformanceSampleHoldCount;
                                    long nMinToleranceTicks = WorkUnitBase.MicrosecondsToTicks(nMinToleranceTimeInMicroseconds);

                                    if (nToleranceTicks < nMinToleranceTicks)
                                    {
                                        nToleranceTicks = nMinToleranceTicks;
                                    }
                                }

                                if (Math.Abs(nHighTotalTicks - nLowTotalTicks) > nToleranceTicks)
                                {
                                    WorkUnitBase lowMinWorkUnit = lowLoadThread.MinLoadWorkUnit;
                                    WorkUnitBase lowMaxWorkUnit = lowLoadThread.MaxLoadWorkUnit;
                                    WorkUnitBase highMinWorkUnit = highLoadThread.MinLoadWorkUnit;
                                    WorkUnitBase highMaxWorkUnit = highLoadThread.MaxLoadWorkUnit;

                                    bool bLowMinTransfer = false;
                                    bool bLowMaxTransfer = false;
                                    bool bHighMinTransfer = false;
                                    bool bHighMaxTransfer = false;
                                    {
                                        long nMinDifferenceTicks = Math.Abs(nHighTotalTicks - nLowTotalTicks);

                                        long nLowMinTicks = ((lowMinWorkUnit != null) && (lowMinWorkUnit != lowMaxWorkUnit)) ? lowMinWorkUnit.RecentTotalTicks : 0;
                                        long nLowMaxTicks = (lowMaxWorkUnit != null) ? lowMaxWorkUnit.RecentTotalTicks : 0;
                                        long nHighMinTicks = ((highMinWorkUnit != null) && (highMinWorkUnit != highMaxWorkUnit)) ? highMinWorkUnit.RecentTotalTicks : 0;
                                        long nHighMaxTicks = (highMaxWorkUnit != null) ? highMaxWorkUnit.RecentTotalTicks : 0;

                                        bool[] tryOrNot = { false, true };
                                        bool[] notTry = { false };
                                        bool[] lowMinOptions = (nLowMinTicks > 0) ? tryOrNot : notTry;
                                        bool[] lowMaxOptions = (nLowMaxTicks > 0) ? tryOrNot : notTry;
                                        bool[] highMinOptions = (nHighMinTicks > 0) ? tryOrNot : notTry;
                                        bool[] highMaxOptions = (nHighMaxTicks > 0) ? tryOrNot : notTry;
                                        foreach (bool bLowMinTry in lowMinOptions)
                                        {
                                            foreach (bool bLowMaxTry in lowMaxOptions)
                                            {
                                                foreach (bool bHighMinTry in highMinOptions)
                                                {
                                                    foreach (bool bHighMaxTry in highMaxOptions)
                                                    {
                                                        long nLowLaterTicks = nLowTotalTicks;
                                                        long nHighLaterTicks = nHighTotalTicks;
                                                        if (bLowMinTry)
                                                        {
                                                            nLowLaterTicks -= nLowMinTicks;
                                                            nHighLaterTicks += nLowMinTicks;
                                                        }
                                                        if (bLowMaxTry)
                                                        {
                                                            nLowLaterTicks -= nLowMaxTicks;
                                                            nHighLaterTicks += nLowMaxTicks;
                                                        }
                                                        if (bHighMinTry)
                                                        {
                                                            nLowLaterTicks += nHighMinTicks;
                                                            nHighLaterTicks -= nHighMinTicks;
                                                        }
                                                        if (bHighMaxTry)
                                                        {
                                                            nLowLaterTicks += nHighMaxTicks;
                                                            nHighLaterTicks -= nHighMaxTicks;
                                                        }

                                                        long nDifferenceTicks = Math.Abs(nHighLaterTicks - nLowLaterTicks);
                                                        if (nDifferenceTicks < nMinDifferenceTicks)
                                                        {
                                                            nMinDifferenceTicks = nDifferenceTicks;

                                                            bLowMinTransfer = bLowMinTry;
                                                            bLowMaxTransfer = bLowMaxTry;
                                                            bHighMinTransfer = bHighMinTry;
                                                            bHighMaxTransfer = bHighMaxTry;
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }

                                    if (bLowMinTransfer || bLowMaxTransfer || bHighMinTransfer || bHighMaxTransfer)
                                    {
                                        if (bLowMinTransfer)
                                        {
                                            Transfer(lowMinWorkUnit, lowLoadThread, highLoadThread);
                                        }
                                        if (bLowMaxTransfer)
                                        {
                                            Transfer(lowMaxWorkUnit, lowLoadThread, highLoadThread);
                                        }
                                        if (bHighMinTransfer)
                                        {
                                            Transfer(highMinWorkUnit, highLoadThread, lowLoadThread);
                                        }
                                        if (bHighMaxTransfer)
                                        {
                                            Transfer(highMaxWorkUnit, highLoadThread, lowLoadThread);
                                        }

                                        ++nLowIndex;
                                        --nHighIndex;

                                        bNeedTransfer = true;
                                    }
                                    else
                                    {
                                        --nHighIndex;
                                    }
                                }
                                else
                                {
                                    break;
                                }
                            }
                            else
                            {
                                if (lowLoadThreadState != ThreadObject.EState.NORMAL)
                                {
                                    Log.Error($"Low Load Thread {lowLoadThread.ID} Is Not In The State Of Normal!");
                                    ++nLowIndex;
                                }
                                if (highLoadThreadState != ThreadObject.EState.NORMAL)
                                {
                                    Log.Error($"High Load Thread {highLoadThread.ID} Is Not In The State Of Normal!");
                                    --nHighIndex;
                                }
                            }
                        }
                        else
                        {
                            Log.Error($"Common Thread Load List Is Invalid!");
                            break;
                        }
                    }
                }

                if (m_bNeedTransferWorkUnit && !bNeedTransfer)
                {
                    LogCommonThreadLoad();
                }
                m_bNeedTransferWorkUnit = bNeedTransfer;
            }
        }

        private void LogCommonThreadLoad()
        {
            if (m_nCommonThreadNormalCount > 0)
            {
                long nSum = 0;
                long nSquareSum = 0;
                long nMinimumValue = long.MaxValue;
                long nMaximumValue = long.MinValue;

                for (int nIndex = 0; nIndex < m_nCommonThreadNormalCount; ++nIndex)
                {
                    long nThreadLoadInMicroseconds = WorkUnitBase.TicksToMicroseconds(m_CommonThreadDataList[nIndex].m_nRecentTicks / WorkUnitBase.PerformanceSampleHoldCount);
                    nSum += nThreadLoadInMicroseconds;
                    nSquareSum += nThreadLoadInMicroseconds * nThreadLoadInMicroseconds;
                    if (nThreadLoadInMicroseconds < nMinimumValue)
                    {
                        nMinimumValue = nThreadLoadInMicroseconds;
                    }
                    if (nThreadLoadInMicroseconds > nMaximumValue)
                    {
                        nMaximumValue = nThreadLoadInMicroseconds;
                    }
                }

                long nMeanValue = nSum / m_nCommonThreadNormalCount;
                long nSquareMeanValue = nSquareSum / m_nCommonThreadNormalCount;
                long nMeanValueSquare = nMeanValue * nMeanValue;
                long nVariance = nSquareMeanValue - nMeanValueSquare;
                long nStandardDeviation = (nVariance > 0) ? (long)Math.Sqrt(nVariance) : 0;

                Log.Warning($"[LoadBalancing] All Common Thread (In Normal State) Loads Have Just Stabilized, Minimum Value Is {nMinimumValue} Microseconds, Maximum Value Is {nMaximumValue} Microseonds, Mean Value Is {nMeanValue} Microseconds, Standard Deviation Is {nStandardDeviation} Microseconds.");
            }
        }

        //[PerformanceAspect]
        private void CheckReferenceTotal()
        {
            foreach (ReferenceTotalMonitor monitor in m_ReferenceTotalMonitorList.Values)
            {
                monitor.Reset();
            }

            foreach (KeyValuePair<Type, int> keyValuePair in m_RemainReferenceCountList)
            {
                ReferenceTotalMonitor monitor = null;
                if (!m_ReferenceTotalMonitorList.TryGetValue(keyValuePair.Key, out monitor))
                {
                    monitor = new ReferenceTotalMonitor(keyValuePair.Key);
                    m_ReferenceTotalMonitorList.Add(keyValuePair.Key, monitor);
                }
                monitor.Add(keyValuePair.Value);
            }

            foreach (ThreadObject threadObject in m_ThreadList)
            {
                Dictionary<Type, int> referenceCountList = threadObject.ReferenceCountList;
                if (referenceCountList != null)
                {
                    foreach (KeyValuePair<Type, int> keyValuePair in referenceCountList)
                    {
                        ReferenceTotalMonitor monitor = null;
                        if (!m_ReferenceTotalMonitorList.TryGetValue(keyValuePair.Key, out monitor))
                        {
                            monitor = new ReferenceTotalMonitor(keyValuePair.Key);
                            m_ReferenceTotalMonitorList.Add(keyValuePair.Key, monitor);
                        }
                        monitor.Add(keyValuePair.Value);
                    }
                }
                else
                {
                    Log.Error($"Thread Reference Count List Is Not Ready!");
                    return;
                }
            }

            foreach (ReferenceTotalMonitor monitor in m_ReferenceTotalMonitorList.Values)
            {
                monitor.Check();
            }
        }

        private void RecordThreadReferenceCount(ThreadObject threadObject)
        {
            Dictionary<Type, int> referenceCountList = threadObject.GetReferenceCountListForcibly();
            foreach (KeyValuePair<Type, int> keyValuePair in referenceCountList)
            {
                if (!m_RemainReferenceCountList.ContainsKey(keyValuePair.Key))
                {
                    m_RemainReferenceCountList.Add(keyValuePair.Key, 0);
                }
                m_RemainReferenceCountList[keyValuePair.Key] += keyValuePair.Value;
            }
        }
    }
}
