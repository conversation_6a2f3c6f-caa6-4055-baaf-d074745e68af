﻿using Aurora.Framework;
using Aurora.Framework;
using BattleServer.Server;
using System.Collections.Concurrent;
using System.Diagnostics;

namespace BattleServer.Server
{
    public interface IWorkUnitOwner
    {
        string LogName
        {
            get;
            set;
        }

        WorkUnitBase WorkUnit
        {
            get;
            set;
        }

        void Tick(int nDeltaTime);

        void InitWorkUnit();

        void ClearWorkUnit();

        void OnStart();

        void OnStop();

        void OnDestroy();

        void OnException(Exception e);
    }

    public class WorkUnitBase : IReference
    {
        public enum EState
        {
            INITIAL = 0,
            SLEEP = 1,
            RUN = 2,
            LEAVE = 3,
            SHUT_DOWN = 4
        }

        private uint m_nID;
        public ThreadSyncContext m_ThreadSyncContext = new ThreadSyncContext();
        private ThreadObject m_Thread;
        private IWorkUnitOwner m_Owner;
        private string m_strOwnerTickMethodName;
        private EState m_eState = EState.INITIAL;
        //private EntitySystem m_EntitySystem = new EntitySystem();
        private ConcurrentQueue<Packet> m_PacketQueue = new ConcurrentQueue<Packet>();

        public uint ID => m_nID;

        public IWorkUnitOwner Owner => m_Owner;

        #region 帧消耗时间
        public int FrameTime => m_FrameTime;
        private int m_FrameTime;
        private long m_LastClientTime;
        #endregion

        private static int s_nStandardUpdateIntervalInMS = 100;
        private static int s_nUpdateIntervalDisturbanceInMS = 10;
        private int m_nUpdateIntervalInMS;
        private Stopwatch m_OuterStopwatch = new Stopwatch();
        private Stopwatch m_InnerStopwatch = new Stopwatch();

        private static int s_nPacketDispatchMaxTimeInMS = 10;
        private Stopwatch m_PacketStopwatch = new Stopwatch();
        private static ulong s_nPacketLeftCountMinBaseLine = 16;
        private static ulong s_nPacketLeftCountMaxIncreaseAmount = 128;
        private ValueMonitor m_PacketLeftCountMonitor = new ValueMonitor(s_nPacketLeftCountMinBaseLine, s_nPacketLeftCountMaxIncreaseAmount);

        private static ushort s_nPerformanceSampleHoldCount = 100;
        private static long s_nMicrosecondsPerSecond = 1000 * 1000;
        private static double s_fTicksToMicrosecondsFactor = (double)s_nMicrosecondsPerSecond / Stopwatch.Frequency;
        private static double s_fMicrosecondsToTicksFactor = (double)Stopwatch.Frequency / s_nMicrosecondsPerSecond;

        private StatisticalList m_TicksStatisticalList = new StatisticalList(PerformanceSampleHoldCount);

        private bool m_bPerformanceEnabled = false;

        private static ulong s_nLastTicksMinBaseLine = (ulong)MicrosecondsToTicks(50 * 1000);
        private static ulong s_nLastTicksMaxIncreaseAmount = (ulong)MicrosecondsToTicks((long)60 * 60 * 1000 * 1000);
        private static ulong s_nAverageTicksMinBaseLine = (ulong)MicrosecondsToTicks(5 * 1000);
        private static ulong s_nAverageTicksMaxIncreaseAmount = (ulong)MicrosecondsToTicks((long)60 * 60 * 1000 * 1000);
        private ValueMonitor m_LastTicksMonitor = new ValueMonitor(s_nLastTicksMinBaseLine, s_nLastTicksMaxIncreaseAmount);
        private ValueMonitor m_AverageTicksMonitor = new ValueMonitor(s_nAverageTicksMinBaseLine, s_nAverageTicksMaxIncreaseAmount);

        private sealed class MethodPerformanceData : IReference
        {
            private uint m_nWorkUnitID;
            private string m_strMethodName;

            private long m_nCallCount = 0;
            private long m_nTicks = 0;
            private StatisticalList m_CallStatisticalList = new StatisticalList(PerformanceSampleHoldCount);
            private StatisticalList m_TicksStatisticalList = new StatisticalList(PerformanceSampleHoldCount);

            private static ulong s_nLastTicksMinBaseLine = (ulong)MicrosecondsToTicks(25 * 1000);
            private static ulong s_nLastTicksMaxIncreaseAmount = (ulong)MicrosecondsToTicks((long)60 * 60 * 1000 * 1000);
            private static ulong s_nAverageTicksMinBaseLine = (ulong)MicrosecondsToTicks(2500);
            private static ulong s_nAverageTicksMaxIncreaseAmount = (ulong)MicrosecondsToTicks((long)60 * 60 * 1000 * 1000);
            private ValueMonitor m_LastTicksMonitor = new ValueMonitor(s_nLastTicksMinBaseLine, s_nLastTicksMaxIncreaseAmount);
            private ValueMonitor m_AverageTicksMonitor = new ValueMonitor(s_nAverageTicksMinBaseLine, s_nAverageTicksMaxIncreaseAmount);

            public string MethodName
            {
                get
                {
                    return m_strMethodName;
                }
            }

            public long LastCallCount
            {
                get
                {
                    return m_CallStatisticalList.LastSample;
                }
            }

            public long LastTicks
            {
                get
                {
                    return m_TicksStatisticalList.LastSample;
                }
            }

            public long LastTicksEachCall
            {
                get
                {
                    if (LastCallCount > 0)
                    {
                        return LastTicks / LastCallCount;
                    }
                    else
                    {
                        return 0;
                    }
                }
            }

            public float AverageCallCount
            {
                get
                {
                    return m_CallStatisticalList.RecentAverageFloat;
                }
            }

            public long AverageTicks
            {
                get
                {
                    return m_TicksStatisticalList.RecentAverage;
                }
            }

            public long AverageTicksEachCall
            {
                get
                {
                    if (m_CallStatisticalList.RecentTotal > 0)
                    {
                        return m_TicksStatisticalList.RecentTotal / m_CallStatisticalList.RecentTotal;
                    }
                    else
                    {
                        return 0;
                    }
                }
            }

            public void Init(uint nWorkUnitID, string strMethodName)
            {
                m_nWorkUnitID = nWorkUnitID;
                m_strMethodName = strMethodName;
            }

            public void Clear()
            {
                m_nCallCount = 0;
                m_nTicks = 0;
                m_CallStatisticalList.Clear();
                m_TicksStatisticalList.Clear();
                m_LastTicksMonitor.Clear();
                m_AverageTicksMonitor.Clear();
            }

            public void AddData(long nTicks)
            {
                ++m_nCallCount;
                m_nTicks += nTicks;
            }

            public void RecordData()
            {
                m_CallStatisticalList.AddSample(m_nCallCount);
                m_TicksStatisticalList.AddSample(m_nTicks);

                m_nCallCount = 0;
                m_nTicks = 0;

                {
                    if (m_LastTicksMonitor.CheckCross(m_TicksStatisticalList.LastSample, out bool bAbove, out ulong nCrossLine))
                    {
                        if (bAbove)
                        {
                            long nLastTimeInMicroseconds = TicksToMicroseconds(m_TicksStatisticalList.LastSample);
                            Log.Warning($"[Performance] Method {m_strMethodName} Last Frame Execute Time Is About {nLastTimeInMicroseconds} Microseconds At {LastCallCount} Times In WorkUnit {m_nWorkUnitID}.");
                        }
                        else
                        {
                            long nCrossTimeInMicroseconds = TicksToMicroseconds((long)nCrossLine);
                            Log.Info($"[Performance] Method {m_strMethodName} Last Frame Execute Time Fall Below {nCrossTimeInMicroseconds} Microseconds In WorkUnit {m_nWorkUnitID}.");
                        }
                    }
                }

                {
                    if (m_AverageTicksMonitor.CheckCross(m_TicksStatisticalList.RecentAverage, out bool bAbove, out ulong nCrossLine))
                    {
                        if (bAbove)
                        {
                            long nAverageTimeInMicroseconds = TicksToMicroseconds(m_TicksStatisticalList.RecentAverage);
                            Log.Warning($"[Performance] Method {m_strMethodName} Recent Average Execute Time Is About {nAverageTimeInMicroseconds} Microseconds At {AverageCallCount:0.00} Times In WorkUnit {m_nWorkUnitID}.");
                        }
                        else
                        {
                            long nCrossTimeInMicroseconds = TicksToMicroseconds((long)nCrossLine);
                            Log.Info($"[Performance] Method {m_strMethodName} Recent Average Execute Time Fall Below {nCrossTimeInMicroseconds} Microseconds In WorkUnit {m_nWorkUnitID}.");
                        }
                    }
                }
            }

            public static int Compare_LastTicks(MethodPerformanceData x, MethodPerformanceData y)
            {
                if (x.LastTicks < y.LastTicks)
                {
                    return 1;
                }
                else if (x.LastTicks > y.LastTicks)
                {
                    return -1;
                }
                else
                {
                    return 0;
                }
            }

            public static int Compare_AverageTicks(MethodPerformanceData x, MethodPerformanceData y)
            {
                if (x.AverageTicks < y.AverageTicks)
                {
                    return 1;
                }
                else if (x.AverageTicks > y.AverageTicks)
                {
                    return -1;
                }
                else
                {
                    return 0;
                }
            }
        }
        private Dictionary<string, MethodPerformanceData> m_MethodPerformanceDataList = new Dictionary<string, MethodPerformanceData>();
        private List<MethodPerformanceData> m_MethodLogList = new List<MethodPerformanceData>();
        private static long s_nMethodLogMinTimeInMicroseconds = 10;

        private sealed class MethodContext : IReference
        {
            private string m_strMethodName;
            private Stopwatch m_Stopwatch = new Stopwatch();

            public string MethodName
            {
                get
                {
                    return m_strMethodName;
                }
            }

            public long Ticks
            {
                get
                {
                    return m_Stopwatch.ElapsedTicks;
                }
            }

            public void Init(string strMethodName)
            {
                m_strMethodName = strMethodName;
            }

            public void Clear()
            {
                m_strMethodName = null;
                m_Stopwatch.Reset();
            }

            public void Enter()
            {
                m_Stopwatch.Start();
            }

            public void Leave()
            {
                m_Stopwatch.Stop();
            }
        }
        private Stack<MethodContext> m_MethodContextStack = new Stack<MethodContext>();
        private static ushort s_nMethodStackMaxLayer = 1024;

        public static ushort PerformanceSampleHoldCount
        {
            get
            {
                return s_nPerformanceSampleHoldCount;
            }
        }

        public EState State
        {
            get { return m_eState; }
            set { m_eState = value; }
        }

        public ThreadObject OwnThreadObject
        {
            get { return m_Thread; }
            set
            {
                if (value != m_Thread)
                {
                    if (m_Thread != null)
                    {
                        Log.Warning($"[WorkUnit] WorkUnit {ID} Leave Thread {m_Thread.ID}.");
                    }

                    m_Thread = value;

                    if (m_Thread != null)
                    {
                        Log.Warning($"[WorkUnit] WorkUnit {ID} Enter Thread {m_Thread.ID}.");
                    }
                }
            }
        }

        //public EntitySystem EntitySystem
        //{
        //    get { return m_EntitySystem; }
        //}

        public long RecentTotalTicks
        {
            get
            {
                return m_TicksStatisticalList.RecentTotal;
            }
        }

        public WorkUnitBase()
        {
        }

        public void Init(uint nID, IWorkUnitOwner owner)
        {
            ClearPacket();

            m_nID = nID;
            m_Owner = owner;
            m_Owner.WorkUnit = this;
            m_strOwnerTickMethodName = m_Owner.GetType().Name + ".Tick";
        }

        public void Clear()
        {
            //m_EntitySystem.Clear();

            m_strOwnerTickMethodName = "";
            m_Owner.WorkUnit = null;
            m_Owner = null;
            m_nID = 0;

            ClearPacket();
        }

        public static long TicksToMicroseconds(long nTicks)
        {
            return (long)(nTicks * s_fTicksToMicrosecondsFactor);
        }

        public static long MicrosecondsToTicks(long nTimeInMicroseconds)
        {
            return (long)(nTimeInMicroseconds * s_fMicrosecondsToTicksFactor);
        }

        public void EnterMethod(string strMethodName)
        {
            if (m_bPerformanceEnabled)
            {
                if (m_MethodContextStack.TryPeek(out MethodContext lastMethodContext))
                {
                    lastMethodContext.Leave();
                }

                MethodContext currentMethodContext = ReferencePool.Acquire<MethodContext>();
                currentMethodContext.Init(strMethodName);

                m_MethodContextStack.Push(currentMethodContext);

                currentMethodContext.Enter();

                if (m_MethodContextStack.Count > s_nMethodStackMaxLayer)
                {
                    throw new FrameworkException($"Stack Layer Count Exceed {s_nMethodStackMaxLayer}, Suspect Infinite Recursion!");
                }
            }
        }

        public void LeaveMethod(string strMethodName)
        {
            if (m_bPerformanceEnabled)
            {
                if (m_MethodContextStack.TryPeek(out MethodContext currentMethodContext))
                {
                    if (currentMethodContext.MethodName == strMethodName)
                    {
                        currentMethodContext.Leave();

                        if (!m_MethodPerformanceDataList.TryGetValue(currentMethodContext.MethodName, out MethodPerformanceData data))
                        {
                            data = ReferencePool.Acquire<MethodPerformanceData>();
                            data.Init(ID, currentMethodContext.MethodName);
                            m_MethodPerformanceDataList[currentMethodContext.MethodName] = data;
                            m_MethodLogList.Add(data);
                        }
                        data.AddData(currentMethodContext.Ticks);

                        m_MethodContextStack.Pop();

                        ReferencePool.Release(currentMethodContext);

                        if (m_MethodContextStack.TryPeek(out MethodContext lastMethodContext))
                        {
                            lastMethodContext.Enter();
                        }
                    }
                    else
                    {
                        Log.Exception($"Method Context Stack Top Is Not Matching!");
                    }
                }
                else
                {
                    Log.Exception($"Method Context Stack Should Not Be Empty!");
                }
            }
        }

        public void OnFrameEnd()
        {
            m_bPerformanceEnabled = false;
            try
            {
                if (m_MethodContextStack.Count > 0)
                {
                    while (m_MethodContextStack.TryPop(out MethodContext currentMethodContext))
                    {
                        ReferencePool.Release(currentMethodContext);
                    }

                    Log.Exception($"Method Context Stack Should Be Empty!");
                }

                foreach (MethodPerformanceData data in m_MethodPerformanceDataList.Values)
                {
                    data.RecordData();
                }

                {
                    if (m_LastTicksMonitor.CheckCross(m_TicksStatisticalList.LastSample, out bool bAbove, out ulong nCrossLine))
                    {
                        if (bAbove)
                        {
                            long nLastTimeInMicroseconds = TicksToMicroseconds(m_TicksStatisticalList.LastSample);
                            Log.Warning($"[Performance] WorkUnit {ID} Last Frame Execute Time Is About {nLastTimeInMicroseconds} Microseconds.");

                            m_MethodLogList.Sort(MethodPerformanceData.Compare_LastTicks);

                            bool bNeedLog = false;
                            foreach (MethodPerformanceData data in m_MethodLogList)
                            {
                                long nMethodLastTimeInMicroseconds = TicksToMicroseconds(data.LastTicks);
                                if (nMethodLastTimeInMicroseconds >= s_nMethodLogMinTimeInMicroseconds)
                                {
                                    if (!bNeedLog)
                                    {
                                        bNeedLog = true;
                                        Log.Info($"[Performance] WorkUnit {ID} All Methods Last Frame Performance Are Listed As Below:");
                                    }
                                    Log.Info($"[Performance] Method {data.MethodName} Last Frame Execute Time Is About {nMethodLastTimeInMicroseconds} Microseconds At {data.LastCallCount} Times In WorkUnit {ID}.");
                                }
                                else
                                {
                                    break;
                                }
                            }
                            if (bNeedLog)
                            {
                                Log.Info($"[Performance] WorkUnit {ID} All Methods Last Frame Performance Are Listed As Above.");
                            }
                        }
                        else
                        {
                            long nCrossTimeInMicroseconds = TicksToMicroseconds((long)nCrossLine);
                            Log.Info($"[Performance] WorkUnit {ID} Last Frame Execute Time Fall Below {nCrossTimeInMicroseconds} Microseconds.");
                        }
                    }
                }

                {
                    if (m_AverageTicksMonitor.CheckCross(m_TicksStatisticalList.RecentAverage, out bool bAbove, out ulong nCrossLine))
                    {
                        if (bAbove)
                        {
                            long nAverageTimeInMicroseconds = TicksToMicroseconds(m_TicksStatisticalList.RecentAverage);
                            Log.Warning($"[Performance] WorkUnit {ID} Recent Average Execute Time Is About {nAverageTimeInMicroseconds} Microseconds.");

                            m_MethodLogList.Sort(MethodPerformanceData.Compare_AverageTicks);

                            bool bNeedLog = false;
                            foreach (MethodPerformanceData data in m_MethodLogList)
                            {
                                long nMethodAverageTimeInMicroseconds = TicksToMicroseconds(data.AverageTicks);
                                if (nMethodAverageTimeInMicroseconds >= s_nMethodLogMinTimeInMicroseconds)
                                {
                                    if (!bNeedLog)
                                    {
                                        bNeedLog = true;
                                        Log.Info($"[Performance] WorkUnit {ID} All Methods Recent Average Performance Are Listed As Below:");
                                    }
                                    Log.Info($"[Performance] Method {data.MethodName} Recent Average Execute Time Is About {nMethodAverageTimeInMicroseconds} Microseconds At {data.AverageCallCount:0.00} Times In WorkUnit {ID}.");
                                }
                                else
                                {
                                    break;
                                }
                            }
                            if (bNeedLog)
                            {
                                Log.Info($"[Performance] WorkUnit {ID} All Methods Recent Average Performance Are Listed As Above.");
                            }
                        }
                        else
                        {
                            long nCrossTimeInMicroseconds = TicksToMicroseconds((long)nCrossLine);
                            Log.Info($"[Performance] WorkUnit {ID} Recent Average Execute Time Fall Below {nCrossTimeInMicroseconds} Microseconds.");
                        }
                    }
                }
            }
            finally
            {
                m_bPerformanceEnabled = true;
            }
        }

        public void Run()
        {
            try
            {
                m_InnerStopwatch.Start();
                {
                    int nLeftCount = m_PacketQueue.Count;
                    if (nLeftCount > 0)
                    {
                        m_PacketStopwatch.Restart();

                        while ((nLeftCount-- > 0) && m_PacketQueue.TryDequeue(out Packet packet))
                        {
                            PacketDispatcherManager.Dispatch(packet);

                            if (m_PacketStopwatch.ElapsedMilliseconds >= s_nPacketDispatchMaxTimeInMS)
                            {
                                if (m_PacketLeftCountMonitor.CheckCross(nLeftCount, out bool bAbove, out ulong nCrossLine))
                                {
                                    if (bAbove)
                                    {
                                        Log.Warning($"[Performance] WorkUnit {m_nID} Packet Left Count Is About {nLeftCount}.");
                                    }
                                    else
                                    {
                                        Log.Info($"[Performance] WorkUnit {m_nID} Packet Left Count Fall Below {nCrossLine}.");
                                    }
                                }

                                break;
                            }
                        }

                        m_PacketStopwatch.Stop();
                    }
                    /*
                                        while (m_PacketQueue.TryDequeue(out Packet packet))
                                        {
                                            m_CachePacketQueue.Enqueue(packet);
                                        }
                                        while (m_CachePacketQueue.TryDequeue(out Packet packet))
                                        {
                                            PacketDispatcherManager.Dispatch(packet);
                                        }
                    */
                    //m_EntitySystem.FrequentlyUpdate();
                }
                m_InnerStopwatch.Stop();

                m_OuterStopwatch.Stop();
                if (m_OuterStopwatch.ElapsedMilliseconds >= m_nUpdateIntervalInMS)
                {
                    m_nUpdateIntervalInMS = s_nStandardUpdateIntervalInMS + RandomGenerator.RandomNumber(-s_nUpdateIntervalDisturbanceInMS, (s_nUpdateIntervalDisturbanceInMS + 1));
                    m_OuterStopwatch.Restart();

                    m_InnerStopwatch.Start();
                    {
                        frameTimeUpdate();

                        try
                        {
#if OPEN_PERFORMANCE
                            PerformanceMonitorManager.EnterMethod(m_strOwnerTickMethodName);
#endif                           
                            m_Owner.Tick(this.m_FrameTime);
                        }
                        finally
                        {
#if OPEN_PERFORMANCE
                            PerformanceMonitorManager.LeaveMethod(m_strOwnerTickMethodName);
#endif                       
                        }

                        //m_EntitySystem.Update(this.m_FrameTime);

                        //m_EntitySystem.LateUpdate();

                        m_ThreadSyncContext.Update();
                    }
                    m_InnerStopwatch.Stop();
                    m_TicksStatisticalList.AddSample(m_InnerStopwatch.ElapsedTicks);
                    m_InnerStopwatch.Reset();

                    OnFrameEnd();
                }
                else
                {
                    m_OuterStopwatch.Start();
                }
            }
            catch (Exception e)
            {
                Log.Exception($"[WorkUnit] WorkUnit {ID} Encounter Exception {e.Message} With Owner {Owner.LogName}, StackTrace:{e.StackTrace}!");

                m_Owner.OnException(e);
            }
        }

        public void PostPacket(Packet packet)
        {
            m_PacketQueue.Enqueue(packet);
        }

        public void ClearPacket()
        {
            while (m_PacketQueue.TryDequeue(out Packet packet))
            {
                PacketDispatcherManager.DiscardPacket(packet);
            }
        }

        public void Start()
        {
            //m_EntitySystem.OwnerID = m_nID;

            m_eState = EState.SLEEP;
            m_LastClientTime = ATimer.Instance.SystemTicks;
            m_nUpdateIntervalInMS = RandomGenerator.RandomNumber(0, s_nStandardUpdateIntervalInMS);
            m_OuterStopwatch.Start();

            m_bPerformanceEnabled = true;

            m_Owner.OnStart();

            Log.Warning($"[WorkUnit] WorkUnit {ID} Start With Owner {Owner.LogName}.");
        }

        public void Stop()
        {
            Log.Warning($"[WorkUnit] WorkUnit {ID} Stop With Owner {Owner.LogName}.");

            m_Owner.OnStop();

            m_bPerformanceEnabled = false;

            if (m_MethodContextStack.Count > 0)
            {
                while (m_MethodContextStack.TryPop(out MethodContext currentMethodContext))
                {
                    ReferencePool.Release(currentMethodContext);
                }

                Log.Exception($"Method Context Stack Should Be Empty!");
            }

            m_MethodLogList.Clear();
            foreach (MethodPerformanceData data in m_MethodPerformanceDataList.Values)
            {
                ReferencePool.Release(data);
            }
            m_MethodPerformanceDataList.Clear();

            ClearPacket();

            m_TicksStatisticalList.Clear();
            m_LastTicksMonitor.Clear();
            m_AverageTicksMonitor.Clear();
            m_PacketLeftCountMonitor.Clear();
            m_PacketStopwatch.Reset();
            m_InnerStopwatch.Reset();
            m_OuterStopwatch.Reset();
            m_ThreadSyncContext.Clear();
            m_eState = EState.INITIAL;

            m_Owner.OnDestroy();
        }

        private void frameTimeUpdate()
        {
            long now = ATimer.Instance.SystemTicks;
            this.m_FrameTime = (int)(now - this.m_LastClientTime);
            this.m_LastClientTime = now;
        }
    }
}
