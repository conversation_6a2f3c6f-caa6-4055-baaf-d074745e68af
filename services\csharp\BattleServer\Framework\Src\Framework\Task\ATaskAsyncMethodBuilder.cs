﻿//*********************************************************
// Framework
// Author:  Jasen
// Date  :  2022-11-3
//*********************************************************


using System;
using System.Runtime.CompilerServices;

namespace Aurora.Framework
{
    public struct ATaskAsyncMethodBuilder<TResult> : IReference
    {
        public static ATaskAsyncMethodBuilder<TResult> Create()
        {
            return new ATaskAsyncMethodBuilder<TResult>() { m_Task = new ATask<TResult>() };
        }

        private ATask<TResult> m_Task;
        public ATask<TResult> Task => m_Task;

        public void SetResult(TResult result)
        {
            Task.SetResult(result);
        }

        public void Start<TStateMachine>(ref TStateMachine stateMachine) where TStateMachine : IAsyncStateMachine
        {
            stateMachine.MoveNext();
        }

        public void AwaitOnCompleted<TAwaiter, TStateMachine>(ref TAwaiter awaiter, ref TStateMachine stateMachine)
            where TAwaiter : INotifyCompletion where TStateMachine : IAsyncStateMachine
        {
            //配置完成后的延续
            awaiter.OnCompleted(stateMachine.MoveNext);
        }

        public void AwaitUnsafeOnCompleted<TAwaiter, TStateMachine>(ref TAwaiter awaiter, ref TStateMachine stateMachine)
            where TAwaiter : ICriticalNotifyCompletion where TStateMachine : IAsyncStateMachine
        {
            //配置完成后的延续
            awaiter.UnsafeOnCompleted(stateMachine.MoveNext);
        }

        public void SetStateMachine(IAsyncStateMachine stateMachine)
        {
        }

        public void SetException(Exception exception)
        {
            if (exception == null) return;
            Log.Exception($"ATaskAsyncMethodBuilder SetException, Message:{exception.Message}\n StackTrace:\n{exception.StackTrace}\n");
            Task.SetException(exception);
        }
        public void Clear()
        {
            
        }
    }
}
