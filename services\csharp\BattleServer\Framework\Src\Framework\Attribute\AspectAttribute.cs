﻿//#if SERVER_FRAMEWORK
//using PostSharp.Aspects;
//using PostSharp.Serialization;
//using System.Reflection;

//namespace Aurora.Framework
//{
//    [PSerializable]
//    //[AttributeUsage(AttributeTargets.Method)]
//    public sealed class PerformanceAspectAttribute : OnMethodBoundaryAspect
//    {
//        private string m_strMethodName;
//        public override bool CompileTimeValidate(MethodBase method)
//        {
//            if (method == null) return false;
//            m_strMethodName = GetMethodName(method);

//            return base.CompileTimeValidate(method);

//            //MethodInfo methodInfo = method as MethodInfo;
//            //if (methodInfo != null)
//            //{
//            //    if (methodInfo.ReturnType.Name.StartsWith("ATask"))
//            //    {
//            //        return false;
//            //    }

//            //    return base.CompileTimeValidate(method);
//            //}
//            //else
//            //{
//            //    return false;
//            //}
//        }

//        public override void OnEntry(MethodExecutionArgs args)
//        {
//            base.OnEntry(args);

//#if OPEN_PERFORMANCE
//            PerformanceMonitorManager.EnterMethod(m_strMethodName);
//#endif                  
//        }

//        public override void OnYield(MethodExecutionArgs args)
//        {
//#if OPEN_PERFORMANCE
//            PerformanceMonitorManager.LeaveMethod(m_strMethodName);
//#endif
//            base.OnYield(args);
//        }

//        public override void OnResume(MethodExecutionArgs args)
//        {
//            base.OnResume(args);

//#if OPEN_PERFORMANCE 
//            PerformanceMonitorManager.EnterMethod(m_strMethodName);
//#endif         
//        }

//        public override void OnExit(MethodExecutionArgs args)
//        {
//#if OPEN_PERFORMANCE
//            PerformanceMonitorManager.LeaveMethod(m_strMethodName);
//#endif          
//            base.OnExit(args);
//        }

//        private static string GetMethodName(MethodBase method)
//        {
//            Type classType = method.DeclaringType;
//            if (classType != null)
//            {
//                if (classType.IsNested)
//                {
//                    return classType.FullName + "." + method.Name;
//                }
//                else
//                {
//                    return classType.Name + "." + method.Name;
//                }
//            }
//            else
//            {
//                return method.Name;
//            }
//        }
//    }
//}
//#endif

//#if CLIENT_FRAMEWORK
//using System;

//namespace Aurora.Framework
//{
//    public class PerformanceAspectAttribute : Attribute
//    {
//    }
//}
//#endif
