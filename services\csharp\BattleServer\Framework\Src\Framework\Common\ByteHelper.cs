﻿//*********************************************************
// Framework
// Author:  Jasen
// Date  :  2022-11-22
//*********************************************************

using System.Globalization;
using System.Text;

namespace Aurora.Framework
{
    public static class ByteHelper
    {
        public static string ToHex(this byte b)
        {
            return b.ToString("X2", CultureInfo.InvariantCulture);
        }

        public static string ToHex(this byte[] bytes)
        {
            StringBuilder stringBuilder = new StringBuilder();
            if (bytes == null) return stringBuilder.ToString();
            foreach (byte b in bytes)
            {
                stringBuilder.Append(b.ToString("X2", CultureInfo.InvariantCulture));
            }
            return stringBuilder.ToString();
        }

        public static string ToHex(this byte[] bytes, string format)
        {
            StringBuilder stringBuilder = new StringBuilder();
            if (bytes == null) return stringBuilder.ToString();
            foreach (byte b in bytes)
            {
                stringBuilder.Append(b.ToString(format, CultureInfo.InvariantCulture));
            }
            return stringBuilder.ToString();
        }

        public static string ToHex(this byte[] bytes, int offset, int count)
        {
            StringBuilder stringBuilder = new StringBuilder();
            if (bytes == null) return stringBuilder.ToString();
            for (int i = offset; i < offset + count; ++i)
            {
                stringBuilder.Append(bytes[i].ToString("X2", CultureInfo.InvariantCulture));
            }
            return stringBuilder.ToString();
        }

        public static string ToStr(this byte[] bytes)
        {
            return Encoding.Default.GetString(bytes);
        }

        public static string ToStr(this byte[] bytes, int index, int count)
        {
            return Encoding.Default.GetString(bytes, index, count);
        }

        public static string Utf8ToStr(this byte[] bytes)
        {
            return Encoding.UTF8.GetString(bytes);
        }

        public static string Utf8ToStr(this byte[] bytes, int index, int count)
        {
            return Encoding.UTF8.GetString(bytes, index, count);
        }

        public static void WriteTo(this byte[] bytes, int offset, uint num)
        {
            if (bytes == null) return;
            bytes[offset] = (byte)(num & 0xff);
            bytes[offset + 1] = (byte)((num & 0xff00) >> 8);
            bytes[offset + 2] = (byte)((num & 0xff0000) >> 16);
            bytes[offset + 3] = (byte)((num & 0xff000000) >> 24);
        }

        public static void WriteTo(this byte[] bytes, int offset, int num)
        {
            if (bytes == null) return;
            bytes[offset] = (byte)(num & 0xff);
            bytes[offset + 1] = (byte)((num & 0xff00) >> 8);
            bytes[offset + 2] = (byte)((num & 0xff0000) >> 16);
            bytes[offset + 3] = (byte)((num & 0xff000000) >> 24);
        }

        public static void WriteTo(this byte[] bytes, int offset, byte num)
        {
            if (bytes == null) return;
            bytes[offset] = num;
        }

        public static void WriteTo(this byte[] bytes, int offset, short num)
        {
            if (bytes == null) return;
            bytes[offset] = (byte)(num & 0xff);
            bytes[offset + 1] = (byte)((num & 0xff00) >> 8);
        }

        public static void WriteTo(this byte[] bytes, int offset, ushort num)
        {
            if (bytes == null) return;
            bytes[offset] = (byte)(num & 0xff);
            bytes[offset + 1] = (byte)((num & 0xff00) >> 8);
        }

        //public static unsafe void WriteTo(this byte[] bytes, int offset, long num)
        //{
        //    if (bytes == null) return;
        //    if (bytes.Length >= offset + sizeof(long))
        //    {
        //        fixed (byte* b = &bytes[offset])
        //        {
        //            *((long*)b) = num;
        //        }
        //    }
        //}
    }
}
