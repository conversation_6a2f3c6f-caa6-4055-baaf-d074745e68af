﻿//*********************************************************
// Framework
// Author:  Jasen
// Date  :  2022-11-3
//*********************************************************


using System;
using System.Runtime.Serialization;

namespace Aurora.Framework
{
    public class FrameworkException : Exception
    {
        public FrameworkException() : base()
        {
        }

        public FrameworkException(string msg) : base(msg)
        {
        }

        public FrameworkException(string msg, Exception innerException) : base(msg, innerException)
        {
        }

        //public FrameworkException(SerializationInfo info, StreamingContext context)
        //    : base(info, context)
        //{
        //}
    }
}
