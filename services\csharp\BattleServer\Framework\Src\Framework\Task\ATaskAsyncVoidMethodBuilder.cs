﻿//*********************************************************
// Framework
// Author:  Jasen
// Date  :  2022-11-3
//*********************************************************


using System;
using System.Runtime.CompilerServices;

namespace Aurora.Framework
{
    public struct ATaskAsyncVoidMethodBuilder
    {
        public static ATaskAsyncVoidMethodBuilder Create()
        {
            return new ATaskAsyncVoidMethodBuilder() { m_Task = new ATask() };
        }

        private ATask m_Task;
        public ATask Task => m_Task;

        public void SetResult()
        {
            Task.SetResult();
        }

        public void Start<TStateMachine>(ref TStateMachine stateMachine) where TStateMachine : IAsyncStateMachine
        {
            //Builder用于保存Task及状态机的状态切换
            //编译器代码编译完后，异步函数包装的异步程序第一次执行
            stateMachine.MoveNext();
        }

        public void AwaitOnCompleted<TAwaiter, TStateMachine>(ref TAwaiter awaiter, ref TStateMachine stateMachine)
            where TAwaiter : INotifyCompletion where TStateMachine : IAsyncStateMachine
        {
            //配置完成后的延续,完成后继续执行状态机
            awaiter.OnCompleted(stateMachine.MoveNext);
        }

        public void AwaitUnsafeOnCompleted<TAwaiter, TStateMachine>(ref TAwaiter awaiter, ref TStateMachine stateMachine)
            where TAwaiter : ICriticalNotifyCompletion where TStateMachine : IAsyncStateMachine
        {
            //配置完成后的延续
            awaiter.UnsafeOnCompleted(stateMachine.MoveNext);
        }

        public void SetStateMachine(IAsyncStateMachine stateMachine)
        {
        }

        public void SetException(Exception exception)
        {
            if (exception == null) return;
            Log.Exception($"ATaskAsyncVoidMethodBuilder SetException, Message:{exception.Message}\n StackTrace:\n{exception.StackTrace}\n");
            Task.SetException(exception);
        }
    }
}
