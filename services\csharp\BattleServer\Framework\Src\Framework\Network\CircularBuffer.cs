﻿//*********************************************************
// Framework
// Author:  Jasen
// Date  :  2022-11-15
//*********************************************************

using System;
using System.Collections.Generic;
using System.IO;

namespace Aurora.Framework
{
    public sealed class CircularBuffer
    {
        public static int ChunkSize = 8192;

        private readonly Queue<byte[]> m_BufferQueue = new Queue<byte[]>();

        public int HeadIndex { get; set; }

        public int TailIndex {
            get; 
            set; 
        }

        private byte[] m_TailBuffer;

        public CircularBuffer()
        {

        }
        //头部块能读的大小
        public int ReadSize
        {
            get { return ChunkSize - HeadIndex; }
        }
        //尾部块能写的大小
        public int WriteSize
        {
            get { return ChunkSize - TailIndex; }
        }
        public long Length
        {
            get
            {
                int c = 0;
                if (m_BufferQueue.Count == 0)
                {
                    c = 0;
                }
                else
                {
                    c = (m_BufferQueue.Count - 1) * ChunkSize + TailIndex - HeadIndex;
                }
                if (c < 0)
                {
                    c = 0;
                    Log.Error($"CircularBuffer count < 0: {m_BufferQueue.Count}, {TailIndex}, {HeadIndex}");
                }
                return c;
            }
        }

        public void AddTail()
        {
            byte[] buffer = BytePool.Instance.GetBytes(ChunkSize);
            m_BufferQueue.Enqueue(buffer);
            m_TailBuffer = buffer;
        }
        public void RemoveHead()
        {
            BytePool.Instance.Recycle(m_BufferQueue.Dequeue());
        }

        public byte[] Head
        {
            get
            {
                if (m_BufferQueue.Count == 0)
                {
                    AddTail();
                }
                return m_BufferQueue.Peek();
            }
        }

        public byte[] Tail
        {
            get
            {
                if (m_BufferQueue.Count == 0)
                {
                    AddTail();
                }
                return m_TailBuffer;
            }
        }

        //从CircleBuffer读出数据
        public void Read(Stream stream, int count)
        {
            if (stream == null) return;
            if(count > Length)
            {
                throw new FrameworkException("CircularBuffer read data error: want to read too long!");
            }
            int hasCopyCount = 0;
            while(hasCopyCount < count)
            {
                int remainingCount = count - hasCopyCount;
                int canReadCount = ChunkSize - HeadIndex;
                if (canReadCount > remainingCount)
                {
                    //整个数据在当前块，直接读
                    stream.Write(Head, HeadIndex, remainingCount);
                    HeadIndex += remainingCount;
                    hasCopyCount += remainingCount;
                }
                else
                {
                    //刚刚好或者不够
                    stream.Write(Head, HeadIndex, canReadCount);
                    hasCopyCount += canReadCount;
                    HeadIndex = 0;
                    RemoveHead();
                }
            }
        }

        //从CircleBuffer读出数据
        public void Read(byte[] buffer, int offset, int count)
        {
            if (buffer == null) return;
            if(buffer.Length < offset + count)
            {
                throw new FrameworkException("CircularBuffer read data Error： buffer too short!");
            }
            int toReadLength = count;
            if (Length < toReadLength)
            {
                toReadLength = (int)Length;
            }
            int hasCopyCount = 0;
            while (hasCopyCount < toReadLength)
            {
                int remainingCount = toReadLength - hasCopyCount;
                int canReadCount = ChunkSize - HeadIndex;
                if (canReadCount > remainingCount)
                {
                    //整个数据在当前块，直接读
                    Buffer.BlockCopy(Head, HeadIndex, buffer, offset + hasCopyCount, remainingCount);
                    HeadIndex += remainingCount;
                    hasCopyCount += remainingCount;
                }
                else
                {
                    //刚刚好或者不够
                    Buffer.BlockCopy(Head, HeadIndex, buffer, offset + hasCopyCount, canReadCount);
                    hasCopyCount += canReadCount;
                    HeadIndex = 0;
                    RemoveHead();
                }
            }
        }
        //将数据写入到CircleBuffer中
        public void Write(Stream stream)
        {
            if (stream == null) return;
            int toWriteCount = (int)stream.Length;
            stream.Position = 0;
            int hasCopyCount = 0;
            while(hasCopyCount < toWriteCount)
            {
                //写完了，尾部增加一个
                if(TailIndex == ChunkSize)
                {
                    AddTail();
                    TailIndex = 0;
                }
                int remainingCount = toWriteCount - hasCopyCount;
                int canWriteCount = ChunkSize - TailIndex;
                if (canWriteCount > remainingCount)
                {
                    stream.Read(Tail, TailIndex, remainingCount);
                    TailIndex += remainingCount;
                    hasCopyCount += remainingCount;
                }
                else
                {
                    //全写满，还不够
                    stream.Read(Tail, TailIndex, canWriteCount);
                    TailIndex = ChunkSize;
                    hasCopyCount += canWriteCount;
                }
            }
        }
        //将数据写入到CircleBuffer中
        public void Write(byte[] buffer, int offset, int count)
        {
            if (buffer == null) return;
            if(buffer.Length < offset + count)
            {
                throw new FrameworkException("CircularBuffer write data Error： buffer too short!");
            }
            int toWriteCount = count;
            int hasCopyCount = 0;
            while (hasCopyCount < toWriteCount)
            {
                //写完了，尾部增加一个
                if (TailIndex == ChunkSize)
                {
                    AddTail();
                    TailIndex = 0;
                }
                int remainingCount = toWriteCount - hasCopyCount;
                int canWriteCount = ChunkSize - TailIndex;
                if (canWriteCount > remainingCount)
                {
                    Buffer.BlockCopy(buffer, offset + hasCopyCount, Tail, TailIndex, remainingCount);
                    TailIndex += remainingCount;
                    hasCopyCount += remainingCount;
                }
                else
                {
                    //全写满，还不够
                    Buffer.BlockCopy(buffer, offset + hasCopyCount, Tail, TailIndex, canWriteCount);
                    TailIndex = ChunkSize;
                    hasCopyCount += canWriteCount;
                }
            }
        }
    }
}
