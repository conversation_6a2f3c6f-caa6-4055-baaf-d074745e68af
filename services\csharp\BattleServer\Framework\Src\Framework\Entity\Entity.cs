﻿//*********************************************************
// Framework
// Author:  Jasen
// Date  :  2022-11-4
//*********************************************************


using System;
using System.Collections.Generic;
using System.Linq;

namespace Aurora.Framework
{
	//Entity树形类基类，一切皆是Entity
	public class Entity : IReference, IRelease
	{
		//唯一实例ID,哪都别改!
		public long InstanceID
		{
			get;
			set;
		}

		// //逻辑区分ID
		// [BsonIgnoreIfDefault]
		// [BsonDefaultValue(0L)]
		// [BsonElement]
		// [BsonId]
		// public long ID
		// {
		// 	get;
		// 	set;
		// }
        private EntitySystem _OwnSystem = null;
		public void SetOwnSystem(EntitySystem ownSystem)
		{
			_OwnSystem = ownSystem;
		}
		public EntitySystem GetOwnSystem()
		{
			return _OwnSystem;
		}

        public bool Valid = false;


		private bool m_WaitingRelease = false;


		public bool WaitingRecycle
		{
			get { return m_WaitingRelease; }
			set { m_WaitingRelease = value; }
		}


        public IComponentSystemCallback FrequentlyUpdateCallback { get; set; }


        public IComponentSystemCallback LateUpdateCallback { get; set; }


        public IComponentSystemCallback SlowlyUpdateCallback { get; set; }


        public string FrequentlyUpdateMethodName { get; set; }


        public string UpdateMethodName { get; set; }


        public string LateUpdateMethodName { get; set; }


        public string SlowlyUpdateMethodName { get; set; }

        //区分Entity和Component

		public bool IsComponent
		{
			get;
			protected set;
		}
		private static Entity Create(Type type)
		{
			Entity newEntity = (Entity)ReferencePool.Acquire(type);
			if (newEntity == null)
			{
				throw new FrameworkException($"Create entity error, type name: {type.FullName}");
			}
			//newEntity.ID = 0;

            return newEntity;
		}

		//设置根阈，用于常规外部访问

		protected Entity m_Root;


		public Entity Root
		{
			get => m_Root;
			set
			{
				if (value == m_Root)
				{
					return;
				}
				if (m_Root != null && value != null)
				{
					throw new FrameworkException($"Entity root has been set: {GetType().Name}");
				}
				if (value != null)
				{
					m_Root = value;
					EntitySystem ownSystem = m_Root.GetOwnSystem();
					if (ownSystem == null)
					{
						throw new FrameworkException($"Entity root has been set: {GetType().Name}");
					}
					ownSystem.Register(this);

					if (m_Childrens != null)
					{
						foreach (var child in m_Childrens)
						{
							child.Root = Root;
						}
					}
					if (m_Components != null)
					{
						foreach (var comp in m_Components)
						{
							comp.Value.Root = Root;
						}
					}
				}
				else
				{
					if (m_Components != null)
					{
						foreach (var comp in m_Components)
						{
							comp.Value.Root = null;
						}
					}
					if (m_Childrens != null)
					{
						foreach (var child in m_Childrens)
						{
							child.Root = null;
						}
					}

					EntitySystem ownSystem = GetOwnSystem();
					if (ownSystem == null)
					{
						throw new FrameworkException($"Entity root has been set: {GetType().Name}");
					}
					ownSystem.Unregister(this);
					m_Root = null;
				}
/*
				Entity oldRoot = m_Root;
				m_Root = value;
				//之前还没设置过的
				if (oldRoot == null && !(value == this))//排除最外层自己给自己设置
				{
					InstanceID = IDGenerater.Instance.InstanceId();

					// 反序列化出来的需要设置父子关系
// 					if (this.componentsDB != null)
// 					{
// 						foreach (BaseComponent component in this.componentsDB)
// 						{
// 							component.IsComponent = true;
// 							//this.Components.Add(component.GetType(), component);
// 							component.ComponentParent = this;
// 						}
// 					}

// 					if (this.childrenDB != null)
// 					{
// 						foreach (Entity child in this.childrenDB)
// 						{
// 							child.IsComponent = false;
// 							//this.m_Childrens.Add(child.ID, child);
// 							child.Parent = this;
// 						}
// 					}
					if (_OwnSystem == null)
						_OwnSystem = m_Root.GetOwnSystem();
					//将自己注册到EntitySystem去，设置了Root，才正式启用
					_OwnSystem.Register(this);
				}
				if (m_Childrens != null)
				{
					foreach (var child in m_Childrens)
					{
						child.Value.Root = this.Root;
					}
				}
				if (m_Components != null)
				{
					foreach (var comp in m_Components)
					{
						comp.Value.Root = this.Root;
					}
				}
*/
			}
		}


		protected Entity m_Parent;


		public Entity Parent
		{
			get => m_Parent;
			set
			{
				if (value == m_Parent)
				{
					if (m_Parent != null)
					{
						Log.Error($"duplicated set Parent: {GetType().Name} parent: {m_Parent.GetType().Name}");
					}
					return;
				}
				if (value == this)
				{
					throw new FrameworkException($"Entity can't set parent self: {GetType().Name}");
				}
				if (m_Parent != null)
				{
					m_Parent.InnerUnbindingChildren(this);
					m_Parent = null;
				}
				if (value != null)
				{
					m_Parent = value;
					m_Parent.InnerBindingChildren(this);
				}
				if (m_Parent != null)
				{
					Root = m_Parent.Root;
				}
				else
				{
					Root = null;
				}
			}
		}


		public Entity ComponentParent
		{
			get => m_Parent;
			private set
			{
				if (value == m_Parent)
				{
					if (m_Parent != null)
					{
						Log.Error($"duplicated set ComponentParent: {GetType().Name} parent: {m_Parent.GetType().Name}");
					}
					return;
				}
				if (value == this)
				{
					throw new FrameworkException($"Component can't set parent self: {GetType().Name}");
				}
				if (m_Parent != null)
				{
					m_Parent.InnerUnbindingComponent(this);
					m_Parent = null;
				}
				if (value != null)
				{
					m_Parent = value;
					m_Parent.InnerBindingComponent(this);
				}
				if (m_Parent != null)
				{
					Root = m_Parent.Root;
				}
				else
				{
					Root = null;
				}
			}
		}

// 		[BsonElement("Children")]
// 		[BsonIgnoreIfNull]
// 		private HashSet<Entity> childrenDB;

		//子节点以逻辑Id为Key
		private HashSetReuse<Entity> m_Childrens;
		public HashSetReuse<Entity> Childrens
		{
			get
			{
				if (m_Childrens == null)
				{
					m_Childrens = ReferencePool.Acquire<HashSetReuse<Entity>>();
				}
				return m_Childrens;
			}
		}
		// private DictionaryReuse<long, Entity> m_Childrens;
		// public DictionaryReuse<long, Entity> Childrens
		// {
		// 	get
		// 	{
		// 		if (m_Childrens == null)
		// 		{
		// 			m_Childrens = ReferencePool.Acquire<DictionaryReuse<long, Entity>>();
		// 		}
		// 		return m_Childrens;
		// 	}
		// }

// 		[BsonElement("C")]
// 		[BsonIgnoreIfNull]
// 		private HashSet<BaseComponent> componentsDB;

		//自身Entity挂接的Components，同种类型只能挂接一个
		private DictionaryReuse<Type, BaseComponent> m_Components;

		public DictionaryReuse<Type, BaseComponent> Components
		{
			get
			{
				if (m_Components == null)
				{
					m_Components = ReferencePool.Acquire<DictionaryReuse<Type, BaseComponent>>();
				}
				return m_Components;
			}
		}
		//绑定孩子
		private void InnerBindingChildren(Entity entity)
		{
            if (Childrens.Contains(entity))
			{
				Log.Error($"InnerBindingChildren add same Entity：Name: {entity.GetType().Name}");
				return;
			}
			Childrens.Add(entity);
			//this.AddToChildrenDB(entity);
		}

		//解除孩子的绑定
		private void InnerUnbindingChildren(Entity entity)
		{
			if (m_Childrens == null || entity == null)
			{
				return;
			}
			m_Childrens.Remove(entity);
			if (m_Childrens.Count == 0)
			{
				//退回引用池
				ReferencePool.Release(m_Childrens);
				m_Childrens = null;
			}

			//this.RemoveFromChildrenDB(entity);
		}

// 		private void AddToChildrenDB(Entity entity)
// 		{
// 			if (!(entity is ISerializeToEntity))
// 			{
// 				return;
// 			}
// 
// 			this.childrenDB ??= new HashSet<Entity>();
// 
// 			this.childrenDB.Add(entity);
// 		}

// 		private void RemoveFromChildrenDB(Entity entity)
// 		{
// 			if (!(entity is ISerializeToEntity))
// 			{
// 				return;
// 			}
// 
// 			if (this.childrenDB == null)
// 			{
// 				return;
// 			}
// 
// 			this.childrenDB.Remove(entity);
// 
// 			if (this.childrenDB.Count == 0/* && this.IsNew*/)
// 			{
// 				//ObjectPool.Instance.Recycle(this.childrenDB);
// 				this.childrenDB = null;
// 			}
// 		}

		//加入现有的Entity作为子节点
		public Entity AddChild(Entity entity, Action<Entity> afterAddAction = null)
		{
			if (entity == null)
			{
				throw new FrameworkException("Entity add entity is null!");
			}
			// if (entity.ID == 0)
			// {
			// 	entity.ID = IDGenerater.Instance.ID();
			// }
			entity.Parent = this;
			afterAddAction?.Invoke(entity);

			return entity;
		}
        // //加入现有的Entity作为子节点
        // public Entity AddChild(long logicID,Entity entity)
        // {
        //     if (entity == null)
        //     {
        //         throw new FrameworkException("Entity add entity is null!");
        //     }
        //     if (logicID == 0)
        //     {
        //         entity.ID = IDGenerater.Instance.ID();
		// 	}
		// 	else
		// 	{
        //         entity.ID = logicID;

        //     }
        //     entity.Parent = this;
        //     return entity;
        // }

        //创建并加入Entity作为子节点
        public T AddChild<T>(Action<Entity> afterAddAction = null) where T : Entity
		{
			Entity child = Create(typeof(T));
			// child.ID = IDGenerater.Instance.ID();
            child.Parent = this;
			afterAddAction?.Invoke(child);
			return (T)child;
		}
		// //创建并加入Entity作为子节点，并带入ID
		// public T AddChild<T>(long id) where T : Entity
		// {
		// 	Entity child = Create(typeof(T));
		// 	child.ID = id;
        //     child.Parent = this;
		// 	return (T)child;
		// }

		// public T GetChild<T>(long id) where T : Entity
		// {
		// 	if (m_Childrens == null)
		// 	{
		// 		return null;
		// 	}
		// 	m_Childrens.TryGetValue(id, out Entity child);
		// 	return child as T;
		// }
		public void RemoveChild(Entity entity, Action<Entity> beforeRemoveAction = null)
		{
			if (entity == null || m_Childrens == null)
				return;
			if (!m_Childrens.Contains(entity))
			{
				return;
			}
			beforeRemoveAction?.Invoke(entity);
            //m_Childrens.Remove(entity);
			entity.Parent = null;
            //entity.Release();
        }

		//组件绑定
		private void InnerBindingComponent(Entity component)
		{
			//调用Components，没有会从池中取一个

			if(Components.ContainsKey(component.GetType()))
			{
                Log.Error($"InnerBindingComponent add same component：Name: {component.GetType().Name}");
                return;
			}

			Components.Add(component.GetType(), component as BaseComponent);
			//this.AddToComponentsDB(component as BaseComponent);
		}
		//解除组件绑定
		private void InnerUnbindingComponent(Entity component)
		{
			if (m_Components == null || component == null)
				return;

			m_Components.Remove(component.GetType());
			if (m_Components.Count == 0)
			{
				ReferencePool.Release(m_Components);
				m_Components = null;
			}

			//this.RemoveFromComponentsDB(component as BaseComponent);
		}

		public BaseComponent AddComponent(BaseComponent component)
		{
			if (component == null || component.IsComponent == false)
			{
				throw new FrameworkException("Entity add component is null or not the component type!");
			}
			if (Components.ContainsKey(component.GetType()))
			{
				Log.Error($"Entity duplicated add component：Name: {component.GetType().Name}");
				return component;
			}
			//这里会检测之前是否已经挂接了，处理之前的挂接信息
			//component.ID = this.ID;
            component.ComponentParent = this;
            //string str = $"Init Component  : {component.GetType().ToString()}";
            //GameAnalysisFramework.StartWatchTime(str);
            EntitySystem.Awake(component);
            //GameAnalysisFramework.StopWatchTime(str, GameAnalysisFramework.MethodAnalysis.Common);
            return component;
		}

        public BaseComponent AddComponent(BaseComponent component,object arg)
        {
            if (component == null || component.IsComponent == false)
            {
                throw new FrameworkException("Entity add component is null or not the component type!");
            }
            if (Components.ContainsKey(component.GetType()))
            {
                Log.Error($"Entity duplicated add component：Name: {component.GetType().Name}");
                return component;
            }
            //这里会检测之前是否已经挂接了，处理之前的挂接信息
            //component.ID = this.ID;
            component.ComponentParent = this;
            //string str = $"Init Component  : {component.GetType().ToString()}";
            //GameAnalysisFramework.StartWatchTime(str);
            EntitySystem.Awake(component,arg);
            //GameAnalysisFramework.StopWatchTime(str, GameAnalysisFramework.MethodAnalysis.Common);
            return component;
        }

        public BaseComponent AddComponent(BaseComponent component, object arg1,object arg2)
        {
            if (component == null || component.IsComponent == false)
            {
                throw new FrameworkException("Entity add component is null or not the component type!");
            }
            if (Components.ContainsKey(component.GetType()))
            {
                Log.Error($"Entity duplicated add component：Name: {component.GetType().Name}");
                return component;
            }
            //这里会检测之前是否已经挂接了，处理之前的挂接信息
            //component.ID = this.ID;
            component.ComponentParent = this;
            //string str = $"Init Component  : {component.GetType().ToString()}";
            //GameAnalysisFramework.StartWatchTime(str);
            EntitySystem.Awake(component, arg1, arg2);
            //GameAnalysisFramework.StopWatchTime(str, GameAnalysisFramework.MethodAnalysis.Common);
            return component;
        }

        public BaseComponent AddComponent(Type tp)
        {
			if (tp == null) return null;
            Entity component = Create(tp);
            //component.ID = this.ID;
            component.ComponentParent = this;
            //string str = $"Init Component  : {component.GetType().ToString()}";
            //GameAnalysisFramework.StartWatchTime(str);
            EntitySystem.Awake(component);
            //GameAnalysisFramework.StopWatchTime(str, GameAnalysisFramework.MethodAnalysis.Common);
            return component as BaseComponent;
        }

        public T AddComponent<T>() where T : BaseComponent
		{
            //string str = $"Init Component  : {typeof(T).ToString()}";
            //GameAnalysisFramework.StartWatchTime(str);

            Entity component = Create(typeof(T));
			//component.ID = this.ID;
            component.ComponentParent = this;
			EntitySystem.Awake(component);

            //GameAnalysisFramework.StopWatchTime(str, GameAnalysisFramework.MethodAnalysis.Common);

            return component as T;
		}

		public T AddComponent<T>(object arg) where T : BaseComponent
        {
            //string str = $"Init Component  : {typeof(T).ToString()}";
            //GameAnalysisFramework.StartWatchTime(str);

            Entity component = Create(typeof(T));
            //component.ID = this.ID;
            component.ComponentParent = this;
			EntitySystem.Awake(component, arg);

            //GameAnalysisFramework.StopWatchTime(str, GameAnalysisFramework.MethodAnalysis.Common);

            return component as T;
        }
        public T AddComponent<T>(object arg1,object arg2) where T : BaseComponent
        {
            //string str = $"Init Component  : {typeof(T).ToString()}";
            //GameAnalysisFramework.StartWatchTime(str);

            Entity component = Create(typeof(T));
            //component.ID = this.ID;
            component.ComponentParent = this;
			EntitySystem.Awake(component, arg1,arg2);

            //GameAnalysisFramework.StopWatchTime(str, GameAnalysisFramework.MethodAnalysis.Common);

            return component as T;
        }
        //移除组件，回收子Entity及其所有的Component
        public void RemoveComponent(BaseComponent component)
		{
			if (component == null)
				return;
			if (m_Components == null)
				return;
			BaseComponent nowComponent = null;
			if (!m_Components.TryGetValue(component.GetType(), out nowComponent))
			{
				return;
			}
			//这里需要注意，如果同类型的组件移除时，有可能已经不是之前的组件了
			if (component.InstanceID != nowComponent.InstanceID)
				return;

			//递归释放
			component.Release();
		}
		public void RemoveComponent<T>() where T : BaseComponent
		{
            if (m_Components == null)
                return;
            Type tp = typeof(T);
			BaseComponent component = null;
			if (!m_Components.TryGetValue(tp, out component))
			{
				return;
			}
			RemoveComponent(component);
		}

		public void ExchangeComponent(Entity other, BaseComponent selfCom, BaseComponent othersCom)
		{
			if (other == null || othersCom == null)
			{
				return;
			}
			if (other.m_Components == null)
			{
				return;
			}
			if (selfCom == null)
			{
				return;
			}
			BaseComponent selfNowComponent = null;
			if (!m_Components.TryGetValue(selfCom.GetType(), out selfNowComponent))
			{
				return;
			}
			BaseComponent otherNowComponent = null;
			if (!other.m_Components.TryGetValue(othersCom.GetType(), out otherNowComponent))
			{
				return;
			}
			if (selfNowComponent == null || otherNowComponent == null)
			{
				return;
			}
			
			selfNowComponent.ComponentParent = null;
            otherNowComponent.ComponentParent = null;

            selfNowComponent.ComponentParent = other;
			otherNowComponent.ComponentParent = this;
		}

		public T GetComponent<T>() where T : BaseComponent
		{
            if (m_Components == null)
                return null;
            Type tp = typeof(T);
			BaseComponent component = null;
			if (!m_Components.TryGetValue(tp, out component))
			{
				return null;
			}
			return (T)component;
		}

        public BaseComponent GetComponent(Type tp)
        {
            if (m_Components == null)
                return null;
            BaseComponent component = null;
            if (!m_Components.TryGetValue(tp, out component))
            {
                return null;
            }
            return component;
        }

        public T GetComponentInParent<T>() where T : BaseComponent
		{
            Type tp = typeof(T);
			Entity parent = this;
			BaseComponent component = null;
            while (parent != null)
			{
				component = parent.GetComponent<T>();
                if (component != null)
				{
					return (T)component;
                }
				if(parent.IsComponent)
				{
					parent = parent.ComponentParent;
				}
				else
				{
					if(parent == parent.Parent)
					{
						break;
					}
					parent = parent.Parent;
				}
			}
			return null;
        }


//         private void AddToComponentsDB(BaseComponent component)
// 		{
// 			if (!(component is ISerializeToEntity))
// 			{
// 				return;
// 			}
// 
// 			if (!componentsDB.Contains(component))
// 			{
// 				this.componentsDB ??= new HashSet<BaseComponent>()/*ObjectPool.Instance.Fetch<HashSet<Entity>>()*/;
// 				this.componentsDB.Add(component);
// 			}
// 		}

// 		private void RemoveFromComponentsDB(BaseComponent component)
// 		{
// 			if (!(component is ISerializeToEntity))
// 			{
// 				return;
// 			}
// 
// 			if (this.componentsDB == null)
// 			{
// 				return;
// 			}
// 
// 			this.componentsDB.Remove(component);
// 			if (this.componentsDB.Count == 0/* && this.IsNew*/)
// 			{
// 				//ObjectPool.Instance.Recycle(this.componentsDB);
// 				this.componentsDB = null;
// 			}
// 		}

		public virtual void PreClear()
		{ }
        //Clear是有内部引用池管理
        public virtual void Clear()
		{
			WaitingRecycle = false;

			m_Components = null;
			m_Childrens = null;
			InstanceID = 0;
			//ID = 0;
			m_Parent = null;
			m_Root = null;
			_OwnSystem = null;
		}

		public virtual void OnEnterSystem()
		{
		}

		public virtual void OnLeaveSystem()
		{
		}

        /// <summary>
        /// Disconnect The Association From The External
        /// </summary>
        /// <exception cref="FrameworkException"></exception>
        private void UnassociateExternal()
		{
            if (Root == this)
            {
                if (GetOwnSystem() != null)
                {
                    GetOwnSystem().RemoveRootEntity(this);
                }
                else
                {
                    throw new FrameworkException("Entity Unassociate External Fail! Root Entity Can Not Find Its Own EntitySystem.");
                }
            }
            else
            {
                if (IsComponent)
                {
                    ComponentParent = null;
                }
                else
                {
                    Parent = null;
                }
            }
        }

		public virtual void Release()
		{
			EntitySystem system = null;
			{
				EntitySystem currentSystem = EntitySystem.InstanceGetter?.Invoke();
				if (currentSystem != null)
				{
					system = currentSystem;
				}
				else
				{
					system = _OwnSystem;
				}
/*
				if (_OwnSystem != null)
				{
					if (currentSystem != _OwnSystem)
					{
						//Log.Error("Entity Release Error! Entity System Is Not Matching.");
					}
					system = _OwnSystem;
				}
				else
				{
					if (currentSystem != null)
					{
						system = currentSystem;
					}
					else
					{
						//Log.Error("Entity Release Error! Entity System Is Not Found.");
					}
				}
*/
			}

            Destroy();

            UnassociateExternal();

            if (system != null)
			{
				system.RecycleEntity(this);
			}
			else
			{
				RecycleImmediately();
			}
		}

		public void Destroy()
		{
            if (m_Childrens != null && m_Childrens.Count > 0)
            {
                List<Entity> entitys = m_Childrens.ToList();
                foreach (var child in entitys)
                {
                    child.Destroy();
                }
            }

            if (m_Components != null && m_Components.Count > 0)
            {
                List<BaseComponent> comps = m_Components.Values.ToList();
                foreach (var com in comps)
                {
                    com.Destroy();
                }
            }

			PreClear();

            EntitySystem.Destroy(this);
        }

		public void RecycleImmediately()
		{
			if (m_Childrens != null && m_Childrens.Count > 0)
			{
				List<Entity> entitys = m_Childrens.ToList();
				foreach (var child in entitys)
				{
					child.RecycleImmediately();
				}
			}

			if (m_Components != null && m_Components.Count > 0)
			{
				List<BaseComponent> comps = m_Components.Values.ToList();
				foreach (var com in comps)
				{
					com.RecycleImmediately();
				}
			}

			UnassociateExternal();

			ReferencePool.Release(this);
		}

		public delegate bool OnUpdateExceptionErrorCallback(Entity entity, Entity errEntity, Exception e); 

		public OnUpdateExceptionErrorCallback OnUpdateExceptionErrCallback;
		public OnUpdateExceptionErrorCallback OnFrequentlyUpdateExceptionErrCallback;
		public OnUpdateExceptionErrorCallback OnLateUpdateExceptionErrCallback;
        public OnUpdateExceptionErrorCallback OnSlowlyUpdateExceptionErrCallback;

        public void OnUpdateExceptionError(Exception e)
		{
			Entity entity = this;
			Entity errEntity = this;
			bool bDoneException = false;
			while (bDoneException == false)
			{
				if (entity.OnUpdateExceptionErrCallback != null)
				{
					bDoneException = entity.OnUpdateExceptionErrCallback(entity, errEntity, e);
					if (bDoneException)
					{
						break;
					}
				}
				entity = entity.Parent;
				if (entity == null)
				{
					break;
				}
			}
		}

		public void OnFrequentlyUpdateExceptionError(Exception e)
		{
			Entity entity = this;
			Entity errEntity = this;
			bool bDoneException = false;
			while (bDoneException == false)
			{
				if (entity.OnFrequentlyUpdateExceptionErrCallback != null)
				{
					bDoneException = entity.OnFrequentlyUpdateExceptionErrCallback(entity, errEntity, e);
					if (bDoneException)
					{
						break;
					}
				}
				entity = entity.Parent;
				if (entity == null)
				{
					break;
				}
			}
		}

		public void OnLateUpdateExceptionError(Exception e)
		{
			Entity entity = this;
			Entity errEntity = this;
			bool bDoneException = false;
			while (bDoneException == false)
			{
				if (entity.OnLateUpdateExceptionErrCallback != null)
				{
					bDoneException = entity.OnLateUpdateExceptionErrCallback(entity, errEntity, e);
					if (bDoneException)
					{
						break;
					}
				}
				entity = entity.Parent;
				if (entity == null)
				{
					break;
				}
			}
		}

        public void OnSlowlyUpdateExceptionError(Exception e)
        {
            Entity entity = this;
            Entity errEntity = this;
            bool bDoneException = false;
            while (bDoneException == false)
            {
                if (entity.OnSlowlyUpdateExceptionErrCallback != null)
                {
                    bDoneException = entity.OnSlowlyUpdateExceptionErrCallback(entity, errEntity, e);
                    if (bDoneException)
                    {
                        break;
                    }
                }
                entity = entity.Parent;
                if (entity == null)
                {
                    break;
                }
            }
        }
    }
}
