﻿//*********************************************************
// Framework
// Author:  Jasen
// Date  :  2022-11-4
//*********************************************************


using System;
using System.Runtime.CompilerServices;

namespace Aurora.Framework
{
    [System.Runtime.CompilerServices.AsyncMethodBuilder(typeof(ATaskAsyncNothingMethodBuilder))]
    public class ATaskNothing : ICriticalNotifyCompletion, INotifyCompletion
    {
        public ATaskNothing()
        {
        }
        public ATaskNothing GetAwaiter()
        {
            return this;
        }
        public bool IsCompleted => true;
        public void OnCompleted(Action continuation)
        {
            //Do nothing
        }
        public void UnsafeOnCompleted(Action continuation)
        {
            //Do nothing
        }
        //设置异常
        public void SetException(Exception e)
        {
            throw e;
        }
        //设置异步结果，同时触发Awaiter后续
        public void SetResult()
        {
            //Do nothing
        }
        public void GetResult()
        {
            //Do nothing
        }
        public void Coroutine()
        {

        }
    }
}
