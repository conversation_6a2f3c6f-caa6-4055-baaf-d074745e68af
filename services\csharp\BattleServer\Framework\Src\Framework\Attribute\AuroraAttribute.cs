﻿//*********************************************************
// Framework
// Author:  Jasen
// Date  :  2022-11-8
//*********************************************************

using System;

namespace Aurora.Framework
{
    //所有标签特性的基类，System、Event等特性标签都继承于此类
    [AttributeUsage(AttributeTargets.Class, AllowMultiple = true)]
    public class AuroraAttribute : Attribute
    {
    }


    //函数标签基类
    [AttributeUsage(AttributeTargets.Method, AllowMultiple = true)]
    public class AuroraMethodAttribute : Attribute
    {
    }

    //属性标签基类
    [AttributeUsage(AttributeTargets.Property, AllowMultiple = true)]
    public class AuroraPropertyAttribute : Attribute
    {
    }
}
