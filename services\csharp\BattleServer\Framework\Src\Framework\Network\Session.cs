﻿//*********************************************************
// Framework
// Author:  Jasen
// Date  :  2022-11-16
//*********************************************************

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Sockets;
using System.Threading;

namespace Aurora.Framework
{
    public readonly struct RpcInfo
    {
        public static long s_MaxWaitTime = 128000;

        public readonly IRequest Request;
        public readonly ATask<IResponse> Tcs;
        public readonly long GiveUpTime;

        public RpcInfo(IRequest request)
        {
            this.Request = request;
            this.Tcs = new ATask<IResponse>();
            GiveUpTime = ATimer.Instance.SystemTicks + s_MaxWaitTime;
        }
    }

    public sealed class Session : <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ke, ID<PERSON>roy, IFrequentlyUpdate, IUpdate
    {
        public static ThreadLocal<bool> t_Interrupted = new ThreadLocal<bool>(() => { return false; }, false);
        public static bool Interrupted
        {
            get
            {
                return t_Interrupted.Value;
            }
            set
            {
                t_Interrupted.Value = value;
            }
        }

        /// <summary>
        /// Interrupt Dispatching The Following Packets In Current Cycle For Leaving Session.
        /// </summary>
        public static void InterruptOnce()
        {
            Interrupted = true;
        }

        public Queue<Packet> m_ReceivedPacketQueue = new Queue<Packet>();

        public MessageAddress m_MessageAddress = new MessageAddress();

        public MessageAddress Address
        {
            get { return m_MessageAddress; }
        }

        private TChannel m_Channel = null;

        public TChannel Channel
        {
            get
            {
                return m_Channel;
            }
            set
            {
                if (m_Channel != value)
                {
                    if (m_Channel != null)
                    {
                        RemoteAddress = null;
                        m_Channel.m_ReadCallback -= this.OnChannelRead;
                        m_Channel.m_ErrorCallback -= this.OnChannelError;
                        m_Channel.m_ConnectCallback -= this.OnConnect;
                        m_Channel.Clear();
                    }
                    m_Channel = value;
                    if (m_Channel != null)
                    {
                        RemoteAddress = m_Channel.RemoteAddress;
                        m_Channel.m_ReadCallback += this.OnChannelRead;
                        m_Channel.m_ErrorCallback += this.OnChannelError;
                        m_Channel.m_ConnectCallback += this.OnConnect;
                    }
                }
            }
        }

        public readonly SortedDictionary<int, RpcInfo> RequestRPCCallbacks = new SortedDictionary<int, RpcInfo>();
        public int RpcID//todo: 区分不同线程的rpcid
        {
            get;
            set;
        }
        public IPEndPoint RemoteAddress
        {
            get;
            set;
        }
        public long LastRecvTime
        {
            get;
            set;
        }

        public long LastSendTime
        {
            get;
            set;
        }
        public Action m_ChannelConnectCallback;
        public Action<Packet> m_ChannelReadCallback;
        public Action<Session> m_ReadErrorCallback;
        public Action<Session> m_ExcuteErrorCallback;

        public static ushort s_BufferSampleHoldCount = 128;
        public StatisticalList m_BufferStatisticalList = new StatisticalList(s_BufferSampleHoldCount);

        public static ulong s_BufferMinBaseline = 1024 * 1024;
        public static ulong s_BufferMaxIncreaseAmount = 16 * 1024 * 1024;
        
        public ulong s_BufferAlarmValue = 16 * 1024 * 1024;// 1024 * 1024
        
        public ValueMonitor m_BufferMonitor = new ValueMonitor(s_BufferMinBaseline, s_BufferMaxIncreaseAmount);
    }

    [ComponentSystem(typeof(Session))]
    public static class SessionSystem
    {
        [MethodAwake]
        public static void OnAwake(Session self)
        {
            if (self == null) return;
            self.Channel = null;
            long now = ATimer.Instance.SystemTicks;
            self.LastRecvTime = now;
            self.LastSendTime = now;
            self.RequestRPCCallbacks.Clear();
            self.m_BufferStatisticalList.Clear();
            self.m_BufferMonitor.Clear();
            Log.Debug($"session create");
        }

		[MethodAwake1]
		public static void OnAwake1(Session self, TChannel channel)
		{
            if (self == null) return;
            self.Channel = channel;
			long now = ATimer.Instance.SystemTicks;
			self.LastRecvTime = now;
			self.LastSendTime = now;
			self.RequestRPCCallbacks.Clear();
            self.m_BufferStatisticalList.Clear();
            self.m_BufferMonitor.Clear();
            Log.Debug($"session create");
		}

		[MethodDestroy]
        public static void OnDestroy(Session self)
        {
            if (self == null) return;
            self.m_ReceivedPacketQueue.Clear();

            string remoteAddress = (self.RemoteAddress != null) ? self.RemoteAddress.ToString() : "NONE";

            self.m_ChannelConnectCallback = null;
            self.m_ChannelReadCallback = null;
            self.m_ReadErrorCallback = null;
            self.m_ExcuteErrorCallback = null;
            self.Channel = null;
/*
            foreach (RpcInfo action in self.RequestRPCCallbacks.Values.ToArray())
            {
                IResponse response = MessageIDComponent.Instance.CreateResponse(action.Request);
                response.Error = (int)FError.SocketErr;
                response.Message = $"Socket Error, request: {action.Request.GetType().Name} response: {response.GetType().Name}";
                action.Tcs.SetResult(response);
            }
*/
            self.RequestRPCCallbacks.Clear();
            self.Address.SetAsNone();

            Log.Debug($"session dispose:{remoteAddress}");
        }

        [MethodFrequentlyUpdate]
        public static void OnFrequentlyUpdate(Session self)
        {
            if (self == null) return;
            if (self.Channel != null)
            {
                self.Channel.Update();

                self.DispatchPacket();
                return;
            }
        }

        [MethodUpdate]
        public static void OnUpdate(Session self)
        {
            if (self == null) return;
            self.CheckRPCTimeout();
            self.CheckSendBufferLength();
        }

        public static void OnConnect(this Session self)
        {
            if (self == null) return;
            if (self.m_ChannelConnectCallback != null)
            {
                self.m_ChannelConnectCallback.Invoke();
            }
        }

        public static void OnChannelError(this Session self)
        {
            if (self == null) return;
            self.Channel = null;
            if (self.m_ReadErrorCallback != null)
            {
                self.m_ReadErrorCallback.Invoke(self);
            }
        }

        public static void OnChannelRead(this Session self, Packet packet)
        {
            if (self == null) return;
            PacketDispatcherManager.OnRead(packet, self);
            self.m_ChannelReadCallback?.Invoke(packet);
            self.m_ReceivedPacketQueue.Enqueue(packet);
        }

        public static void DispatchPacket(this Session self)
        {
            if (self == null) return;
            if (self.m_ReceivedPacketQueue.Count > 0)
            {
                if (Session.Interrupted)
                {
                    Session.Interrupted = false;
                }
                while (self.m_ReceivedPacketQueue.TryDequeue(out Packet packet))
                {
                    PacketDispatcherManager.Dispatch(packet, self);
                    if (Session.Interrupted)
                    {
                        Session.Interrupted = false;
                        return;
                    }
                }
            }
        }

        public static void OnRead(this Session self, int rpcID, IResponse response)
        {
            if (self == null || response == null) return;
            if (!self.RequestRPCCallbacks.TryGetValue(rpcID, out var action))
            {
                return;
            }

            self.RequestRPCCallbacks.Remove(rpcID);
            if (response.Error > 0)
            {
                action.Tcs.SetException(new FrameworkException($"Rpc error, request: {action.Request.ToString()} response: {response.ToString()}"));
                return;
            }
            action.Tcs.SetResult(response);
        }

        public static void OnExcuteError(this Session self)
        {
            if (self == null) return;
            if (self.m_ExcuteErrorCallback != null)
            {
                self.m_ExcuteErrorCallback.Invoke(self);
            }
        }

        public static async ATask<IResponse> SendAsync(this Session self, Packet packet)
        {
            if (self == null || packet == null) return null;
            int rpcId = ++self.RpcID;
            RpcInfo rpcInfo = new RpcInfo(packet.GetMessage() as IRequest);
            self.RequestRPCCallbacks[rpcId] = rpcInfo;

            packet.RpcID = rpcId;
            packet.SourceAddress.CopyFrom(self.Address);
            PacketDispatcherManager.Dispatch(packet, self);

            return await rpcInfo.Tcs;
        }

        public static void Send(this Session self, Packet packet)
        {
            if (self == null) return;
            PacketDispatcherManager.OnSend(packet, self);

            if (self.Channel != null)
            {
                self.Channel.Send(packet);
            }
        }

        private static void CheckRPCTimeout(this Session self)
        {
            if (self.RequestRPCCallbacks.Count > 0)
            {
                List<int> removeList = new List<int>();
                {
                    long currentTime = ATimer.Instance.SystemTicks;
                    foreach (KeyValuePair<int, RpcInfo> keyValuePair in self.RequestRPCCallbacks)
                    {
                        if (currentTime > keyValuePair.Value.GiveUpTime)
                        {
                            removeList.Add(keyValuePair.Key);
                        }
                        else
                        {
                            break;
                        }
                    }
                }
                foreach (int rpcID in removeList)
                {
                    if (self.RequestRPCCallbacks.TryGetValue(rpcID, out RpcInfo rpcInfo))
                    {
                        self.RequestRPCCallbacks.Remove(rpcID);

                        IResponse response = MessageIDComponent.Instance.CreateResponse(rpcInfo.Request);
                        {
                            response.Error = (int)FError.RPCTimeOutErr;
                            response.Message = $"RPC Timeout, request: {rpcInfo.Request.GetType().Name} response: {response.GetType().Name}";
                        }
                        rpcInfo.Tcs.SetResult(response);
                    }
                }
            }
        }

        private static void CheckSendBufferLength(this Session self)
        {
            const ulong byteCountPerMB = 1024 * 1024;

            if (self.Channel != null)
            {
                self.m_BufferStatisticalList.AddSample(self.Channel.SendBufferLength);
                long averageLength = self.m_BufferStatisticalList.RecentAverage;
                if (self.m_BufferMonitor.CheckCross(averageLength, out bool bAbove, out ulong nCrossLine))
                {
                    string remoteAddress = (self.RemoteAddress != null) ? self.RemoteAddress.ToString() : "NONE";
                    if (bAbove)
                    {
                        ulong AbovePreMB = nCrossLine / byteCountPerMB;
                        Log.Warning($"[Network] Session (Address = {remoteAddress}) Send Buffer Length Recently Rise Above {AbovePreMB} MB!");
                        if (nCrossLine >= self.s_BufferAlarmValue)
                        {
                            Log.Error($"[Network] Session (Address = {remoteAddress}) Send Buffer Length Recently Rise Above  {AbovePreMB} MB!  nCrossLine >= {self.s_BufferAlarmValue}  OnChannelError!");
                            self.OnChannelError();
                        }
                    }
                    else
                    {
                        Log.Info($"[Network] Session (Address = {remoteAddress}) Send Buffer Length Recently Fall Below {nCrossLine / byteCountPerMB} MB!");
                    }
                }
            }
        }
    }
}
