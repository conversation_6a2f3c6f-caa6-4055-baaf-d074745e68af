﻿using BattleServer.config;
using Aurora.Framework;
using BattleServer.Framework;
using BattleServer.Nats;
using BattleServer.Server;
using BattleServer.Service;
using BattleServer.Game.Core;
using CommandLine;
using CommandLine.Text;
using BattleServer.Nats.SerializerRegistry;
using NATS.Client.Core;
using System;
using BattleServer.src.service;
using Game.Core;
using System.Diagnostics;

namespace BattleServer;

class ProgramOptions
{
    [Option( "config", Required = false,Default = "../configs/battleserver.yaml", HelpText = "Set the battleserver.yaml path.")]
    public string BattleYamlPath { get; set; }

    [Option( "commconf", Required = false,Default = "../configs/config.yaml", HelpText = "Set the common.yaml path..")]
    public string CommonYamlPath { get; set; }

}

class Program
{
    public static void Main(string[] args)
    {
        try
        {
            CommandLineParser.ParseArgs(args);
            MainThread.Instance.Init(GlobalArgs.APP_CONFIG_PATH);
            MainThread.Instance.InitGCSetting();
            MainThread.Instance.AfterInitCheck();



            Parser.Default.ParseArguments<ProgramOptions>(args)
                .WithParsed(options =>
                {
                    ConfigHelper.Config = ConfigHelper.InitConfig<Config>(options.BattleYamlPath);
                    ConfigHelper.Config.Common = ConfigHelper.InitConfig<CommonConfig>(options.CommonYamlPath);
                });


            TableManager.Instance.InitAll();


            var opts = new NatsOpts
            {
                Url = ConfigHelper.Config.Common.Nats.Servers,
                SerializerRegistry = new ProtoBufSerializerRegistry(),
                Name = "BattleServer",
            };
            NatsConnection Nats = new NatsConnection(opts);

            // 初始化 NATS 客户端（发送消息）
            if (ConfigHelper.Config.Nats != null)
            {
                NatsClient.InitNatsRpcClient(Nats);
                Log.Warning("NATS RPC Client initialized successfully");
            }
            else
            {
                Log.Warning("NATS client configuration not found, using server connection for client");
                NatsClient.InitNatsRpcClient(Nats);
            }

       
            //放在建立监听之前
            MainThread.Instance.CheckNetAddress();
           // ServiceManager.Instance.ServiceFactory = new ServerServiceFactory();

            SceneManager.Instance.Init();
            ThreadManager.Instance.StartWorkUnit(SceneManager.Instance.WorkUnit, ThreadType.EXCLUSIVE_THREAD);

            ServiceManager.Instance.Init();
            ThreadManager.Instance.StartWorkUnit(ServiceManager.Instance.WorkUnit, ThreadType.EXCLUSIVE_THREAD);

            Log.Warning($"-------------【服务器程序加载完毕】-----------------------");
            Log.Warning($"------------------------------------------------------");

            Process cur = Process.GetCurrentProcess();
            string FILE_NAME = "battleserver.pid";
            using (FileStream fs = new FileStream(FILE_NAME, FileMode.OpenOrCreate))
            {
                using (StreamWriter w = new StreamWriter(fs))
                {
                    w.WriteLine(cur.Id);
                }
            }

            // 开始主循环
            MainThread.Instance.Loop();

            Log.Warning($"------------------------------------------------------");
            Log.Warning($"-------------【服务器程序即将释放】-----------------------");

            MainThread.Instance.Release();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Main program catch exception! Fatal Error!\n{ex.ToString()}");
            //Utility.FatalErrorPrint(ex.ToString());
        }
    }

}