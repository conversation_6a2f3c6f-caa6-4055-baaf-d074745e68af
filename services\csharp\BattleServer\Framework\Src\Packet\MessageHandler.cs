﻿//*********************************************************
// Framework
// Author:  Jasen
// Date  :  2022-11-16
//*********************************************************

using System;
using System.Globalization;
using System.Reflection;
using System.Runtime.CompilerServices;

namespace Aurora.Framework
{

    public static class MsgHandlerBinder
    {
        public static IMessageHandler Bind(Type msgType, MethodInfo method)
        {
            if (msgType == null || method == null) return null;
            IMessageHandler callbackObj = null;

            ParameterInfo[] parameters = method.GetParameters();

            if (parameters.Length != 2 && parameters.Length != 4)
            {
                Log.Error($"消息处理函数绑定错误:参数长度不匹配,Type=[{msgType.Name}],Method=[{method.Name}]");
                return null;
            }

            if (parameters[1].ParameterType != msgType)
            {
                Log.Error($"消息处理函数绑定错误:接收消息类型不匹配,Type=[{msgType.Name}],Method=[{method.Name}]");
                return null;
            }

            bool bRequest = (parameters.Length == 4);
            bool bAsync = (method.GetCustomAttribute<AsyncStateMachineAttribute>() != null);

            try
            {
                if (bRequest)
                {
                    Type[] argTypes = new Type[2];
                    argTypes[0] = parameters[1].ParameterType;
                    argTypes[1] = parameters[2].ParameterType;

                    if (typeof(IRequest).IsAssignableFrom(argTypes[0]))
                    {
                        if (MessageIDComponent.Instance.GetResponseType(argTypes[0]) == argTypes[1])
                        {
                            if (bAsync)
                            {
                                Type actionType = typeof(MsgRequestAsyncCallback<,>).MakeGenericType(argTypes);
                                Type callbackType = typeof(MsgRequestAsyncHandler<,>).MakeGenericType(argTypes);
                                callbackObj = Activator.CreateInstance(callbackType, new object[] { Delegate.CreateDelegate(actionType, method) }) as IMessageHandler;
                            }
                            else
                            {
                                Type actionType = typeof(MsgRequestCallback<,>).MakeGenericType(argTypes);
                                Type callbackType = typeof(MsgRequestHandler<,>).MakeGenericType(argTypes);
                                callbackObj = Activator.CreateInstance(callbackType, new object[] { Delegate.CreateDelegate(actionType, method) }) as IMessageHandler;
                            }
                        }
                        else
                        {
                            Log.Error($"消息处理函数绑定错误:应答消息类型不匹配,Type=[{msgType.Name}],Method=[{method.Name}]");
                            return null;
                        }
                    }
                    else
                    {
                        Log.Error($"消息处理函数绑定错误:仅支持请求类消息,Type=[{msgType.Name}],Method=[{method.Name}]");
                        return null;
                    }
                }
                else
                {
                    Type[] argTypes = new Type[1];
                    argTypes[0] = parameters[1].ParameterType;

                    if (typeof(IResponse).IsAssignableFrom(argTypes[0]))
                    {
                        Log.Error($"消息处理函数绑定错误:不支持应答类消息,Type=[{msgType.Name}],Method=[{method.Name}]");
                        return null;
                    }
                    else
                    {
                        if (bAsync)
                        {
                            Type actionType = typeof(MsgAsyncCallback<>).MakeGenericType(argTypes);
                            Type callbackType = typeof(MsgAsyncHandler<>).MakeGenericType(argTypes);
                            callbackObj = Activator.CreateInstance(callbackType, new object[] { Delegate.CreateDelegate(actionType, method) }) as IMessageHandler;
                        }
                        else
                        {
                            Type actionType = typeof(MsgCallback<>).MakeGenericType(argTypes);
                            Type callbackType = typeof(MsgHandler<>).MakeGenericType(argTypes);
                            callbackObj = Activator.CreateInstance(callbackType, new object[] { Delegate.CreateDelegate(actionType, method) }) as IMessageHandler;
                        }
                    }
                }
            }
            catch (Exception e)
            {
                Log.Exception($"消息处理函数绑定异常:Type=[{msgType.Name}],Method=[{method.Name}],Msg={e.Message}");
            }

            return callbackObj;
        }
    }

    public static class ActorMsgHandlerBinder
    {
        public static IActorMessageHandler Bind(Type msgType, MethodInfo method)
        {
            if (msgType == null || method == null) return null;
            IActorMessageHandler callbackObj = null;
            ParameterInfo[] parameters = method.GetParameters();
            if (parameters.Length != 2 && parameters.Length != 4)
            {
                Log.Error($"Actor消息处理函数绑定错误:参数长度不匹配，Type=[{msgType.Name}],Method=[{method.Name}]");
                return null;
            }
            bool bAsync = (parameters.Length == 4);
            if (parameters[1].ParameterType != msgType)
            {
                Log.Error($"Actor消息处理函数绑定错误:消息类型不匹配，Type=[{msgType.Name}],Method=[{method.Name}]");
                return null;
            }

            try
            {
                if (!bAsync)
                {
                    Type[] argTypes = new Type[2];
                    //取第一二个参数类型-消息类型
                    argTypes[0] = parameters[0].ParameterType;
                    argTypes[1] = parameters[1].ParameterType;
                    Type actionType = typeof(ActorMsgCallback<,>).MakeGenericType(argTypes);
                    Type callbackType = typeof(ActorMsgHandler<,>).MakeGenericType(argTypes);
                    callbackObj = Activator.CreateInstance(callbackType, new object[] { Delegate.CreateDelegate(actionType, method) }) as IActorMessageHandler;
                }
                else
                {
                    Type[] argTypes = new Type[3];
                    //取第二三个参数类型-消息类型
                    argTypes[0] = parameters[0].ParameterType;
                    argTypes[1] = parameters[1].ParameterType;
                    argTypes[2] = parameters[2].ParameterType;
                    Type actionType = typeof(ActorAsyncCallback<,,>).MakeGenericType(argTypes);
                    Type callbackType = typeof(ActorAsyncHandler<,,>).MakeGenericType(argTypes);
                    callbackObj = Activator.CreateInstance(callbackType, new object[] { Delegate.CreateDelegate(actionType, method) }) as IActorMessageHandler;
                }
            }
            catch (Exception e)
            {
                Log.Exception($"Actor消息处理函数绑定异常:Type=[{msgType.Name}],Method=[{method.Name}],Msg={e.Message}");
            }
            return callbackObj;
        }
    }

    public delegate void MsgCallback<Message>(Session session, Message message);
    public class MsgHandler<Message> : IMessageHandler where Message : class, IMessage
    {
        private MsgCallback<Message> Callback = null;

        public MsgHandler(MsgCallback<Message> callback)
        {
            Callback = callback;
        }

        public void Handle(Session session, Packet pkt)
        {
            if (Callback != null)
            {
                if (pkt == null) return;
                IMessage msg = pkt.GetMessage();
                //string msgName = msg.GetType().Name;
                //GameAnalysisFramework.StartWatchTime(msgName);
                Callback.Invoke(session, msg as Message);
                //GameAnalysisFramework.StopWatchTime(msgName, GameAnalysisFramework.MethodAnalysis.MsgCallBack);
            }
        }
    }

    public delegate ATask MsgAsyncCallback<Message>(Session session, Message message);
    public class MsgAsyncHandler<Message> : IMessageHandler where Message : class, IMessage
    {
        private MsgAsyncCallback<Message> Callback = null;

        public MsgAsyncHandler(MsgAsyncCallback<Message> callback)
        {
            Callback = callback;
        }

        public void Handle(Session session, Packet pkt)
        {
            if (Callback != null)
            {
                if (pkt == null) return;
                IMessage msg = pkt.GetMessage();
                Callback.Invoke(session, msg as Message).Coroutine();
            }
        }
    }

    public delegate void MsgRequestCallback<Request, Response>(Session session, Request message, Response response, Action reply);
    public class MsgRequestHandler<Request, Response> : IMessageHandler where Request : class, IRequest where Response : class, IResponse
    {
        private MsgRequestCallback<Request, Response> Callback = null;

        public MsgRequestHandler(MsgRequestCallback<Request, Response> callback)
        {
            Callback = callback;
        }

        public void Handle(Session session, Packet pkt)
        {
            if (pkt == null) return;
            Request request = pkt.GetMessage() as Request;
            if (request == null)
            {
                string msgName = pkt.MsgID.ToString(CultureInfo.InvariantCulture);
                Type msgType = MessageIDComponent.Instance.GetMsgType(pkt.MsgID);
                if (msgType != null)
                {
                    msgName = msgType.Name;
                }
                throw new FrameworkException($"Request Is Null! Message Name = {msgName}.");
            }

            int rpcID = pkt.RpcID;
            Response response = Activator.CreateInstance<Response>();
            response.Replyed = false;

            MessageAddress replyAddress = new MessageAddress();
            replyAddress.CopyFrom(pkt.SourceAddress);

            void Reply()
            {
                if (rpcID != 0)
                {
                    Packet packet = Packet.Create(response);
                    {
                        packet.RpcID = rpcID;
                        packet.TargetAddress.CopyFrom(replyAddress);
                    }
                    PacketDispatcherManager.Dispatch(packet, session);
                }
                response.Replyed = true;
            }

            try
            {
                if (Callback != null)
                {
                    //IMessage msg = pkt.GetMessage();
                    //string msgName = msg.GetType().Name;
                    //GameAnalysisFramework.StartWatchTime(msgName);
                    Callback.Invoke(session, request, response, Reply);
                    //GameAnalysisFramework.StopWatchTime(msgName, GameAnalysisFramework.MethodAnalysis.MsgCallBack);

                }
            }
            catch (Exception exception)
            {
                Log.Error($"Exception:{exception.Message}\nStackTrace:\n{exception.StackTrace}");
                if (false == response.Replyed)
                {
                    response.Error = (int)FError.RpcFailErr;
                    response.Message = exception.ToString();
                    Reply();
                }
                throw;
            }
        }
    }

    public delegate ATask MsgRequestAsyncCallback<Request, Response>(Session session, Request message, Response response, Action reply);
    //异步消息处理
    public class MsgRequestAsyncHandler<Request, Response> : IMessageHandler where Request : class, IRequest where Response : class, IResponse
    {
        private MsgRequestAsyncCallback<Request, Response> Callback = null;

        public MsgRequestAsyncHandler(MsgRequestAsyncCallback<Request, Response> callback)
        {
            Callback = callback;
        }

        public void Handle(Session session, Packet pkt)
        {
            HandAsync(session, pkt).Coroutine();
        }

        public async ATask HandAsync(Session session, Packet pkt)
        {
            if (pkt == null) return;
            Request request = pkt.GetMessage() as Request;
            if (request == null)
            {
                string msgName = pkt.MsgID.ToString(CultureInfo.InvariantCulture);
                Type msgType = MessageIDComponent.Instance.GetMsgType(pkt.MsgID);
                if (msgType != null)
                {
                    msgName = msgType.Name;
                }
                throw new FrameworkException($"Request Is Null! Message Name = {msgName}.");
            }

            int rpcID = pkt.RpcID;
            Response response = Activator.CreateInstance<Response>();
            response.Replyed = false;

            MessageAddress replyAddress = new MessageAddress();
            replyAddress.CopyFrom(pkt.SourceAddress);

            void Reply()
            {
                if (rpcID != 0)
                {
                    Packet packet = Packet.Create(response);
                    {
                        packet.RpcID = rpcID;
                        packet.TargetAddress.CopyFrom(replyAddress);
                    }
                    PacketDispatcherManager.Dispatch(packet, session);
                }
                response.Replyed = true;
            }

            try
            {
                if (Callback != null)
                {
                    await Callback.Invoke(session, request, response, Reply);
                }
            }
            catch (Exception exception)
            {
                Log.Error($"Exception: {exception.Message}\nStackTrace:\n{exception.StackTrace}");
                if (false == response.Replyed)
                {
                    response.Error = (int)FError.SocketErr;
                    response.Message = exception.ToString();
                    Reply();
                }
                throw;
            }
        }
    }

    //异步ActorMessage，无回调
    public delegate ATask ActorMsgCallback<Entity, Message>(Entity entity, Message message);
    public class ActorMsgHandler<E, Message> : IActorMessageHandler where E : Entity where Message : class, IMessage
    {
        private ActorMsgCallback<E, Message> Callback = null;
        public ActorMsgHandler(ActorMsgCallback<E, Message> callback)
        {
            Callback = callback;
        }
        public async ATask Handle(Entity entity, Session session, Packet pkt)
        {
            if (pkt == null) return;
            Message request = pkt.GetMessage() as Message;
            if (request == null)
            {
                string msgName = pkt.MsgID.ToString(CultureInfo.InvariantCulture);
                Type msgType = MessageIDComponent.Instance.GetMsgType(pkt.MsgID);
                if (msgType != null)
                {
                    msgName = msgType.Name;
                }
                throw new FrameworkException($"消息类型转换错误: msg name:{msgName}");
            }
            try
            {
                await Callback.Invoke(entity as E,request);
            }
            catch (Exception exception)
            {
                Log.Error($"Exception:{exception.Message}\nStackTrace:\n{exception.StackTrace}");
            }
        }
    }

    //异步ActorMessage RPC 处理回调
    public delegate ATask ActorAsyncCallback<Entity, Request, Response>(Entity entity, Request message, Response response, Action reply);

    //异步消息处理
    public class ActorAsyncHandler<E, Request, Response> : IActorMessageHandler where E : Entity where Request : class, IRequest where Response : class, IResponse
    {
        private ActorAsyncCallback<E, Request, Response> Callback = null;
        public ActorAsyncHandler(ActorAsyncCallback<E, Request, Response> callback)
        {
            Callback = callback;
        }
        public async ATask Handle(Entity entity, Session session, Packet pkt)
        {
            if (pkt == null) return;
            Request request = pkt.GetMessage() as Request;
            if (request == null)
            {
                string msgName = pkt.MsgID.ToString(CultureInfo.InvariantCulture);
                Type msgType = MessageIDComponent.Instance.GetMsgType(pkt.MsgID);
                if (msgType != null)
                {
                    msgName = msgType.Name;
                }
                throw new FrameworkException($"消息类型转换错误: Type={msgName}");
            }
            int rpcID = pkt.RpcID;
            Response response = Activator.CreateInstance<Response>();

            void Reply()
            {
                if (rpcID != 0)
                {
                    Packet packet = Packet.Create(response);
                    {
                        packet.RpcID = rpcID;
                        packet.TargetAddress.CopyFrom(pkt.SourceAddress);
                    }
                    PacketDispatcherManager.Dispatch(packet, session);
                }
            }
            try
            {
                await Callback.Invoke(entity as E, request, response, Reply);
            }
            catch (Exception exception)
            {
                Log.Error($"Exception:{exception.Message}\nStackTrace:\n{exception.StackTrace}");
                response.Error = (int)FError.SocketErr;
                response.Message = exception.ToString();
                Reply();
            }
        }
    }
}
