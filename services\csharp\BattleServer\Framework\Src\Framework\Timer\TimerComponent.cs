﻿//*********************************************************
// Framework
// Author:  Jasen
// Date  :  2022-11-8
//*********************************************************


using System;
using System.Collections.Generic;

namespace Aurora.Framework
{
    public class TimerTrigger : Entity
    {
        public long ID
        {
            get;
            set;
        }
        public ITimerHandler Handle { get; set; }
        public long ExpireTime { get; set; } //未来某时刻，到期时间
        public long Interval { get; set; }   //时间间隔
        public TimerType TimeType { get; set; }
        public static TimerTrigger Create(ITimerHandler th, long interval, TimerType tp)
        {
            //注意：一定要用池创建，销毁时会放入池中
            TimerTrigger timeTrigger = ReferencePool.Acquire<TimerTrigger>();
            timeTrigger.Handle = th;
            timeTrigger.Interval = interval;
            timeTrigger.ExpireTime = ATimer.Instance.SystemTicks + interval;
            timeTrigger.TimeType = tp;
            return timeTrigger;
        }
    }
    public interface ICancellationToken
    {
        public void Add(Action callback)
        {
        }

        public void Remove(Action callback)
        {
        }
    }
    public class TimerComponent : BaseComponent, IAwake, IUpdate, IDestroy
    {
        public long LastTriggerID { get; set; }

        //每帧调用的时间触发器的ID
        public readonly List<long> FrameTimerList = new List<long>();
        public readonly List<long> FrameTimerListTemp = new List<long>();
        //到期时间--时间触发器ID
        public readonly Dictionary<long, long> TimerIDExpire = new Dictionary<long, long>();

        public Dictionary<long, Entity> EntityDictionary = new Dictionary<long, Entity>();

        //带参数的构造函数，采用静态Create的函数
        public static TimerComponent Create()
        {
            //注意：一定要用池创建，销毁时会放入池中
            TimerComponent comp = ReferencePool.Acquire<TimerComponent>();
            return comp;
        }
    }

    [ComponentSystem(typeof(TimerComponent))]
    public static class TimerComponentSystem
    {
        //加入组件后，使用前初始化
        [MethodAwake]
        public static void OnAwake(TimerComponent self)
        {
            //Log.Debug("TimerComponentSystem OnAwake");
        }

        //当前对象释放时调用
        [MethodDestroy]
        public static void OnDestroy(TimerComponent self)
        {
            //Log.Debug("TimerComponentSystem OnDestroy");
            if (self == null) return;

            self.LastTriggerID = 0;

            self.FrameTimerList.Clear();
            self.FrameTimerListTemp.Clear();
            self.TimerIDExpire.Clear();
            self.EntityDictionary.Clear();
        }

        //更新调用
        [MethodUpdate]
        public static void OnUpdate(TimerComponent self)
        {
            if (self == null) return;
            //每帧调用，触发逻辑时，有可能外部也修改到当前的迭代器数据
            self.FrameTimerListTemp.Clear();
            self.FrameTimerListTemp.AddRange(self.FrameTimerList);
            Entity entity = null;
            TimerTrigger trigger = null;
            foreach (var timerid in self.FrameTimerListTemp)
            {
                if (self.EntityDictionary.TryGetValue(timerid, out entity))
                {
                    trigger = entity as TimerTrigger;
                    if (trigger != null)
                    {
                        //确保触发器没有被释放，是正常使用的
                        if (trigger.Handle != null)
                        {
                            trigger.Handle.Excute();
                        }
                        else
                        {
                            self.FrameTimerList.Remove(timerid);
                        }
                    }
                }
            }

            //遍历得到已经到期的时间ID
            self.FrameTimerListTemp.Clear();
            long now = ATimer.Instance.SystemTicks;
            foreach (var pair in self.TimerIDExpire)
            {
                //时间已到期的TimeID加入列表
                if (pair.Value < now)
                {
                    self.FrameTimerListTemp.Add(pair.Key);
                }
            }

            foreach (var timerid in self.FrameTimerListTemp)
            {
                self.TimerIDExpire.Remove(timerid);

                // TimerTrigger trigger = self.GetChild<TimerTrigger>(timerid);
                if (self.EntityDictionary.TryGetValue(timerid, out entity))
                {
                    trigger = entity as TimerTrigger;
                    if (trigger != null)
                    {
                        //确保触发器没有被释放，是正常使用的
                        if (trigger.Handle != null)
                        {
                            trigger.Handle.Excute();
                            if (trigger.TimeType == TimerType.Repeated)
                            {
                                //重新设置时间
                                trigger.ExpireTime = now + trigger.Interval;
                                self.InnerAddTimer(trigger);
                            }
                            else
                            {
                                //删除
                                self.EntityDictionary.Remove(trigger.ID);
                                trigger.Release();
                            }
                        }
                    }
                }
            }
        }

        private static long GenerateTriggerID(this TimerComponent self)
        {
            return ++self.LastTriggerID;
        }

        public static void RemoveTimer(this TimerComponent self, long timeTriggerId)
        {
            if (self == null)
                return;

            Entity entity = null;
            TimerTrigger trigger = null;
            if (self.EntityDictionary.TryGetValue(timeTriggerId, out entity))
            {
                trigger = entity as TimerTrigger;
                if (trigger == null)
                {
                    return;
                }

                if (trigger.TimeType == TimerType.Frame)
                    self.FrameTimerList.Remove(timeTriggerId);
                else
                    self.TimerIDExpire.Remove(timeTriggerId);

                trigger.Release();
                self.EntityDictionary.Remove(timeTriggerId);
                return;
            }
        }

        private static void InnerAddTimer(this TimerComponent self, TimerTrigger timerTrigger)
        {
            if (self == null || timerTrigger == null)
                return;
            if (timerTrigger.TimeType == TimerType.Frame)
            {
                self.FrameTimerList.Add(timerTrigger.ID);
                return;
            }
            if (!self.TimerIDExpire.ContainsKey(timerTrigger.ID))
            {
                self.TimerIDExpire.Add(timerTrigger.ID, timerTrigger.ExpireTime);
            }
        }
        //等待一次回调，基于BaseComponent
        public static long AddOnceTimer<T>(this TimerComponent self, long ms, Action<T> callback, T obj) where T : BaseComponent
        {
            TimerHandler<T> th = new TimerHandler<T>(obj, callback);
            TimerTrigger timeTrigger = TimerTrigger.Create(th, ms, TimerType.Once);
            if (self == null) return timeTrigger.ID;
            self.AddChild(timeTrigger, (Entity entity) => {
                TimerTrigger trigger = entity as TimerTrigger;
                if (trigger != null)
                {
                    trigger.ID = self.GenerateTriggerID();
                    self.EntityDictionary.Add(trigger.ID, trigger);
                }
            });
            self.InnerAddTimer(timeTrigger);
            return timeTrigger.ID;
        }
        //等待一次回调，带特定参数T的回调，可以是Object或Static函数
        public static long AddOnceTimerObject<T>(this TimerComponent self, long ms, Action<T> callback, T obj) where T : class
        {
            ObjectTimerHandler<T> th = new ObjectTimerHandler<T>(obj, callback);
            TimerTrigger timeTrigger = TimerTrigger.Create(th, ms, TimerType.Once);
            if (self == null) return timeTrigger.ID;
            self.AddChild(timeTrigger, (Entity entity) => {
                TimerTrigger trigger = entity as TimerTrigger; 
                trigger.ID = self.GenerateTriggerID();
                self.EntityDictionary.Add(trigger.ID, trigger);
            });
            self.InnerAddTimer(timeTrigger);
            return timeTrigger.ID;
        }
        //一个重复回调，基于BaseComponent
        public static long AddRepeatTimer<T>(this TimerComponent self, long ms, Action<T> callback, T obj) where T : BaseComponent
        {
            TimerHandler<T> th = new TimerHandler<T>(obj, callback);
            TimerTrigger timeTrigger = TimerTrigger.Create(th, ms, TimerType.Repeated);
            if (self == null) return timeTrigger.ID;
            self.AddChild(timeTrigger, (Entity entity) => {
                TimerTrigger trigger = entity as TimerTrigger; 
                trigger.ID = self.GenerateTriggerID();
                self.EntityDictionary.Add(trigger.ID, trigger);
            });
            self.InnerAddTimer(timeTrigger);
            return timeTrigger.ID;
        }
        //一个重复回调，带特定参数T的回调，可以是Object或Static函数
        public static long AddRepeatTimerObject<T>(this TimerComponent self, long ms, Action<T> callback, T obj) where T : class
        {
            ObjectTimerHandler<T> th = new ObjectTimerHandler<T>(obj, callback);
            TimerTrigger timeTrigger = TimerTrigger.Create(th, ms, TimerType.Repeated);
            if (self == null) return timeTrigger.ID;
            self.AddChild(timeTrigger, (Entity entity) => {
                TimerTrigger trigger = entity as TimerTrigger; 
                trigger.ID = self.GenerateTriggerID();
                self.EntityDictionary.Add(trigger.ID, trigger);
            });
            self.InnerAddTimer(timeTrigger);
            return timeTrigger.ID;
        }
        //异步等待多久时间
        public static async ATask<bool> WaitTime(this TimerComponent self, long ms)
        {
            if (self == null) return false;
            ATask<bool> timerTask = new ATask<bool>();
            AsyncTimerHandler th = new AsyncTimerHandler(timerTask);
            TimerTrigger timeTrigger = TimerTrigger.Create(th, ms, TimerType.OnceWait);
            self.AddChild(timeTrigger, (Entity entity) => {
                TimerTrigger trigger = entity as TimerTrigger; 
                trigger.ID = self.GenerateTriggerID();
                self.EntityDictionary.Add(trigger.ID, trigger);
            });
            self.InnerAddTimer(timeTrigger);
            //加入到TimerComponent处理，等待异步结果
            bool result = false;
            try
            {
                result = await timerTask;
            }
            catch (Exception e)
            {
                Log.Exception($"Timer component async wait timer error, message:{e.Message}");
            }
            return result;
        }
        //异步等待多久时间
        public static async ATask<bool> WaitTime(this TimerComponent self, long ms, ICancellationToken cancellationToken)
        {
            if (self == null) return false;
            ATask<bool> timerTask = new ATask<bool>();
            AsyncTimerHandler th = new AsyncTimerHandler(timerTask);
            TimerTrigger timeTrigger = TimerTrigger.Create(th, ms, TimerType.OnceWait);
            self.AddChild(timeTrigger, (Entity entity) => {
                TimerTrigger trigger = entity as TimerTrigger;
                trigger.ID = self.GenerateTriggerID();
                self.EntityDictionary.Add(trigger.ID, trigger);
            });
            self.InnerAddTimer(timeTrigger);

            void CancelAction()
            {
                //if (self.Remove(timerId))
                self.RemoveTimer(timeTrigger.ID);
                {
                    timerTask.SetResult(true);
                }
            }

            //加入到TimerComponent处理，等待异步结果
            bool result = false;
            try
            {
                cancellationToken?.Add(CancelAction);
                result = await timerTask;
            }
            catch (Exception e)
            {
                Log.Exception($"TimerTriggerID component async wait timer error, message:{e.Message}");
            }
            finally
            {
                cancellationToken?.Remove(CancelAction);
            }
            return result;
        }
        //异步等待一帧
        public static async ATask<bool> WaitFrame(this TimerComponent self)
        {
            bool result = await self.WaitTime(1);
            return result;
        }
    }
}
