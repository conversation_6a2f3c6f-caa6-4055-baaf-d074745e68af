﻿//*********************************************************
// Framework
// Author:  Jasen
// Date  :  2022-11-4
//*********************************************************


using Aurora.Framework;
using System;

namespace BattleServer.Server
{
    public class ConsoleLogger : ILog
    {
        private LogLevel m_LogLevel = LogLevel.Debug;
        public ConsoleLogger(LogLevel lvl = LogLevel.Debug)
        {
            m_LogLevel = lvl;
            switch (m_LogLevel)
            {
                case LogLevel.Exception:
                    Console.ForegroundColor = ConsoleColor.Blue;
                    break;
                case LogLevel.Error:
                    Console.ForegroundColor = ConsoleColor.Red;
                    break;
                case LogLevel.Warning:
                    Console.ForegroundColor = ConsoleColor.Yellow;
                    break;
                case LogLevel.Info:
                    Console.ForegroundColor = ConsoleColor.Green;
                    break;
                default:
                    Console.ForegroundColor = ConsoleColor.White;
                    break;
            }
        }
        private bool CanPrint(LogLevel level)
        {
            return m_LogLevel <= level;
        }
        public void Debug(string msg)
        {
            if (!CanPrint(LogLevel.Debug))
                return;
            Console.ForegroundColor = ConsoleColor.White;
            Console.WriteLine(msg);
        }
        public void Info(string msg)
        {
            if (!CanPrint(LogLevel.Info))
                return;
            Console.ForegroundColor = ConsoleColor.Green;
            Console.WriteLine(msg);
        }
        public void Warn(string msg)
        {
            if (!CanPrint(LogLevel.Warning))
                return;
            Console.ForegroundColor = ConsoleColor.Yellow;
            Console.WriteLine(msg);
        }
        public void Error(string msg)
        {
            if (!CanPrint(LogLevel.Error))
                return;
            Console.ForegroundColor = ConsoleColor.Red;
            Console.WriteLine(msg);
        }
        public void Exception(string msg)
        {
            if (!CanPrint(LogLevel.Exception))
                return;
            Console.ForegroundColor = ConsoleColor.Blue;
            Console.WriteLine(msg);
        }

    }
}
