﻿//*********************************************************
// Framework
// Author:  Jasen 
// Date  :  2022-10-24
//*********************************************************

using System;
using System.Diagnostics;

namespace Aurora.Framework
{
    /// <summary>
    /// Log的对外接口文件
    /// 此处使用模板而不是用变参或Object，防止转换带来的GC开销
    /// </summary>
    public static class Log
    {
        private static ILog s_Logger;
        private static ILog s_ConsoleLogger;

        //初始化日志
        public static void Init(ILog logger, ILog consoleLogger = null)
        {
            s_Logger = logger;
            s_ConsoleLogger = consoleLogger;
        }

        [Conditional("LOG_ENABLE")]
        public static void Debug(string msg)
        {
            s_Logger.Debug(msg);
            if (null != s_ConsoleLogger)
                s_ConsoleLogger.Debug(msg);
        }

        [Conditional("LOG_ENABLE")]
        public static void Info(string msg)
        {
            s_Logger.Info(msg);
            if (null != s_ConsoleLogger)
                s_ConsoleLogger.Info(msg);
        }

        [Conditional("LOG_ENABLE")]
        public static void Warning(string msg)
        {
            s_Logger.Warn(msg);
            if (null != s_ConsoleLogger)
                s_ConsoleLogger.Warn(msg);
        }
#if CLIENT_FRAMEWORK
        [Conditional("ERROR_ENABLE")]
#endif
        [Conditional("LOG_ENABLE")]
        public static void Error(string msg)
        {
            s_Logger?.Error(msg);
            if (null != s_ConsoleLogger)
                s_ConsoleLogger.Error(msg);
        }

#if CLIENT_FRAMEWORK
        [Conditional("ERROR_ENABLE")]
#endif
        [Conditional("LOG_ENABLE")]
        public static void Error(Exception e)
        {
            if (e == null) return;
            s_Logger.Error(e.Message);
            if (null != s_ConsoleLogger)
                s_ConsoleLogger.Error(e.Message);
        }
#if CLIENT_FRAMEWORK
        [Conditional("ERROR_ENABLE")]
#endif
        [Conditional("LOG_ENABLE")]
        public static void Exception(string msg)
        {
            s_Logger.Exception(msg);
            if (null != s_ConsoleLogger)
                s_ConsoleLogger.Exception(msg);
        }
        /*
        [Conditional("LOG_ENABLE")]
        public static void Debug(string message, params object[] args)
        {
            s_Logger.Debug(string.Format(message, args));

        }

        [Conditional("LOG_ENABLE")]
        public static void Info(string message, params object[] args)
        {
            s_Logger.Info(string.Format(message, args));
        }

        [Conditional("LOG_ENABLE")]
        public static void Warning(string message, params object[] args)
        {
            s_Logger.Warn(string.Format(message, args));
        }

        [Conditional("LOG_ENABLE")]
        public static void Error(string message, params object[] args)
        {
            s_Logger.Error(string.Format(message, args));
        }
        [Conditional("LOG_ENABLE")]
        public static void Execption(string message, params object[] args)
        {
            s_Logger.Exception(string.Format(message, args));
        }*/
    }
}

