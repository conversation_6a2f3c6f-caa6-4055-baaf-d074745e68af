﻿//*********************************************************
// Framework
// Author:  Jasen
// Date  :  2022-11-16
//*********************************************************

using System;
using System.Reflection;

namespace Aurora.Framework
{
    //消息的处理基类
    public interface IMessageHandler
    {
        void Handle(Session session, Packet pkt);
    }

    public interface IActorMessageHandler
    {
        ATask Handle(Entity entity, Session session, Packet pkt);
    }
}
