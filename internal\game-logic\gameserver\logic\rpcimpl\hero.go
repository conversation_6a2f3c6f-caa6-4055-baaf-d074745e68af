package rpcimpl

import (
	"context"
	"liteframe/internal/common/error_code"

	"liteframe/internal/common/protos/cs"
	"liteframe/internal/game-logic/gameserver/logic/player"
	"liteframe/pkg/log"
)

type heroService struct {
	defaultService
}

func (h heroService) HeroList(ctx context.Context, sender *player.Player, in *cs.CLHeroListReq) (out *cs.LCHeroListResp) {
	log.Info("Receive CLHeroListReq")
	out = &cs.LCHeroListResp{}

	heroList, code := sender.Hero().HeroList()
	out.ErrorCode = int32(code)

	if code == error_code.ERROR_OK {
		out.HeroList = heroList
	}

	return out
}

func (h heroService) HeroLevelUp(ctx context.Context, sender *player.Player, in *cs.CLHeroLevelUpReq) (out *cs.LCHeroLevelUpRes) {
	log.Info("Receive CLHeroLevelUpReq")
	out = &cs.LCHeroLevelUpRes{}

	level, exp, code := sender.Hero().HeroLevelUp(in.HeroId)
	out.ErrorCode = int32(code)

	if code == error_code.ERROR_OK {
		out.Level = level
		out.Exp = exp
		out.HeroId = in.HeroId
	}

	return out
}

func (h heroService) HeroAwakeLevelUp(ctx context.Context, sender *player.Player, in *cs.CLHeroAwakeLevelUpReq) (out *cs.LCHeroAwakeLevelUpRes) {
	log.Info("Receive CLHeroAwakeLevelUpReq")
	out = &cs.LCHeroAwakeLevelUpRes{}

	awakeLevel, code := sender.Hero().HeroAwakeLevelUp(in.HeroId)
	out.ErrorCode = int32(code)

	if code == error_code.ERROR_OK {
		out.AwakeLevel = awakeLevel
		out.HeroId = in.HeroId
	}

	return out
}
