﻿
using BattleServer.Nats;
using BattleServer.Service;
using Game.Core;
using Aurora.Framework;
using LiteFrame.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BattleServer.Game
{
    public class BattleResult
    {
        public ulong PlayerID;
        public bool Win;
    }

    // 棋盘定义
    public class BattleChess
    {
        private int mID;

        private List<BattleResult> mResults = new List<BattleResult>();
        private BattlePlayer?[] mBattlePlayers = new BattlePlayer[2];

        private Scene mScene;

        public BattleChess(int id, Scene scene, int rows = 10, int cols = 6)
        {
            mID = id;
            mScene = scene;
        }

        // 设置战斗玩家
        public void SetPlayers(BattlePlayer player1, BattlePlayer player2)
        {
            Log.Debug($"[battle] SetPlayers id {mID} player1 {player1.Info.Uid} player2 {player2.Info.Uid}");

            mBattlePlayers[0] = player1;
            mBattlePlayers[1] = player2;
            if (mBattlePlayers[0] == null || mBattlePlayers[1] == null)
            {
                Log.Error($"[battle] SetPlayers error, player1 or player2 is null");
                return;
            }

            for (int i = 0; i < 2; i++)
            {
                mBattlePlayers[i].battle = this;
            }

            mBattlePlayers[0].InitCell(1, 30);
            mBattlePlayers[1].InitCell(31, 60);
        }

        public void OnPlayerBattleEnd(ulong uid, bool win)
        {
            Log.Debug($"[battle] OnPlayerBattleEnd id {mID} uid {uid} win {win}");

            BattlePlayer player = GetPlayer(uid);
            player.OnRoundBattleEnd(win);

            BattleResult battleResult = new BattleResult();
            battleResult.PlayerID = uid;
            battleResult.Win = win;
            mResults.Add(battleResult);

            //检测对手是否是机器人
            BattlePlayer enemyPlayer = GetEnemyPlayer(uid);
            if (enemyPlayer.IsRobot)
            {
                enemyPlayer.OnRoundBattleEnd(!win);

                battleResult = new BattleResult();
                battleResult.PlayerID = enemyPlayer.Info.Uid;
                battleResult.Win = !win;
                mResults.Add(battleResult);
            }

            if (mResults.Count < 2)
                return;

            if (mResults[0].Win == mResults[1].Win)
            {
                Log.Error($"battle OnBattleEnd result error, all player win p1 {mResults[0].PlayerID} p2 {mResults[1].PlayerID}");
            }
        }

        public bool IsBattleEnd()
        {
            if (IsEmpty())
            {
                Log.Debug($"[battle] IsBattleEnd id {mID} but is empty");
                return true;
            }
            return mResults.Count > 0;
        }

        public void OnRoundStart(bool pushOnlyNewHero)
        {
            if (IsEmpty())
            {
                Log.Debug($"[battle] OnRoundStart id {mID} but is empty");
                return;
            }

            Log.Debug($"[battle] OnRoundStart id {mID}");

            TablePlayMode playMode = TablePlayMode.GetData(1);
            int prepareTimeout = playMode.PreDuration * 1000 + 5; // 准备阶段时间 + 5秒缓冲
            int buffTimeout = playMode.BuffDuration * 1000 + 5; // Buff选择时间 + 5秒缓冲
            var timeoutTimestamp = TimeHelper.GetCurrentTimestampMs() + prepareTimeout;
            var buffTimeoutTimestamp = timeoutTimestamp + buffTimeout;

            foreach (var player in mBattlePlayers)
            {
                if (player.IsDead()) continue;
                //if (player.IsRobot) continue;

                var req = new RoundStartReq();
                req.TimeoutTimestamp = timeoutTimestamp; // 准备阶段65秒超时
                req.BuffTimeoutTimestamp = buffTimeoutTimestamp; // buff选择25秒超时

                req.Uid = player.Info.Uid;
                req.Buffers.Add(player.GetOptionsBuffer());


                // 规定下盘为第一个推送
                if (player.IsLowerBoard())
                {
                    var lowerBoard = new PBPlayerBoard();
                    lowerBoard.Uid = player.Info.Uid;
                    //if (pushOnlyNewHero)
                    //{
                    //    // 只推送新英雄
                    //    lowerBoard.BoardInfo.AddRange(player.GetNewHeroBoard());
                    //    // 推送全部英雄
                    //    lowerBoard.BoardInfo.AddRange(player.GetHeroBoard());
                    //}
                    //else
                    //{
                    //    // 推送全部英雄
                    //    lowerBoard.BoardInfo.AddRange(player.GetHeroBoard());
                    //}
                    lowerBoard.BoardInfo.AddRange(player.GetHeroBoard());
                    req.PlayerBoards.Add(lowerBoard);
                    Log.Debug($"[battle] RoundStartReq add player {player.Info.Uid} board info ");

                    // 获取对手的棋盘信息
                    var otherPlayer = GetEnemyPlayer(player.Info.Uid);
                    var otherBoard = new PBPlayerBoard();
                    otherBoard.Uid = otherPlayer.Info.Uid;
                    otherBoard.BoardInfo.AddRange(otherPlayer.GetLastBattleHeroBoard());
                    req.PlayerBoards.Add(otherBoard);
                    Log.Debug($"[battle] RoundStartReq add player {otherPlayer.Info.Uid} board info ");
                }
                else
                {
                    // 获取对手的棋盘信息
                    var otherPlayer = GetEnemyPlayer(player.Info.Uid);
                    var otherBoard = new PBPlayerBoard();
                    otherBoard.Uid = otherPlayer.Info.Uid;
                    otherBoard.BoardInfo.AddRange(otherPlayer.GetLastBattleHeroBoard());
                    req.PlayerBoards.Add(otherBoard);
                    Log.Debug($"[battle] RoundStartReq add player {otherPlayer.Info.Uid} board info ");

                    var lowerBoard = new PBPlayerBoard();
                    lowerBoard.Uid = player.Info.Uid;
                    //if (pushOnlyNewHero)
                    //{
                    //    // 只推送新英雄
                    //    lowerBoard.BoardInfo.AddRange(player.GetNewHeroBoard());
                    //    // 推送全部英雄
                    //    lowerBoard.BoardInfo.AddRange(player.GetHeroBoard());
                    //}
                    //else
                    //{
                    //    // 推送全部英雄
                    //    lowerBoard.BoardInfo.AddRange(player.GetHeroBoard());
                    //}
                    lowerBoard.BoardInfo.AddRange(player.GetHeroBoard());
                    req.PlayerBoards.Add(lowerBoard);
                    Log.Debug($"[battle] RoundStartReq add player {player.Info.Uid} board info ");
                }

                //req.PlayerBoards.AddRange(player.battle.GetBoardInfo());
                Log.Debug($"[battle] RoundStartReq sent to player {player.Info.Uid} on server {player.Info.ServerId} pushOnlyNewHero {pushOnlyNewHero}");
                if (player.IsRobot) continue;
                NatsClient.GameServiceClient.RoundStart(req, player.Info.ServerId).ConfigureAwait(false);
            }
        }

        // 每场战斗开始时调用
        public void OnBattleStart(int seed)
        {
            if (IsEmpty())
            {
                Log.Debug($"[battle] OnBattleStart id {mID} but is empty");
                return;
            }

            Log.Debug($"[battle] OnBattleStart id {mID}");

            // 检测是否全部是机器人
            if (mBattlePlayers[0].IsRobot && mBattlePlayers[1].IsRobot)
            {
                OnPlayerBattleEnd(mBattlePlayers[0].Info.Uid, true);
                return;
            }

            var timeoutTimestamp = TimeHelper.GetCurrentTimestampMs() + (mScene.PlayMode.BattleDuration + 5) * 1000;
            // 通知玩家战斗开始
            foreach (var player in mBattlePlayers)
            {
                if (player.IsDead() || player.IsRobot)
                {
                    continue;
                }

                Log.Debug($"[battle] OnBattleStart send RoundBattleStartReq to player {player.Info.Uid}");
                var req = new RoundBattleStartReq();
                // 规定下盘为第一个推送
                if (player.IsLowerBoard())
                {
                    req.Uid = player.Info.Uid;
                    req.BattleId = GetScene().SceneID;
                    req.Seed = seed;
                    req.TimeoutTimestamp = timeoutTimestamp;
                    req.Team.Add(player.ToPBBattleCampInfo());

                    var enemy = player.battle.GetEnemyPlayer(player.Info.Uid);
                    req.Team.Add(enemy.ToPBBattleCampInfo());
                }
                else
                {
                    var enemy = player.battle.GetEnemyPlayer(player.Info.Uid);
                    req.Team.Add(enemy.ToPBBattleCampInfo());

                    req.Uid = player.Info.Uid;
                    req.BattleId = GetScene().SceneID;
                    req.Seed = seed;
                    req.TimeoutTimestamp = timeoutTimestamp;
                    req.Team.Add(player.ToPBBattleCampInfo());
                }
                    
                
                NatsClient.GameServiceClient.RoundBattleStart(req, player.Info.ServerId).ConfigureAwait(false);
            }
        }

        // 每场战斗结束后调用
        public void OnBattleEnd()
        {
            Log.Debug($"[battle] OnBattleEnd id {mID}");
            if (mResults.Count < 2)
            {
                Log.Debug($"battle OnBattleEnd result error, count {mResults.Count}");
                return;
            }

            bool isGameEnding = GetScene().GetPlayers().Count <= 1;
            var timeoutTimestamp = TimeHelper.GetCurrentTimestampMs() + 5000;
            // 通知玩家战斗结束
            foreach (var player in mBattlePlayers)
            {
                if (player.IsDead())
                {
                    // 死了的以提前送过结算消息
                    continue;
                }
                var req = new RoundBattleEndReq();
                req.Uid = player.Info.Uid;
                req.WinUid = GetWinner();
                req.LoseUid = GetLosser();
                req.IsEnd = isGameEnding; // 设置整场战斗是否即将结束
                req.TimeoutTimestamp = timeoutTimestamp; // 结算确认超时

                NatsClient.GameServiceClient.RoundBattleEnd(req, player.Info.ServerId).ConfigureAwait(false);

                // 清理玩家战斗状态
                player.ClearBattle();
            }
            // 清理战斗
            ClearBattle();
        }

        public void OnBattleTimeout()
        {
            Log.Debug($"[battle] OnBattleTimeout id {mID}");
            if (IsEmpty())
            {
                Log.Debug($"[battle] OnBattleTimeout but is empty");
                return;
            }
            
            List<ulong> realyPlayer = new List<ulong>();
            foreach (var player in mBattlePlayers)
            {
                //if (player.IsDead())
                //{
                //    continue;
                //}
                if (player.IsRobot)
                {
                    // 机器人不需要超时处理
                    continue;
                }
                
                if (!player.IsEnterBattle())
                {
                    Log.Error($"[battle] OnBattleTimeout player {player.Info.Uid} not enter battle");
                    continue;
                }

                realyPlayer.Add(player.Info.Uid);
            }

            if (realyPlayer.Count == 0)
            {
                Log.Debug($"[battle] OnBattleTimeout but no real player");
                return;
            }
            if (realyPlayer.Count == 1)
            {
                Log.Debug($"[battle] OnBattleTimeout only one real player {realyPlayer[0]}");
                // 只有一个真实玩家，直接结束战斗
                OnPlayerBattleEnd(realyPlayer[0], true);
                return;
            }
            else
            {
                OnPlayerBattleEnd(realyPlayer[0], true);
                OnPlayerBattleEnd(realyPlayer[1], false);
            }
        }

        private void ClearBattle()
        {
            Log.Debug($"[battle] ClearBattlePlayers id {mID}");
            for (int i = 0; i < 2; i++)
            {
                mBattlePlayers[i].battle = null;
                mBattlePlayers[i] = null;
            }
            mResults.Clear();
        }

        public void OnUpdate()
        {
            if (IsEmpty())
                return;


        }

        public BattlePlayer GetPlayer(ulong uid)
        {
            for (int i = 0; i < 2; i++)
            {
                if (mBattlePlayers[i].Info.Uid == uid)
                    return mBattlePlayers[i];
            }

            return null;
        }

        public Scene GetScene()
        {
            return mScene;
        }

        public int GetID()
        {
            return mID;
        }

        public BattlePlayer GetEnemyPlayer(ulong uid)
        {
            for (int i = 0; i < 2; i++)
            {
                if (mBattlePlayers[i].Info.Uid != uid)
                    return mBattlePlayers[i];
            }

            return null;
        }

        private bool IsDraw()
        {
            foreach (var result in mResults)
            {
                if (result.Win)
                    return false;
            }

            return true;
        }

        public ulong GetWinner()
        {
            foreach (var result in mResults)
            {
                if (result.Win)
                    return result.PlayerID;
            }

            return 0;
        }

        public ulong GetLosser()
        {
            foreach (var result in mResults)
            {
                if (!result.Win)
                    return result.PlayerID;
            }

            return 0;
        }

        public bool IsEmpty()
        {
            return mBattlePlayers[0] == null && mBattlePlayers[1] == null;
        }
    }
}
