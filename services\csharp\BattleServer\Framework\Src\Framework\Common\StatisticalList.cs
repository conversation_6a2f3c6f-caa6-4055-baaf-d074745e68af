﻿using System;

namespace Aurora.Framework
{
    public class StatisticalList
    {
        private ushort m_nSampleHoldCount;
        private ushort m_nSampleIndex;
        private long[] m_RecentSampleList;
        private long m_RecentTotal;

        public long LastSample
        {
            get
            {
                if (m_nSampleIndex > 0)
                {
                    return m_RecentSampleList[m_nSampleIndex-1];
                }
                else
                {
                    return m_RecentSampleList[m_nSampleHoldCount-1];
                }
            }
        }

        public long RecentTotal
        {
            get
            {
                return m_RecentTotal;
            }
        }

        public long RecentAverage
        {
            get
            {
                return m_RecentTotal / m_nSampleHoldCount;
            }
        }

        public float RecentAverageFloat
        {
            get
            {
                return m_RecentTotal / (float)m_nSampleHoldCount;
            }
        }

        public StatisticalList(ushort nSampleHoldCount)
        {
            if (nSampleHoldCount > 0)
            {
                m_nSampleHoldCount = nSampleHoldCount;
            }
            else
            {
                m_nSampleHoldCount = 1;
            }
            m_RecentSampleList = new long[m_nSampleHoldCount];

            Clear();
        }

        public void AddSample(long sample)
        {
            m_RecentTotal += sample - m_RecentSampleList[m_nSampleIndex];
            m_RecentSampleList[m_nSampleIndex] = sample;
            if (++m_nSampleIndex >= m_nSampleHoldCount)
            {
                m_nSampleIndex = 0;
            }
        }

        public void Clear()
        {
            m_RecentTotal = 0;
            m_nSampleIndex = 0;

            Array.Clear(m_RecentSampleList, 0, m_nSampleHoldCount);
        }
    }
}
