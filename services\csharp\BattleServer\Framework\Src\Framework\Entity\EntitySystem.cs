﻿//*********************************************************
// Framework
// Author:  Jasen
// Date  :  2022-11-4
//*********************************************************

using System;
using System.Collections.Generic;
using System.Reflection;

namespace Aurora.Framework
{
	//全局Entity管理相关，一些系统触发相关
	public class EntitySystem
	{
        public enum OperatingEnvironmentType
        {
            Server,
            Client
        }
        public static OperatingEnvironmentType OperatingEnvironment = OperatingEnvironmentType.Server;
        public delegate EntitySystem GetInstance();
        public static GetInstance InstanceGetter { get; set; }

		private long m_nLastInstanceID;
		private readonly Dictionary<long, Entity> m_Entities = new Dictionary<long, Entity>();

		//用确定类型的代理调用最快，MethodInfo调用差3倍，Deletegate.DdynamicInvoke差9倍左右
		private static  readonly Dictionary<Type, IComponentSystemCallback> m_AwakeCallback = new Dictionary<Type, IComponentSystemCallback>();
        private static  readonly Dictionary<Type, IAwakeCallbackArg1> m_Awake1Callback = new Dictionary<Type, IAwakeCallbackArg1>();
        private static readonly Dictionary<Type, IAwakeCallbackArg2> m_Awake2Callback = new Dictionary<Type, IAwakeCallbackArg2>();
        private static readonly Dictionary<Type, IComponentSystemCallback> m_FrequentlyUpdateCallback = new Dictionary<Type, IComponentSystemCallback>();
        private static readonly Dictionary<Type, IComponentSystemCallback> m_UpdateCallback = new Dictionary<Type, IComponentSystemCallback>();
		private static readonly Dictionary<Type, IComponentSystemCallback> m_LateUpdateCallback = new Dictionary<Type, IComponentSystemCallback>();
        private static readonly Dictionary<Type, IComponentSystemCallback> m_SlowlyUpdateCallback = new Dictionary<Type, IComponentSystemCallback>();
        private static readonly Dictionary<Type, IComponentSystemCallback> m_DestroyCallback = new Dictionary<Type, IComponentSystemCallback>();

		//private long m_nLastTickTime = 0;
		private int m_nFrameTime = 0;

		/// <summary>
		/// Last Frame Time In Milliseconds
		/// </summary>
		public int FrameTime
		{
			get { return m_nFrameTime; }
		}

        //private static int s_nHashSetTrimBaseLine = 1024;
        //private static int s_nHashSetTrimRatio = 4;

        private struct UpdateContext
        {
            public static UpdateContext Default = new UpdateContext { InstanceID = 0, Entity = null, Callback = null };

            public long InstanceID;
            public Entity Entity;
            public IComponentSystemCallback Callback;
        }

        private List<KeyValuePair<long, Entity>> m_FrequentlyUpdateList = new List<KeyValuePair<long, Entity>>();
        private List<KeyValuePair<long, Entity>> m_FrequentlyUpdateAddList = new List<KeyValuePair<long, Entity>>();

        private int m_nUpdateValidCount = 0;
        private List<UpdateContext> m_UpdateContextList = new List<UpdateContext>();
        private Dictionary<long, int> m_UpdateIndexMap = new Dictionary<long, int>();

        private List<KeyValuePair<long, Entity>> m_LateUpdateList = new List<KeyValuePair<long, Entity>>();
        private List<KeyValuePair<long, Entity>> m_LateUpdateAddList = new List<KeyValuePair<long, Entity>>();

        private List<KeyValuePair<long, Entity>> m_SlowlyUpdateList = new List<KeyValuePair<long, Entity>>();
        private List<KeyValuePair<long, Entity>> m_SlowlyUpdateAddList = new List<KeyValuePair<long, Entity>>();

        private static int s_nSlowlyUpdateInterval = 10;
        private int m_nSlowlyUpdateIndex = 0;

        private Queue<Entity> m_RecycleQueue = new Queue<Entity>();

		private EventBus m_EventBus;
		public EventBus EventBus { get { return m_EventBus; } }

        public EntitySystem()
		{
			//m_nLastTickTime = ATimer.Instance.ClientNow;
			m_EventBus = new EventBus(this);
		}

        public void Clear()
        {
            ProcessRecycle();

            m_EventBus.Release();

            m_FrequentlyUpdateList.Clear();
            m_FrequentlyUpdateAddList.Clear();

            m_nUpdateValidCount = 0;
            m_UpdateContextList.Clear();
            m_UpdateIndexMap.Clear();

            m_LateUpdateList.Clear();
            m_LateUpdateAddList.Clear();

            m_SlowlyUpdateList.Clear();
            m_SlowlyUpdateAddList.Clear();

            m_Entities.Clear();

            m_nFrameTime = 0;
            m_nLastInstanceID = 0;
            m_nSlowlyUpdateIndex = 0;
        }

        public static IComponentSystemCallback Bind(Type compType,MethodInfo method)
        {
            if (compType == null || method == null) return null;
            IComponentSystemCallback callbackObj = null;
			ParameterInfo[] parameters = method.GetParameters();
            if (parameters.Length != 1)
            {
                Log.Error($"Component函数绑定错误:参数长度不匹配，Type=[{compType.Name}],Method=[{method.Name}]");
                return null;
            }
            if (parameters[0].ParameterType != compType)
            {
                Log.Error($"Component函数绑定错误:消息类型不匹配，Type=[{compType.Name}],Method=[{method.Name}]");
                return null;
            }
            Type[] argTypes = new Type[parameters.Length];
			for(int i = 0; i< parameters.Length;i++)
			{
				argTypes[i] = parameters[i].ParameterType;
            }
            try
            {
                Type actionType = typeof(Action<>).MakeGenericType(argTypes);
                Type callbackType = typeof(SystemCallback<>).MakeGenericType(argTypes);
				callbackObj = Activator.CreateInstance(callbackType, new object[] { Delegate.CreateDelegate(actionType, method) }) as IComponentSystemCallback;
            }
            catch(Exception e)
			{
                Log.Exception($"Component函数绑定异常:Type=[{compType.Name}],Method=[{method.Name}],Msg={e.Message}");
            }
            return callbackObj;
        }

        public static IAwakeCallbackArg1 BindAwake1(Type compType, MethodInfo method)
        {
            if (compType == null || method == null) return null;
            IAwakeCallbackArg1 callbackObj = null;
            ParameterInfo[] parameters = method.GetParameters();
            if (parameters.Length != 2)
            {
                Log.Error($"Awake1函数绑定错误:参数长度不匹配，Type=[{compType.Name}],Method=[{method.Name}]");
                return null;
            }
            if (parameters[0].ParameterType != compType)
            {
                Log.Error($"Awake1函数绑定错误:消息类型不匹配，Type=[{compType.Name}],Method=[{method.Name}]");
                return null;
            }
            Type[] argTypes = new Type[parameters.Length];
            for (int i = 0; i < parameters.Length; i++)
            {
                argTypes[i] = parameters[i].ParameterType;
            }
            try
			{
                Type actionType = typeof(Action<,>).MakeGenericType(argTypes);
                Type callbackType = typeof(AwakeCallbackArg1<,>).MakeGenericType(argTypes);
				callbackObj = Activator.CreateInstance(callbackType, new object[] { Delegate.CreateDelegate(actionType, method) }) as IAwakeCallbackArg1;
            }
            catch(Exception e)
			{
                Log.Error($"Awake1函数绑定异常:Type=[{compType.Name}],Method=[{method.Name}],Msg={e.Message}");
            }
            return callbackObj;
        }

        public static IAwakeCallbackArg2 BindAwake2(Type compType, MethodInfo method)
        {
            if (compType == null || method == null) return null;
			IAwakeCallbackArg2 callbackObj = null;
            ParameterInfo[] parameters = method.GetParameters();
            if (parameters.Length != 3)
            {
                Log.Error($"Awake2函数绑定错误:参数长度不匹配，Type=[{compType.Name}],Method=[{method.Name}]");
                return null;
            }
            if (parameters[0].ParameterType != compType)
            {
                Log.Error($"Awake2函数绑定错误:消息类型不匹配，Type=[{compType.Name}],Method=[{method.Name}]");
                return null;
            }
            Type[] argTypes = new Type[parameters.Length];
            for (int i = 0; i < parameters.Length; i++)
            {
                argTypes[i] = parameters[i].ParameterType;
            }
            try
			{
                Type actionType = typeof(Action<,,>).MakeGenericType(argTypes);
                Type callbackType = typeof(AwakeCallbackArg2<,,>).MakeGenericType(argTypes);
				callbackObj = Activator.CreateInstance(callbackType, new object[] { Delegate.CreateDelegate(actionType, method) }) as IAwakeCallbackArg2;
				return callbackObj;
            }
            catch (Exception e)
            {
                Log.Error($"Awake2函数绑定异常:Type=[{compType.Name}],Method=[{method.Name}],Msg={e.Message}");
            }
			return callbackObj;
        }


        public static void AddAssembly(Assembly assembly)
        {
            if (assembly == null) return;
            Log.Info($"EntitySystem.InitAssembly Add Assembly. name :{assembly.GetName()}");

			Type[] allTypes = assembly.GetTypes();
            foreach (Type type in allTypes)
            {
                var sysAttr = type.GetCustomAttribute<ComponentSystemAttribute>();
                if (sysAttr == null)
                    continue;
                var methods = type.GetMethods();
                foreach (var method in methods)
                {
                    var methodAttr = method.GetCustomAttribute<ComponentSystemMethodAttribute>();
                    if (methodAttr == null)
                    {
                        continue;
                    }
                    if (methodAttr is MethodAwake1Attribute)
                    {
                        IAwakeCallbackArg1 callback = BindAwake1(sysAttr.CompType, method);
                        if (callback != null)
                        {
							if(!m_Awake1Callback.TryAdd(sysAttr.CompType, callback))
							{
								m_Awake1Callback[sysAttr.CompType]= callback;
                            }
                        }
                        continue;
                    }
                    if (methodAttr is MethodAwake2Attribute)
                    {
                        IAwakeCallbackArg2 callback = BindAwake2(sysAttr.CompType, method);
                        if (callback != null)
                        {
                            if (!m_Awake2Callback.TryAdd(sysAttr.CompType, callback))
                            {
                                m_Awake2Callback[sysAttr.CompType] = callback;
                            }
                        }
                        continue;
                    }

                    if (methodAttr is MethodAwakeAttribute)
                    {
                        IComponentSystemCallback callback = Bind(sysAttr.CompType, method);
                        if (callback != null)
                        {
                            if (!m_AwakeCallback.TryAdd(sysAttr.CompType, callback))
                            {
                                m_AwakeCallback[sysAttr.CompType] = callback;
                            }
                        }
                        continue;
                    }
                    if (methodAttr is MethodFrequentlyUpdateAttribute)
                    {
                        IComponentSystemCallback callback = Bind(sysAttr.CompType, method);
                        if (callback != null)
                        {
                            if (!m_FrequentlyUpdateCallback.TryAdd(sysAttr.CompType, callback))
                            {
                                m_FrequentlyUpdateCallback[sysAttr.CompType] = callback;
                            }
                        }
                        continue;
                    }
                    if (methodAttr is MethodUpdateAttribute)
                    {
                        IComponentSystemCallback callback = Bind(sysAttr.CompType, method);
                        if (callback != null)
                        {
                            if (!m_UpdateCallback.TryAdd(sysAttr.CompType, callback))
                            {
                                m_UpdateCallback[sysAttr.CompType] = callback;
                            }
                        }
                        continue;
                    }
                    if (methodAttr is MethodLateUpdateAttribute)
                    {
                        IComponentSystemCallback callback = Bind(sysAttr.CompType, method);
                        if (callback != null)
                        {
                            if (!m_LateUpdateCallback.TryAdd(sysAttr.CompType, callback))
                            {
                                m_LateUpdateCallback[sysAttr.CompType] = callback;
                            }
                        }
                        continue;
                    }
                    if (methodAttr is MethodSlowlyUpdateAttribute)
                    {
                        IComponentSystemCallback callback = Bind(sysAttr.CompType, method);
                        if (callback != null)
                        {
                            if (!m_SlowlyUpdateCallback.TryAdd(sysAttr.CompType, callback))
                            {
                                m_SlowlyUpdateCallback[sysAttr.CompType] = callback;
                            }
                        }
                        continue;
                    }
                    if (methodAttr is MethodDestroyAttribute)
                    {
                        IComponentSystemCallback callback = Bind(sysAttr.CompType, method);
                        if (callback != null)
                        {
                            if (!m_DestroyCallback.TryAdd(sysAttr.CompType, callback))
                            {
                                m_DestroyCallback[sysAttr.CompType] = callback;
                            }
                        }
                        continue;
                    }
                }
            }
            Log.Info("EntitySystem.InitAssembly End");
        }
        public static void InitAssemblyClient()
        {
            m_AwakeCallback.Clear();
            m_Awake1Callback.Clear();
            m_Awake2Callback.Clear();
            m_FrequentlyUpdateCallback.Clear();
            m_UpdateCallback.Clear();
            m_LateUpdateCallback.Clear();
            m_SlowlyUpdateCallback.Clear();
            m_DestroyCallback.Clear();
            
            Dictionary<Type, List<string>> list = AssemblyManager.Instance.GetAllTypeMethods<MethodAwake1Attribute>();
            foreach (KeyValuePair<Type, List<string>> value in list)
            {
                var sysAttr = value.Key.GetCustomAttribute<ComponentSystemAttribute>();
                if (sysAttr == null)
                    continue;
                for (int i = 0; i < value.Value.Count; i++)
                {
                    MethodInfo method = value.Key.GetMethod(value.Value[i]);
                    IAwakeCallbackArg1 callback = BindAwake1(sysAttr.CompType, method);
                    if (callback != null)
                    {
                        m_Awake1Callback.Add(sysAttr.CompType, callback);
                    }
                }
            }
            list = AssemblyManager.Instance.GetAllTypeMethods<MethodAwake2Attribute>();
            foreach (KeyValuePair<Type, List<string>> value in list)
            {
                var sysAttr = value.Key.GetCustomAttribute<ComponentSystemAttribute>();
                if (sysAttr == null)
                    continue;
                for (int i = 0; i < value.Value.Count; i++)
                {
                    MethodInfo method = value.Key.GetMethod(value.Value[i]);
                    IAwakeCallbackArg2 callback = BindAwake2(sysAttr.CompType, method);
                    if (callback != null)
                    {
                        m_Awake2Callback.Add(sysAttr.CompType, callback);
                    }
                }
            }
            list = AssemblyManager.Instance.GetAllTypeMethods<MethodAwakeAttribute>();
            foreach (KeyValuePair<Type, List<string>> value in list)
            {
                var sysAttr = value.Key.GetCustomAttribute<ComponentSystemAttribute>();
                if (sysAttr == null)
                    continue;
                for (int i = 0; i < value.Value.Count; i++)
                {
                    MethodInfo method = value.Key.GetMethod(value.Value[i]);
                    IComponentSystemCallback callback = Bind(sysAttr.CompType, method);
                    if (callback != null)
                    {
                        m_AwakeCallback.Add(sysAttr.CompType, callback);
                    }
                }
            }
            list = AssemblyManager.Instance.GetAllTypeMethods<MethodFrequentlyUpdateAttribute>();
            foreach (KeyValuePair<Type, List<string>> value in list)
            {
                var sysAttr = value.Key.GetCustomAttribute<ComponentSystemAttribute>();
                if (sysAttr == null)
                    continue;
                for (int i = 0; i < value.Value.Count; i++)
                {
                    MethodInfo method = value.Key.GetMethod(value.Value[i]);
                    IComponentSystemCallback callback = Bind(sysAttr.CompType, method);
                    if (callback != null)
                    {
                        m_FrequentlyUpdateCallback.Add(sysAttr.CompType, callback);
                    }
                }
            }
            list = AssemblyManager.Instance.GetAllTypeMethods<MethodUpdateAttribute>();
            foreach (KeyValuePair<Type, List<string>> value in list)
            {
                var sysAttr = value.Key.GetCustomAttribute<ComponentSystemAttribute>();
                if (sysAttr == null)
                    continue;
                for (int i = 0; i < value.Value.Count; i++)
                {
                    MethodInfo method = value.Key.GetMethod(value.Value[i]);
                    IComponentSystemCallback callback = Bind(sysAttr.CompType, method);
                    if (callback != null)
                    {
                        m_UpdateCallback.Add(sysAttr.CompType, callback);
                    }
                }
            }
            list = AssemblyManager.Instance.GetAllTypeMethods<MethodLateUpdateAttribute>();
            foreach (KeyValuePair<Type, List<string>> value in list)
            {
                var sysAttr = value.Key.GetCustomAttribute<ComponentSystemAttribute>();
                if (sysAttr == null)
                    continue;
                for (int i = 0; i < value.Value.Count; i++)
                {
                    MethodInfo method = value.Key.GetMethod(value.Value[i]);
                    IComponentSystemCallback callback = Bind(sysAttr.CompType, method);
                    if (callback != null)
                    {
                        m_LateUpdateCallback.Add(sysAttr.CompType, callback);
                    }
                }
            }
            list = AssemblyManager.Instance.GetAllTypeMethods<MethodSlowlyUpdateAttribute>();
            foreach (KeyValuePair<Type, List<string>> value in list)
            {
                var sysAttr = value.Key.GetCustomAttribute<ComponentSystemAttribute>();
                if (sysAttr == null)
                    continue;
                for (int i = 0; i < value.Value.Count; i++)
                {
                    MethodInfo method = value.Key.GetMethod(value.Value[i]);
                    IComponentSystemCallback callback = Bind(sysAttr.CompType, method);
                    if (callback != null)
                    {
                        m_SlowlyUpdateCallback.Add(sysAttr.CompType, callback);
                    }
                }
            }
            list = AssemblyManager.Instance.GetAllTypeMethods<MethodDestroyAttribute>();
            foreach (KeyValuePair<Type, List<string>> value in list)
            {
                var sysAttr = value.Key.GetCustomAttribute<ComponentSystemAttribute>();
                if (sysAttr == null)
                    continue;
                for (int i = 0; i < value.Value.Count; i++)
                {
                    MethodInfo method = value.Key.GetMethod(value.Value[i]);
                    IComponentSystemCallback callback = Bind(sysAttr.CompType, method);
                    if (callback != null)
                    {
                        m_DestroyCallback.Add(sysAttr.CompType, callback);
                    }
                }
            }
        }

        public static void InitAssembly()
		{
			Log.Info("EntitySystem.InitAssembly Begin");
			List<Type> allTypes = AssemblyManager.Instance.GetAllTypes();
			if (allTypes.Count <= 0)
			{
				throw new FrameworkException("Assembly Init is Empty!");
			}
			foreach (Type type in allTypes)
			{
				var sysAttr = type.GetCustomAttribute<ComponentSystemAttribute>();
				if (sysAttr == null)
					continue;
                var methods = type.GetMethods();
				foreach (var method in methods)
				{
					var methodAttr = method.GetCustomAttribute<ComponentSystemMethodAttribute>();
					if (methodAttr == null)
					{
						continue;
					}
                    if (methodAttr is MethodAwake1Attribute)
                    {
                        IAwakeCallbackArg1 callback = BindAwake1(sysAttr.CompType, method);
						if(callback != null)
						{
                            m_Awake1Callback.Add(sysAttr.CompType, callback);
                        }
                        else
                        {
                            Log.Error($"EntitySystem.InitAssembly BindAwake1 failed, type={sysAttr.CompType.Name}, method={method.Name}");
                        }
                        continue;
                    }
                    if (methodAttr is MethodAwake2Attribute)
                    {
                        IAwakeCallbackArg2 callback = BindAwake2(sysAttr.CompType, method);
                        if (callback != null)
                        {
                            m_Awake2Callback.Add(sysAttr.CompType, callback);
                        }
                        else
                        {
                            Log.Error($"EntitySystem.InitAssembly BindAwake2 failed, type={sysAttr.CompType.Name}, method={method.Name}");
                        }
                        continue;
                    }

                    if (methodAttr is MethodAwakeAttribute)
                    {
                        IComponentSystemCallback callback = Bind(sysAttr.CompType, method);
                        if (callback != null)
                        {
							m_AwakeCallback.Add(sysAttr.CompType, callback);
                        }
                        else
                        {
                            Log.Error($"EntitySystem.InitAssembly BindAwake failed, type={sysAttr.CompType.Name}, method={method.Name}");
                        }
						continue;
					}
                    if (methodAttr is MethodFrequentlyUpdateAttribute)
                    {
                        IComponentSystemCallback callback = Bind(sysAttr.CompType, method);
                        if (callback != null)
                        {
                            m_FrequentlyUpdateCallback.Add(sysAttr.CompType, callback);
                        }
                        else
                        {
                            Log.Error($"EntitySystem.InitAssembly BindFrequentlyUpdate failed, type={sysAttr.CompType.Name}, method={method.Name}");
                        }
                        continue;
                    }
                    if (methodAttr is MethodUpdateAttribute)
					{
                        IComponentSystemCallback callback = Bind(sysAttr.CompType, method);
                        if (callback != null)
                        {
                            m_UpdateCallback.Add(sysAttr.CompType, callback);
                        }
                        else
                        {
                            Log.Error($"EntitySystem.InitAssembly BindUpdate failed, type={sysAttr.CompType.Name}, method={method.Name}");
                        }
                        continue;
                    }
					if (methodAttr is MethodLateUpdateAttribute)
					{
                        IComponentSystemCallback callback = Bind(sysAttr.CompType, method);
                        if (callback != null)
                        {
                            m_LateUpdateCallback.Add(sysAttr.CompType, callback);
                        }
                        else
                        {
                            Log.Error($"EntitySystem.InitAssembly BindLateUpdate failed, type={sysAttr.CompType.Name}, method={method.Name}");
                        }
                        continue;
                    }
                    if (methodAttr is MethodSlowlyUpdateAttribute)
                    {
                        IComponentSystemCallback callback = Bind(sysAttr.CompType, method);
                        if (callback != null)
                        {
                            m_SlowlyUpdateCallback.Add(sysAttr.CompType, callback);
                        }
                        else
                        {
                            Log.Error($"EntitySystem.InitAssembly BindSlowlyUpdate failed, type={sysAttr.CompType.Name}, method={method.Name}");
                        }
                        continue;
                    }
                    if (methodAttr is MethodDestroyAttribute)
					{
                        IComponentSystemCallback callback = Bind(sysAttr.CompType, method);
                        if (callback != null)
                        {
                            m_DestroyCallback.Add(sysAttr.CompType, callback);
                        }
                        else
                        {
                            Log.Error($"EntitySystem.InitAssembly BindDestroy failed, type={sysAttr.CompType.Name}, method={method.Name}");
                        }
                        continue;
                    }
				}
			}
			Log.Info("EntitySystem.InitAssembly End");
		}

		private long GenerateInstanceID()
		{
			return ++m_nLastInstanceID;
		}

        public void Register(Entity entity)
        {
            if (entity == null)
                return;

            long nInstanceID = GenerateInstanceID();
            if (m_Entities.ContainsKey(nInstanceID))
            {
                return;
            }

            entity.SetOwnSystem(this);
            entity.InstanceID = nInstanceID;

            m_Entities.Add(entity.InstanceID, entity);

            if (entity is IFrequentlyUpdate)
            {
                if (m_FrequentlyUpdateCallback.ContainsKey(entity.GetType()))
                {
                    entity.FrequentlyUpdateCallback = m_FrequentlyUpdateCallback[entity.GetType()];
                    entity.FrequentlyUpdateMethodName = "";
#if SERVER_FRAMEWORK
#if OPEN_PERFORMANCE
                    entity.FrequentlyUpdateMethodName = $"{entity.GetType().Name}.OnFrequentlyUpdate";
#endif
#else
                    if (GameAnalysisFramework.isRunning)
                    {
                        entity.FrequentlyUpdateMethodName = $"{entity.GetType().Name}.OnFrequentlyUpdate";
                    }
#endif
                    m_FrequentlyUpdateAddList.Add(KeyValuePair.Create(entity.InstanceID, entity));
                }
                else
                {
                    Log.Error($"[Entity] Can Not Register {entity.GetType().Name} In FrequentlyUpdate!");
                }
            }
            if (entity is IUpdate)
            {
                if (m_UpdateCallback.TryGetValue(entity.GetType(), out IComponentSystemCallback callback))
                {
                    int nIndex = m_nUpdateValidCount++;

                    UpdateContext context;
                    {
                        context.InstanceID = entity.InstanceID;
                        context.Entity = entity;
                        context.Callback = callback;
                    }
                    if (nIndex < m_UpdateContextList.Count)
                    {
                        m_UpdateContextList[nIndex] = context;
                    }
                    else
                    {
                        m_UpdateContextList.Add(context);
                    }

                    m_UpdateIndexMap[entity.InstanceID] = nIndex;

#if SERVER_FRAMEWORK
#if OPEN_PERFORMANCE
                    entity.UpdateMethodName = $"{entity.GetType().Name}.OnUpdate";
#endif
#else
                    if (GameAnalysisFramework.isRunning)
                    {
                        entity.UpdateMethodName = $"{entity.GetType().Name}.OnUpdate";
                    }
#endif
                }
                else
                {
                    Log.Error($"[Entity] Can Not Register {entity.GetType().Name} In Update!");
                }
            }
            if (entity is ILateUpdate)
            {
                if (m_LateUpdateCallback.ContainsKey(entity.GetType()))
                {
                    entity.LateUpdateCallback = m_LateUpdateCallback[entity.GetType()];
                    entity.LateUpdateMethodName = "";
#if SERVER_FRAMEWORK
#if OPEN_PERFORMANCE
                    entity.LateUpdateMethodName = $"{entity.GetType().Name}.OnLateUpdate";
#endif
#endif
                    m_LateUpdateAddList.Add(KeyValuePair.Create(entity.InstanceID, entity));
                }
                else
                {
                    Log.Error($"[Entity] Can Not Register {entity.GetType().Name} In LateUpdate!");
                }
            }
            if (entity is ISlowlyUpdate)
            {
                if (m_SlowlyUpdateCallback.ContainsKey(entity.GetType()))
                {
                    entity.SlowlyUpdateCallback = m_SlowlyUpdateCallback[entity.GetType()];
                    entity.SlowlyUpdateMethodName = "";
#if SERVER_FRAMEWORK
#if OPEN_PERFORMANCE
                    entity.SlowlyUpdateMethodName = $"{entity.GetType().Name}.OnSlowlyUpdate";
#endif
#endif
                    m_SlowlyUpdateAddList.Add(KeyValuePair.Create(entity.InstanceID, entity));
                }
                else
                {
                    Log.Error($"[Entity] Can Not Register {entity.GetType().Name} In SlowlyUpdate!");
                }
            }

            entity.OnEnterSystem();
        }

        public void Unregister(Entity entity)
        {
            if (entity == null)
                return;

            if (!m_Entities.ContainsKey(entity.InstanceID))
            {
                return;
            }

            entity.OnLeaveSystem();

            m_Entities.Remove(entity.InstanceID);

            if (entity is IFrequentlyUpdate)
            {
                entity.FrequentlyUpdateMethodName = "";
                entity.FrequentlyUpdateCallback = null;
            }
            if (entity is IUpdate)
            {
                if (m_UpdateIndexMap.Remove(entity.InstanceID, out int nIndex))
                {
                    m_UpdateContextList[nIndex] = UpdateContext.Default;
                }
                else
                {
                    Log.Error($"EntitySystem.Unregister Error! {entity.GetType()} Update Index Is Not Found.");
                }
            }
            if (entity is ILateUpdate)
            {
                entity.LateUpdateMethodName = "";
                entity.LateUpdateCallback = null;
            }
            if (entity is ISlowlyUpdate)
            {
                entity.SlowlyUpdateMethodName = "";
                entity.SlowlyUpdateCallback = null;
            }

            entity.InstanceID = 0;
            entity.SetOwnSystem(null);
        }
        
        private bool CheckValid(Entity entity, long nCheckInstanceID)
        {
            if ((entity.GetOwnSystem() == this) && (entity.InstanceID == nCheckInstanceID))
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        public static void Awake(Entity entity)
        {
            if (entity != null)
            {
                if (!entity.Valid)
                {
                    Type compType = entity.GetType();
                    if (m_AwakeCallback.TryGetValue(compType, out IComponentSystemCallback value))
                    {
                        try
                        {
                            var t = ATimer.Instance.Now.Ticks;
                            value.Invoke(entity);
                            var ms = (ATimer.Instance.Now.Ticks - t) / 10000;
                            if(ms>1000)
                                Log.Warning($"{compType} OnAwake:{ms}ms");
                            else if(ms>100)
                                Log.Warning($"{compType} OnAwake:{ms}ms");
                        }
                        catch (System.Exception e)
                        {
                            Log.Error($"[Entity] Exception Is Caught In {entity.GetType().Name}.OnAwake! Message={e.Message}.\nStack Begin:\n{e.StackTrace}\nStack End.");
                        }
                    }                    
                    
                    entity.Valid = true;
                }
                else
                {
                    Log.Error($"[Entity] Can Not Awake {entity.GetType().Name} Twice!");
                }
            }
        }

        public static void Awake(Entity entity, object arg)
        {
            if (entity != null)
            {
                if (!entity.Valid)
                {
                    Type compType = entity.GetType();
                    if (m_Awake1Callback.TryGetValue(compType, out IAwakeCallbackArg1 value))
                    {
                        try
                        {
                            value.Invoke(entity, arg);
                        }
                        catch (System.Exception e)
                        {
                            Log.Error($"[Entity] Exception Is Caught In {entity.GetType().Name}.OnAwake1! Message={e.Message}.\nStack Begin:\n{e.StackTrace}\nStack End.");
                        }
                    }

                    entity.Valid = true;
                }
                else
                {
                    Log.Error($"[Entity] Can Not Awake {entity.GetType().Name} Twice!");
                }
            }
        }

        public static void Awake(Entity entity, object arg1, object arg2)
        {
            if (entity != null)
            {
                if (!entity.Valid)
                {
                    Type compType = entity.GetType();
                    if (m_Awake2Callback.TryGetValue(compType, out IAwakeCallbackArg2 value))
                    {
                        try
                        {
                            value.Invoke(entity, arg1, arg2);
                        }
                        catch (System.Exception e)
                        {
                            Log.Error($"[Entity] Exception Is Caught In {entity.GetType().Name}.OnAwake2! Message={e.Message}.\nStack Begin:\n{e.StackTrace}\nStack End.");
                        }
                    }

                    entity.Valid = true;
                }
                else
                {
                    Log.Error($"[Entity] Can Not Awake {entity.GetType().Name} Twice!");
                }
            }
        }

        public static void Destroy(Entity entity)
        {
            if (entity != null)
            {
                Type compType = entity.GetType();
                if (m_DestroyCallback.TryGetValue(compType, out IComponentSystemCallback value))
                {
                    if (entity.Valid)
                    {
                        entity.Valid = false;

                        try
                        {
                            value.Invoke(entity);
                        }
                        catch (System.Exception e)
                        {
                            Log.Error($"[Entity] Exception Is Caught In {entity.GetType().Name}.OnDestroy! Message={e.Message}.\nStack Begin:\n{e.StackTrace}\nStack End.");
                        }
                    }
                    else
                    {
                        Log.Error($"[Entity] Can Not Destroy {entity.GetType().Name} Twice!");
                    }
                }
                else
                {
                    entity.Valid = false;
                }
            }
        }

        public void FrequentlyUpdate()
        {
            if (m_FrequentlyUpdateAddList.Count > 0)
            {
                m_FrequentlyUpdateList.AddRange(m_FrequentlyUpdateAddList);
                m_FrequentlyUpdateAddList.Clear();
            }

            int nValidCount = m_FrequentlyUpdateList.Count;
            for (int nIndex = 0; nIndex < nValidCount;)
            {
                long nCheckInstanceID = m_FrequentlyUpdateList[nIndex].Key;
                Entity entity = m_FrequentlyUpdateList[nIndex].Value;
                if (CheckValid(entity, nCheckInstanceID))
                {
                    string strMethodName = "";
#if SERVER_FRAMEWORK
#if OPEN_PERFORMANCE
                    strMethodName = entity.FrequentlyUpdateMethodName;
#endif
#else
                    if (GameAnalysisFramework.isRunning)
                    {
                        strMethodName = entity.FrequentlyUpdateMethodName;
                    }
#endif
                    try
                    {
#if SERVER_FRAMEWORK
#if OPEN_PERFORMANCE
                        PerformanceMonitorManager.EnterMethod(strMethodName);
#endif
#else
                        if (GameAnalysisFramework.isRunning)
                        {
                            GameAnalysisFramework.StartWatchTime(strMethodName);
                        }
#endif
                        entity.FrequentlyUpdateCallback.Invoke(entity);
                    }
                    catch (System.Exception e)
                    {
                        if (CheckValid(entity, nCheckInstanceID))
                        {
                            Log.Error($"[Entity] Exception Is Caught In {entity.GetType().Name}.OnFrequentlyUpdate! Message={e.Message}.\nStack Begin:\n{e.StackTrace}\nStack End.");
                            entity.OnFrequentlyUpdateExceptionError(e);
                        }
                        else
                        {
                            Log.Error($"[Entity] Exception Is Caught In UnknownEntity.OnFrequentlyUpdate! Message={e.Message}.\nStack Begin:\n{e.StackTrace}\nStack End.");
                        }
                    }
                    finally
                    {
                        //if (!CheckValid(entity, nCheckInstanceID))
                        //{
                        //    //Log.Warning($"[Entity] Self-Removal Is Detected In {strMethodName}!");
                        //}
#if SERVER_FRAMEWORK
#if OPEN_PERFORMANCE
                        PerformanceMonitorManager.LeaveMethod(strMethodName);
#endif
#else
                        if (GameAnalysisFramework.isRunning)
                        {
                            GameAnalysisFramework.StopWatchTime(strMethodName, GameAnalysisFramework.MethodAnalysis.Tick);
                        }
#endif
                    }
                    ++nIndex;
                }
                else
                {
                    int nLastIndex = --nValidCount;
                    if (nIndex < nLastIndex)
                    {
                        m_FrequentlyUpdateList[nIndex] = m_FrequentlyUpdateList[nLastIndex];
                    }
                }
            }

            if (nValidCount < m_FrequentlyUpdateList.Count)
            {
                m_FrequentlyUpdateList.RemoveRange(nValidCount, (m_FrequentlyUpdateList.Count - nValidCount));
            }
        }

        public void Update(int deltaTimeMs)
        {
            m_nFrameTime = deltaTimeMs;

            for (int nIndex = 0; nIndex < m_nUpdateValidCount;)
            {
                UpdateContext context = m_UpdateContextList[nIndex];
                if (context.Entity != null)
                {
#if SERVER_FRAMEWORK
#if OPEN_PERFORMANCE
                    string strMethodName = context.Entity.UpdateMethodName;
#endif
#else
                    string strMethodName = "";
                    if (GameAnalysisFramework.isRunning)
                    {
                        strMethodName = context.Entity.UpdateMethodName;
                    }
#endif
                    try
                    {
#if SERVER_FRAMEWORK
#if OPEN_PERFORMANCE
                        PerformanceMonitorManager.EnterMethod(strMethodName);
#endif
#else
                        if (GameAnalysisFramework.isRunning)
                        {
                            GameAnalysisFramework.StartWatchTime(strMethodName);
                        }
#endif

                        context.Callback.Invoke(context.Entity);
                    }
                    catch (System.Exception e)
                    {
                        UpdateContext tempContext = m_UpdateContextList[nIndex];
                        if (tempContext.Entity != null)
                        {
                            Log.Error($"[Entity] Exception Is Caught In {tempContext.Entity.GetType().Name}.OnUpdate! Message={e.Message}.\nStack Begin:\n{e.StackTrace}\nStack End.");
                            tempContext.Entity.OnUpdateExceptionError(e);
                        }
                        else
                        {
                            Log.Error($"[Entity] Exception Is Caught In UnknownEntity.OnUpdate! Message={e.Message}.\nStack Begin:\n{e.StackTrace}\nStack End.");
                        }
                    }
                    finally
                    {
#if SERVER_FRAMEWORK
#if OPEN_PERFORMANCE
                        PerformanceMonitorManager.LeaveMethod(strMethodName);
#endif
#else
                        if (GameAnalysisFramework.isRunning)
                        {
                            GameAnalysisFramework.StopWatchTime(strMethodName, GameAnalysisFramework.MethodAnalysis.Tick);
                        }
#endif
                    }
                    ++nIndex;
                }
                else
                {
                    int nLastIndex = --m_nUpdateValidCount;
                    if (nIndex < nLastIndex)
                    {
                        m_UpdateContextList[nIndex] = m_UpdateContextList[nLastIndex];
                        long nInstanceID = m_UpdateContextList[nIndex].InstanceID;
                        if (nInstanceID != 0)
                        {
                            m_UpdateIndexMap[nInstanceID] = nIndex;
                        }
                    }
                }
            }

            //if (m_nUpdateValidCount < m_UpdateContextList.Count)
            //{
            //    m_UpdateContextList.RemoveRange(m_nUpdateValidCount, (m_UpdateContextList.Count - m_nUpdateValidCount));
            //}

            m_EventBus.Run(deltaTimeMs);
        }

        public void LateUpdate()
        {
            if (m_LateUpdateAddList.Count > 0)
            {
                m_LateUpdateList.AddRange(m_LateUpdateAddList);
                m_LateUpdateAddList.Clear();
            }

            int nValidCount = m_LateUpdateList.Count;
            for (int nIndex = 0; nIndex < nValidCount;)
            {
                long nCheckInstanceID = m_LateUpdateList[nIndex].Key;
                Entity entity = m_LateUpdateList[nIndex].Value;
                if (CheckValid(entity, nCheckInstanceID))
                {
                    string strMethodName = "";
#if SERVER_FRAMEWORK
#if OPEN_PERFORMANCE
                    strMethodName = entity.LateUpdateMethodName;
#endif
#endif
                    try
                    {
#if SERVER_FRAMEWORK
#if OPEN_PERFORMANCE
                        PerformanceMonitorManager.EnterMethod(strMethodName);
#endif
#endif
                        entity.LateUpdateCallback.Invoke(entity);
                    }
                    catch (System.Exception e)
                    {
                        if (CheckValid(entity, nCheckInstanceID))
                        {
                            Log.Error($"[Entity] Exception Is Caught In {entity.GetType().Name}.OnLateUpdate! Message={e.Message}.\nStack Begin:\n{e.StackTrace}\nStack End.");
                            entity.OnLateUpdateExceptionError(e);
                        }
                        else
                        {
                            Log.Error($"[Entity] Exception Is Caught In UnknownEntity.OnLateUpdate! Message={e.Message}.\nStack Begin:\n{e.StackTrace}\nStack End.");
                        }
                    }
                    finally
                    {
                        //if (!CheckValid(entity, nCheckInstanceID))
                        //{
                        //    //Log.Warning($"[Entity] Self-Removal Is Detected In {strMethodName}!");
                        //}
#if SERVER_FRAMEWORK
#if OPEN_PERFORMANCE
                        PerformanceMonitorManager.LeaveMethod(strMethodName);
#endif
#endif
                    }
                    ++nIndex;
                }
                else
                {
                    int nLastIndex = --nValidCount;
                    if (nIndex < nLastIndex)
                    {
                        m_LateUpdateList[nIndex] = m_LateUpdateList[nLastIndex];
                    }
                }
            }

            if (nValidCount < m_LateUpdateList.Count)
            {
                m_LateUpdateList.RemoveRange(nValidCount, (m_LateUpdateList.Count - nValidCount));
            }

            ProcessRecycle();
        }

        public void SlowlyUpdate()
        {
            if (m_SlowlyUpdateAddList.Count > 0)
            {
                m_SlowlyUpdateList.AddRange(m_SlowlyUpdateAddList);
                m_SlowlyUpdateAddList.Clear();
            }

            int nIndex = m_nSlowlyUpdateIndex;
            int nValidCount = m_SlowlyUpdateList.Count;
            int nMaxUpdateCount = (int)Math.Ceiling((float)nValidCount / s_nSlowlyUpdateInterval);

            for (int nUpdateCount = 0; (nIndex < nValidCount) && (nUpdateCount < nMaxUpdateCount);)
            {
                long nCheckInstanceID = m_SlowlyUpdateList[nIndex].Key;
                Entity entity = m_SlowlyUpdateList[nIndex].Value;
                if (CheckValid(entity, nCheckInstanceID))
                {
                    string strMethodName = "";
#if SERVER_FRAMEWORK
#if OPEN_PERFORMANCE
                    strMethodName = entity.SlowlyUpdateMethodName;
#endif
#endif
                    try
                    {
#if SERVER_FRAMEWORK
#if OPEN_PERFORMANCE
                        PerformanceMonitorManager.EnterMethod(strMethodName);
#endif
#endif
                        entity.SlowlyUpdateCallback.Invoke(entity);
                    }
                    catch (System.Exception e)
                    {
                        if (CheckValid(entity, nCheckInstanceID))
                        {
                            Log.Error($"[Entity] Exception Is Caught In {entity.GetType().Name}.OnSlowlyUpdate! Message={e.Message}.\nStack Begin:\n{e.StackTrace}\nStack End.");
                            entity.OnSlowlyUpdateExceptionError(e);
                        }
                        else
                        {
                            Log.Error($"[Entity] Exception Is Caught In UnknownEntity.OnSlowlyUpdate! Message={e.Message}.\nStack Begin:\n{e.StackTrace}\nStack End.");
                        }
                    }
                    finally
                    {
                        //if (!CheckValid(entity, nCheckInstanceID))
                        //{
                        //    //Log.Warning($"[Entity] Self-Removal Is Detected In {strMethodName}!");
                        //}
#if SERVER_FRAMEWORK
#if OPEN_PERFORMANCE
                        PerformanceMonitorManager.LeaveMethod(strMethodName);
#endif
#endif
                    }

                    ++nIndex;
                    ++nUpdateCount;
                }
                else
                {
                    int nLastIndex = --nValidCount;
                    if (nIndex < nLastIndex)
                    {
                        m_SlowlyUpdateList[nIndex] = m_SlowlyUpdateList[nLastIndex];
                    }
                }
            }

            if (nIndex < nValidCount)
            {
                m_nSlowlyUpdateIndex = nIndex;
            }
            else
            {
                m_nSlowlyUpdateIndex = 0;
            }

            if (nValidCount < m_SlowlyUpdateList.Count)
            {
                m_SlowlyUpdateList.RemoveRange(nValidCount, (m_SlowlyUpdateList.Count - nValidCount));
            }
        }

        public T GetComponent<T>(long instanceID) where T : BaseComponent
		{
			Entity comp = null;
			if (!m_Entities.TryGetValue(instanceID, out comp))
			{
				return null;
			}
			if (!comp.IsComponent)
				return null;

			return (T)comp;
		}

		public T GetEntity<T>(long instanceID) where T : Entity
		{
			Entity entity = null;
			if (!m_Entities.TryGetValue(instanceID, out entity))
			{
				return null;
			}
			if (entity.IsComponent)
				return null;

			return (T)entity;
		}

		public Entity GetEntity(long instanceID)
		{
			Entity entity = null;
			if (!m_Entities.TryGetValue(instanceID, out entity))
			{
				return null;
			}
			if (entity.IsComponent)
				return null;

			return entity;
		}

		public void AddRootEntity(Entity entity)
		{
			if (entity != null)
			{
				if (entity.GetOwnSystem() == null && entity.Root == null)
				{
					entity.SetOwnSystem(this);
					entity.Root = entity;
				}
				else
				{
					throw new FrameworkException("EntitySystem AddRootEntity Fail! Entity Has Been Registered Already.");
				}
			}
			else
			{
				throw new FrameworkException("EntitySystem AddRootEntity Fail! Entity Is Null.");
			}
		}

		public void RemoveRootEntity(Entity entity)
		{
            if (entity == null) return;
			if (entity.GetOwnSystem() == this)
			{
				if (entity.Root == entity)
				{
					entity.Root = null;
				}
				else
				{
					throw new FrameworkException("EntitySystem RemoveRootEntity Fail! Entity Is Not Root.");
				}
			}
			else
			{
				throw new FrameworkException("EntitySystem RemoveRootEntity Fail! Entity Does Not Belong To This EntitySystem.");
			}
		}

        public void RecycleEntity(Entity entity)
        {
            if (entity == null) return;
            entity.WaitingRecycle = true;
            m_RecycleQueue.Enqueue(entity);
        }

        public void ProcessRecycle()
        {
            while (m_RecycleQueue.TryDequeue(out Entity entity))
            {
                if (entity.WaitingRecycle)
                {
                    entity.RecycleImmediately();
                }
            }
        }
	}
}
