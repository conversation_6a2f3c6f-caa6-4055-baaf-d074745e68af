﻿//*********************************************************
// Framework
// Author:  Jasen
// Date  :  2022-11-3
//*********************************************************

using System;

/// <summary>
/// 通过AsyncMethodBuilderAttribute标识的builder类型关联自定义Task类型
/// </summary>
namespace Aurora.Framework
{
    [AttributeUsage(AttributeTargets.Class)]
    public sealed class AsyncMethodBuilderAttribute : Attribute
    {
        public Type BuilderType
        {
            get;
        }

        public AsyncMethodBuilderAttribute(Type builderType)
        {
            BuilderType = builderType;
        }
    }
}
