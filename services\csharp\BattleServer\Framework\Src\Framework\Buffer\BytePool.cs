﻿//*********************************************************
// Framework
// Author:  Jasen
// Date  :  2023-4-12
//*********************************************************

using System.Threading;
using System.Collections.Generic;
using System;

namespace Aurora.Framework
{
    //Byte数组线程池
    public class BytePool
    {
		private static ThreadLocal<BytePool> s_Instance = new ThreadLocal<BytePool>(() => { return new BytePool(); }, false);

        public static BytePool Instance
        {
            get
            {
                return s_Instance.Value;
            }
        }

        private readonly Dictionary<long, ByteBucket> m_Buckets = new Dictionary<long, ByteBucket>();

        private long m_MaxSize;

        private BytePool()
        {
            m_MaxSize = 1024 * 1024 * 64;//最大64M
            //默认最小1024，最大4M
            SetBlockSize(256, 1024 * 1024 * 4);
            m_UsedSize = 0;
        }

        public int MinBlockSize { get; private set; }
        public int MaxBlockSize { get; private set; }


        private long m_UsedSize;
        public long UsedSize
        {
            get
            {
                return m_UsedSize;
            }
        }

        public void SetBlockSize(int minBlockSize, int maxBlockSize)
        {
            MinBlockSize = minBlockSize;
            MaxBlockSize = maxBlockSize;
        }
        public void SetMaxMemory(long maxSize)
        {
            m_MaxSize = maxSize;
        }

        public bool AddSize(int byteSize)
        {
            if (m_Buckets.TryAdd(byteSize, new ByteBucket(byteSize)))
            {
                return true;
            }
            return false;
        }

        public bool ContainsSize(int byteSize)
        {
            return m_Buckets.ContainsKey(byteSize);
        }

        public void Recycle(byte[] bytes)
        {
            //buff回收
            if (bytes == null || bytes.Length > MaxBlockSize || bytes.Length < MinBlockSize)
            {
                return;
            }
            int byteSize = bytes.Length;
            if (UsedSize > m_MaxSize)
            {
                return;
            }
            //未超过最大值，回收
            ByteBucket bucket = null;
			if (!m_Buckets.TryGetValue(byteSize, out bucket))
            {
                bucket = new ByteBucket(byteSize);
                m_Buckets.TryAdd(byteSize, bucket);
            }
            bucket.Return(bytes);
            m_UsedSize += bytes.Length;
        }

        public byte[] GetBytes(int byteSize)
        {
            if(m_Buckets.TryGetValue(byteSize, out ByteBucket bucket))
            {
                if(bucket.TryGet(out byte[] bytes))
                {
                    m_UsedSize -= byteSize;
                    return bytes;
                }
            }
            return new byte[byteSize];
        }

        public void Clear()
        {
            m_Buckets.Clear();
            GC.Collect();
        }
    }
}
