﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Aurora.Framework
{
    public class MessageRangeDefine
    {
        // 10001 - 30000 是pb，中间分成两个部分，外网pb跟内网pb
        public const ushort PbMsgMin = 10000;

        public const ushort PbOuterMin = 10000;
        public const ushort PbOuterMax = 20000;

        // 20001-30000 内网pb
        public const ushort PbInnerMin = 20001;
        public const ushort PbInnerMax = 30000;

        public const ushort PbMsgMax = 30000;

        // 30001 - 40000 Kratos
        public const ushort KratosInnerMin = 30001;
        public const ushort KratosInnerMax = 35000;
        public const ushort KratosOuterMin = 35001;
        public const ushort KratosOuterMax = 40000;

        public const ushort InnerMaxOpcode = 40000;

        public const ushort GMTPbInnerMin = 40001;
        public const ushort GMTPbInnerMax = 49999;


        public const ushort JsonMinOpcode = 50000;
        public const ushort JsonMaxOpcode = 60000;

        


        public const ushort MaxOpcode = 60000;
    }
}