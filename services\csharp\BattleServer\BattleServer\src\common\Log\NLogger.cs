﻿/*
 * @description: NLog logger implementation
 * @Auther: Kimsee
 * @Date: 2022-11-03 12:07
 *  
*/
using System;
using System.Diagnostics;
using Aurora.Framework;
using NLog;
namespace BattleServer.Server
{

    public class NLogger : ILog
    {
        private readonly NLog.Logger logger;

        public NLogger(string name, int process, string configPath)
        {
            LogManager.Configuration = new NLog.Config.XmlLoggingConfiguration(configPath);
            LogManager.Configuration.Variables["appIdFormat"] = $"{process:000000}";
            LogManager.Configuration.Variables["currentDir"] = Environment.CurrentDirectory;
            this.logger = LogManager.GetLogger(name);
        }

        public void Debug(string message)
        {
            this.logger.Debug(message);
        }
        public void Info(string message)
        {
            this.logger.Info(message);
        }
        public void Warn(string message)
        {
            this.logger.Warn(message);
        }

        public void Error(string message)
        {
            StackTrace st = new StackTrace(2, true);
            this.logger.Error($"{message}\n{st}");
        }

        public void Exception(string message)
        {
            StackTrace st = new StackTrace(2, true);
            this.logger.Fatal($"{message}\n{st}");
        }
        /*
        public void Debug(string message, params object[] args)
        {
            this.logger.Debug(message, args);
        }
        public void Info(string message, params object[] args)
        {
            this.logger.Info(message, args);
        }
        public void Warn(string message, params object[] args)
        {
            this.logger.Warn(message, args);
        }

        public void Error(string message, params object[] args)
        {
            this.logger.Error(message, args);
        }

        public void Exception(string message, params object[] args)
        {
            this.logger.Fatal(message, args);
        }*/
    }
}