﻿//*********************************************************
// Framework
// Author:  Jasen
// Date  :  2022-11-3
//*********************************************************


using System;
using System.Runtime.CompilerServices;

namespace Aurora.Framework
{

    public enum AwaiterState
    {
        Waiting = 0,
        Completed,
        Exception,
    }

    public class ATaskAwaiter : ICriticalNotifyCompletion, INotifyCompletion, IReference
    {
        private AwaiterState m_State;
        private Action m_Callback;
        private Exception m_Exception;
        public ATaskAwaiter()
        {
            m_State = AwaiterState.Waiting;
            m_Callback = null;
            m_Exception = null;
        }
        //无返回类型没有结果
        public void SetResult()
        {
            if(m_State != AwaiterState.Waiting)
            {
                throw new FrameworkException("ATask already completed!");
            }
            m_State = AwaiterState.Completed;
            m_Callback?.Invoke();
        }
        //无返回类型没有结果,最后才会调用这里
        public void GetResult()
        {
            switch(m_State)
            {
                case AwaiterState.Waiting:
                    throw new FrameworkException("ATask get result but state is waiting!");
                case AwaiterState.Exception:
                    Exception ex = m_Exception;
                    ReferencePool.Release(this);
                    if (ex != null)
                    {
                        //重新抛出异常
                        throw ex;
                    }
                    break;
                case AwaiterState.Completed:
                    {
                        ReferencePool.Release(this);
                    }
                    break;
            }
        }
        //设置异常，然后触发延续体
        public void SetException(Exception e)
        {
            m_State = AwaiterState.Exception;
            m_Exception = e;
            m_Callback?.Invoke();
        }
        public bool IsCompleted { get { return m_State != AwaiterState.Waiting; } }

        public void OnCompleted(Action continuation)
        {
            if (this.m_State != AwaiterState.Waiting)
            {
                continuation?.Invoke();
                return;
            }
            this.m_Callback = continuation;
        }
        public void UnsafeOnCompleted(Action continuation)
        {
            if (this.m_State != AwaiterState.Waiting)
            {
                continuation?.Invoke();
                return;
            }
            this.m_Callback = continuation;
        }

        public void Clear()
        {
            m_State = AwaiterState.Waiting;
            m_Callback = null;
            m_Exception = null;
        }
    }

    public class ATaskAwaiter<TResult> : ICriticalNotifyCompletion, INotifyCompletion, IReference
    {
        private Action m_Callback;
        private AwaiterState m_State;
        private TResult m_Result;
        private Exception m_Exception;

        public ATaskAwaiter()
        {
            m_Callback = null;
            m_State = AwaiterState.Waiting;
            m_Result = default;
            m_Exception = null;
        }
        //设置结果，然后触发延续体
        public void SetResult(TResult result)
        {
            if (m_State != AwaiterState.Waiting)
            {
                throw new FrameworkException("ATask already completed!");
            }
            m_State = AwaiterState.Completed;
            m_Result = result;
            m_Callback?.Invoke();
        }
        //取得结果，证明任务完成，取完后可以销毁回收
        public TResult GetResult()
        {
            TResult result = m_Result;
            switch (m_State)
            {
                case AwaiterState.Exception:
                    Exception ex = m_Exception;
                    ReferencePool.Release(this);
                    if (ex != null)
                    {
                        //重新抛出异常
                        throw ex;
                    }
                    return default;
                case AwaiterState.Waiting:
                    throw new FrameworkException("ATask get result but state is waiting!");
            }
            ReferencePool.Release(this);
            return result;
        }
        //设置异常，然后触发延续体
        public void SetException(Exception e)
        {
            m_State = AwaiterState.Exception;
            m_Exception = e;
            m_Callback?.Invoke();
        }

        public bool IsCompleted { get { return m_State != AwaiterState.Waiting; } }
        //设置延续体
        public void OnCompleted(Action continuation)
        {
            this.UnsafeOnCompleted(continuation);
        }
        public void UnsafeOnCompleted(Action continuation)
        {
            if (this.m_State != AwaiterState.Waiting)
            {
                //已经完成，直接触发延续体
                continuation?.Invoke();
                return;
            }
            //保存延续体，完成后触发
            this.m_Callback = continuation;
        }

        public void Clear()
        {
            m_Callback = null;
            m_State = AwaiterState.Waiting;
            m_Result = default;
            m_Exception = null;
        }
    }
}
