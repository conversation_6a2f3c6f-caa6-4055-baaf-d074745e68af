﻿//*********************************************************
// Framework
// Author:  Jasen
// Date  :  2022-11-3
//*********************************************************


using System;

namespace Aurora.Framework
{

    [System.Runtime.CompilerServices.AsyncMethodBuilder(typeof(ATaskAsyncVoidMethodBuilder))]
    //TODO:后续可以优化ATask，用引用池回收
    public partial class ATask
    {
        private ATaskAwaiter m_Awaiter;

        public ATask()
        {
            m_Awaiter = ReferencePool.Acquire<ATaskAwaiter>();
        }
        public ATaskAwaiter GetAwaiter()
        {
            return m_Awaiter;
        }
        //设置异常
        public void SetException(Exception e)
        {
            m_Awaiter.SetException(e);
        }
        //设置异步结果，同时触发Awaiter后续
        public void SetResult()
        {
            m_Awaiter.SetResult();
        }
        private async ATaskNothing InnerCoroutine()
        {
            await this;
        }
        public void Coroutine()
        {
            InnerCoroutine().Coroutine();
        }
    }
}
