﻿//*********************************************************
// Framework
// Author:  Jasen
// Date  :  2022-11-16
//*********************************************************

using System;

namespace Aurora.Framework
{
    public sealed class ResponseTypeAttribute : AuroraAttribute
    {
        public Type MsgType { get; }

        public ResponseTypeAttribute(Type msgType)
        {
            MsgType = msgType;
        }
    }

    public sealed class MessageIDAttribute : AuroraAttribute
    {
        public ushort MessageID { get; }
        public MessageIDAttribute(ushort messageID)
        {
            MessageID = messageID;
        }
    }

    public sealed class MessageTypeAttribute : AuroraAttribute
    {
        public MessageClass ClassType { get; }
        public MessageTypeAttribute(MessageClass classType)
        {
            ClassType = classType;
        }
    }

    public sealed class TargetAttribute : AuroraAttribute
    {
        public MessageTarget Target { get; }
        public TargetAttribute(MessageTarget target)
        {
            Target = target;
        }
    }
}
