﻿using Aurora.Framework;
using System.Collections.Concurrent;
using System.Text;
using Microsoft.Diagnostics.Runtime;
using System.Diagnostics;
using Microsoft.Diagnostics.Runtime.Interfaces;

namespace BattleServer.Server
{
    public class ThreadObject
    {
        public enum EState
        {
            NORMAL = 0,                           // 正常执行工作单元
            TEMPORARILY_UNRESPONSIVE = 1,         // 临时无响应（可恢复）
            PERMANENTLY_UNRESPONSIVE = 2          // 永久无响应（需终止）
        }

        private EState m_eState = EState.NORMAL;

        private ushort m_nRunSerialNumber = 0;

        private object m_StateLock = new object();

        private bool m_bShutDown = false;

        private Thread m_Thread = null;

        private List<WorkUnitBase> m_WorkUnitList = new List<WorkUnitBase>();

        private ConcurrentQueue<WorkUnitBase> m_AddQueue = new ConcurrentQueue<WorkUnitBase>();

        private ConcurrentQueue<WorkUnitBase> m_RemoveQueue = new ConcurrentQueue<WorkUnitBase>();

        private static ThreadLocal<WorkUnitBase> s_CurrentWorkUnit = new ThreadLocal<WorkUnitBase>(() => { return null; }, false);
        private WorkUnitBase m_InnerCurrentWorkUnit = null;

        private long m_nRecentTotalTicks = 0;

        private WorkUnitBase m_MinLoadWorkUnit = null;
        private WorkUnitBase m_MaxLoadWorkUnit = null;

        protected static int s_nLoopIntervalInMS = 10;
        protected Stopwatch m_StopWatch = new Stopwatch();

        private bool m_bReferenceCountListReady = true;
        private Dictionary<Type, int> m_CurrentReferenceCountList = new Dictionary<Type, int>();
        private Dictionary<Type, int> m_CacheReferenceCountList = new Dictionary<Type, int>();

        public static WorkUnitBase CurrentWorkUnit
        {
            get
            {
                return s_CurrentWorkUnit.Value;
            }
            private set
            {
                s_CurrentWorkUnit.Value = value;
            }
        }

        private WorkUnitBase InnerCurrentWorkUnit
        {
            get
            {
                return m_InnerCurrentWorkUnit;
            }
            set
            {
                m_InnerCurrentWorkUnit = value;
            }
        }

        private void SetCurrentWorkUnit(WorkUnitBase workUnit)
        {
            CurrentWorkUnit = workUnit;
            InnerCurrentWorkUnit = workUnit;
        }

        public int ID
        {
            get
            {
                if (m_Thread != null)
                {
                    return m_Thread.ManagedThreadId;
                }
                else
                {
                    return 0;
                }
            }
        }

        public string Name
        {
            get
            {
                if (m_Thread != null)
                {
                    return m_Thread.Name;
                }
                else
                {
                    return "NONE";
                }
            }
        }

        public long RecentTotalTicks
        {
            get
            {
                return m_nRecentTotalTicks;
            }
        }

        public WorkUnitBase MinLoadWorkUnit
        {
            get
            {
                return m_MinLoadWorkUnit;
            }
        }

        public WorkUnitBase MaxLoadWorkUnit
        {
            get
            {
                return m_MaxLoadWorkUnit;
            }
        }

        public ushort RunSerialNumber
        {
            get
            {
                return m_nRunSerialNumber;
            }
        }

        public EState State
        {
            get
            {
                return m_eState;
            }
        }

        /// <summary>
        /// Keep Valid Until The Next Access.
        /// </summary>
        public Dictionary<Type, int> ReferenceCountList
        {
            get
            {
                if (m_bReferenceCountListReady)
                {
                    Dictionary<Type, int> resultList = m_CurrentReferenceCountList;
                    m_bReferenceCountListReady = false;
                    return resultList;
                }
                else
                {
                    return null;
                }
            }
        }

        public Dictionary<Type, int> GetReferenceCountListForcibly()
        {
            return m_CurrentReferenceCountList;
        }

        public void Start(string strUniqueName)
        {
            m_Thread = new Thread(Loop);
            if (null != strUniqueName)
            {
                m_Thread.Name = strUniqueName;
            }
            m_Thread.Start();
        }

        public void Stop()
        {
            //m_Thread.Abort();

            m_bShutDown = true;
        }

        public void AddWorkUnit(WorkUnitBase workUnit)
        {
            if (workUnit == null) { return; }
            if (workUnit.State == WorkUnitBase.EState.LEAVE || workUnit.State == WorkUnitBase.EState.SLEEP)
            {
                m_AddQueue.Enqueue(workUnit);
            }
            else
            {
                Log.Error("WorkUnit State Error!");
            }
        }

        public void RemoveWorkUnit(WorkUnitBase workUnit, bool bShutDown = false)
        {
            if (workUnit == null) { return; }
            if (workUnit.State == WorkUnitBase.EState.RUN)
            {
                if (bShutDown)
                {
                    workUnit.State = WorkUnitBase.EState.SHUT_DOWN;
                }
                else
                {
                    workUnit.State = WorkUnitBase.EState.LEAVE;
                }
                m_RemoveQueue.Enqueue(workUnit);
            }
            else
            {
                Log.Error("WorkUnit State Error!");
            }
        }

        public void Loop()
        {
            BeforeRun();

            while (true)
            {
                try
                {
                    m_StopWatch.Restart();
                    bool bContinue = Run();
                    m_StopWatch.Stop();

                    if (!bContinue)
                    {
                        break;
                    }

                    int nSleepTime = s_nLoopIntervalInMS - (int)m_StopWatch.ElapsedMilliseconds;
                    nSleepTime = Math.Clamp(nSleepTime, 1, s_nLoopIntervalInMS);
                    Thread.Sleep(nSleepTime);
                }
                catch (Exception e)
                {
                    Log.Error($"Loop catch exception!\n StackTrace:\n{e.StackTrace}");
                }
            }
        }

        protected void BeforeRun()
        {
            m_eState = EState.NORMAL;
            m_nRunSerialNumber = 1;
            m_nRecentTotalTicks = 0;
            m_MinLoadWorkUnit = null;
            m_MaxLoadWorkUnit = null;
            SetCurrentWorkUnit(null);
        }

        protected bool Run()
        {
            bool bShutDown = m_bShutDown;
            {
                while (m_AddQueue.TryDequeue(out WorkUnitBase workUnit))
                {
                    if (workUnit.State == WorkUnitBase.EState.LEAVE)
                    {
                        m_AddQueue.Enqueue(workUnit);
                        break;
                    }
                    else if (workUnit.State == WorkUnitBase.EState.SLEEP)
                    {
                        workUnit.OwnThreadObject = this;
                        workUnit.State = WorkUnitBase.EState.RUN;
                        m_WorkUnitList.Add(workUnit);
                    }
                    else
                    {
                        Log.Error("WorkUnit State Error!");
                    }
                }
            }
            {
                long nRecentTotalTicks = 0;
                WorkUnitBase minLoadWorkUnit = null;
                WorkUnitBase maxLoadWorkUnit = null;
                foreach (WorkUnitBase workUnit in m_WorkUnitList)
                {
                    SetCurrentWorkUnit(workUnit);

                    workUnit.Run();

                    lock (m_StateLock)
                    {
                        SetCurrentWorkUnit(null);

                        ++m_nRunSerialNumber;

                        if (m_eState == EState.TEMPORARILY_UNRESPONSIVE)
                        {
                            m_eState = EState.NORMAL;

                            Log.Warning($"Thread {ID} Recovers From The Temporarily Unresponsive State, WorkUnit {workUnit.ID} Has Just Finished The Last Running!");

                            nRecentTotalTicks = workUnit.RecentTotalTicks;
                            minLoadWorkUnit = workUnit;
                            maxLoadWorkUnit = workUnit;
                            break;
                        }
                        else if (m_eState == EState.PERMANENTLY_UNRESPONSIVE)
                        {
                            nRecentTotalTicks = 0;
                            minLoadWorkUnit = null;
                            maxLoadWorkUnit = null;
                            break;
                        }
                    }

                    nRecentTotalTicks += workUnit.RecentTotalTicks;

                    if (minLoadWorkUnit != null)
                    {
                        if (workUnit.RecentTotalTicks < minLoadWorkUnit.RecentTotalTicks)
                        {
                            minLoadWorkUnit = workUnit;
                        }
                    }
                    else
                    {
                        minLoadWorkUnit = workUnit;
                    }

                    if (maxLoadWorkUnit != null)
                    {
                        if (workUnit.RecentTotalTicks >= maxLoadWorkUnit.RecentTotalTicks)
                        {
                            maxLoadWorkUnit = workUnit;
                        }
                    }
                    else
                    {
                        maxLoadWorkUnit = workUnit;
                    }
                }
                m_nRecentTotalTicks = nRecentTotalTicks;
                m_MinLoadWorkUnit = minLoadWorkUnit;
                m_MaxLoadWorkUnit = maxLoadWorkUnit;

                ++m_nRunSerialNumber;
            }
            {
                while (m_RemoveQueue.TryDequeue(out WorkUnitBase workUnit))
                {
                    if (workUnit.State == WorkUnitBase.EState.LEAVE)
                    {
                        if (m_WorkUnitList.Remove(workUnit))
                        {
                            workUnit.OwnThreadObject = null;
                            workUnit.State = WorkUnitBase.EState.SLEEP;
                        }
                        else
                        {
                            Log.Error("WorkUnit Is Not Found!");
                        }
                    }
                    else if (workUnit.State == WorkUnitBase.EState.SHUT_DOWN)
                    {
                        if (m_WorkUnitList.Remove(workUnit))
                        {
                            workUnit.OwnThreadObject = null;

                            workUnit.Stop();
                        }
                        else
                        {
                            Log.Error("WorkUnit Is Not Found!");
                        }
                    }
                    else
                    {
                        Log.Error("WorkUnit State Error!");
                    }
                }
            }
            {
                if (!m_bReferenceCountListReady)
                {
                    ReferencePool.GetReferenceCountList(m_CacheReferenceCountList);
                    (m_CurrentReferenceCountList, m_CacheReferenceCountList) = (m_CacheReferenceCountList, m_CurrentReferenceCountList);
                    m_bReferenceCountListReady = true;
                }
            }
            if (bShutDown)
            {
                if (m_AddQueue.IsEmpty && m_RemoveQueue.IsEmpty)
                {
                    foreach (WorkUnitBase workUnit in m_WorkUnitList)
                    {
                        workUnit.OwnThreadObject = null;
                        workUnit.Stop();
                    }
                    m_WorkUnitList.Clear();

                    return false;
                }
            }

            return true;
        }

        public void TryToRescueOtherWorkUnits(ushort nLastSerialNumber, List<WorkUnitBase> homelessWorkUnitList)
        {
            if (homelessWorkUnitList != null)
            {
                lock (m_StateLock)
                {
                    if (m_nRunSerialNumber == nLastSerialNumber)
                    {
                        WorkUnitBase runningWorkUnit = InnerCurrentWorkUnit;
                        if (runningWorkUnit != null)
                        {
                            m_eState = EState.TEMPORARILY_UNRESPONSIVE;

                            bool bCurrentInRemove = false;
                            while (m_RemoveQueue.TryDequeue(out WorkUnitBase workUnit))
                            {
                                if (workUnit == runningWorkUnit)
                                {
                                    bCurrentInRemove = true;
                                }
                                else
                                {
                                    if (workUnit.State == WorkUnitBase.EState.LEAVE)
                                    {
                                        if (m_WorkUnitList.Remove(workUnit))
                                        {
                                            workUnit.OwnThreadObject = null;
                                            workUnit.State = WorkUnitBase.EState.SLEEP;
                                        }
                                        else
                                        {
                                            Log.Error("WorkUnit Is Not Found!");
                                        }
                                    }
                                    else if (workUnit.State == WorkUnitBase.EState.SHUT_DOWN)
                                    {
                                        if (m_WorkUnitList.Remove(workUnit))
                                        {
                                            workUnit.OwnThreadObject = null;

                                            workUnit.Stop();
                                        }
                                        else
                                        {
                                            Log.Error("WorkUnit Is Not Found!");
                                        }
                                    }
                                    else
                                    {
                                        Log.Error("WorkUnit State Error!");
                                    }
                                }
                            }
                            if (bCurrentInRemove)
                            {
                                m_RemoveQueue.Enqueue(runningWorkUnit);
                            }

                            while (m_AddQueue.TryDequeue(out WorkUnitBase workUnit))
                            {
                                homelessWorkUnitList.Add(workUnit);
                            }

                            foreach (WorkUnitBase workUnit in m_WorkUnitList)
                            {
                                if (workUnit != runningWorkUnit)
                                {
                                    workUnit.OwnThreadObject = null;
                                    workUnit.State = WorkUnitBase.EState.SLEEP;
                                    homelessWorkUnitList.Add(workUnit);
                                }
                            }
                            m_WorkUnitList.Clear();
                            m_WorkUnitList.Add(runningWorkUnit);

                            Log.Error($"Thread {ID} Is Temporarily Unresponsive, WorkUnit {runningWorkUnit.ID} Is Running!");
                        }
                        else
                        {
                            Log.Error($"Error! Thread {ID} Is Temporarily Unresponsive, But There Is No WorkUnit Running!");
                        }
                    }
                }
            }
        }

        public bool TryToAbortThread()
        {
            lock (m_StateLock)
            {
                if (m_eState == EState.TEMPORARILY_UNRESPONSIVE)
                {
                    m_eState = EState.PERMANENTLY_UNRESPONSIVE;
                    Log.Exception($"Fatal Error! Thread {ID} Is Permanently Unresponsive!");

                    //LogStackFrames();

                    WorkUnitBase runningWorkUnit = InnerCurrentWorkUnit;
                    if (runningWorkUnit != null)
                    {
                        runningWorkUnit.OwnThreadObject = null;
                        runningWorkUnit.Stop();
                    }

                    m_AddQueue.Clear();
                    m_WorkUnitList.Clear();
                    m_RemoveQueue.Clear();

                    return true;
                }
            }

            return false;
        }

        public void LogStackFrames()
        {
            StringBuilder stringBuilder = new StringBuilder();

            try
            {
                using (var dataTarget = DataTarget.AttachToProcess(Process.GetCurrentProcess().Id, false))
                {
                    var runtime = dataTarget.ClrVersions.First().CreateRuntime();
                    var thread = runtime.Threads.FirstOrDefault(t => t.ManagedThreadId == ID);
                    if (thread != null)
                    {
                        stringBuilder.AppendLine($"Thread {ID} Current Stack Frames:");

                        List<ClrStackFrame> stackFrames = thread.EnumerateStackTrace().ToList();
                        foreach (var frame in stackFrames)
                        {
                            stringBuilder.AppendLine(frame.ToString());
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                stringBuilder.AppendLine($"Build Thread {ID} Stack Frames Error: {ex.Message}.");
            }
            finally
            {
                Log.Error(stringBuilder.ToString());
            }
        }
    }
}
