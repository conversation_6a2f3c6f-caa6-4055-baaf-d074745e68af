//using System;
//using System.Collections.Generic;
//using System.Linq;
//using BattleServer.Game.Core;
//using Aurora.Framework;

//namespace BattleServer.Game
//{
//    /// <summary>
//    /// 战斗Buff数据
//    /// </summary>
//    public class BattleBuff : IReference
//    {
//        public int BuffId { get; set; }
//        public string Name { get; set; }
//        public string Description { get; set; }
//        public long OwnerId { get; set; }

//        public void Init(int buffId, string name, string description, long ownerId)
//        {
//            BuffId = buffId;
//            Name = name;
//            Description = description;
//            OwnerId = ownerId;
//        }

//        public void Clear()
//        {
//            BuffId = 0;
//            Name = null;
//            Description = null;
//            OwnerId = 0;
//        }
//    }

//    /// <summary>
//    /// Buff选项 - 用于玩家从多个Buff中选择
//    /// </summary>
//    public record BuffOption(int BuffId, string Name, string Description);

//    /// <summary>
//    /// 全局Buff管理器
//    /// </summary>
//    public class BuffManager : BattleComponentBase
//    {
//        private readonly Dictionary<long, List<BattleBuff>> _playerBuffs = new();
//        private readonly Dictionary<long, List<BuffOption>> _playerBuffOptions = new();
//        private readonly Random _random = new();

//        // Buff库 - 从配置表读取
//        private List<BuffOption> GetBuffLibrary()
//        {
//            var buffIds = BattleConfig.Buff.GetBuffList();
//            var buffLibrary = new List<BuffOption>();

//            foreach (var buffId in buffIds)
//            {
//                // 这里应该从Buff配置表读取具体信息，暂时使用简化实现
//                buffLibrary.Add(new BuffOption(buffId, $"Buff_{buffId}", $"Buff description {buffId}"));
//            }

//            return buffLibrary;
//        }
        
//        protected override void OnInitialize()
//        {
//            _playerBuffs.Clear();
//            _playerBuffOptions.Clear();

//            Log.Info($"[{LogName}] Initialized");
//        }
        
//        /// <summary>
//        /// 添加Buff
//        /// </summary>
//        /// <param name="playerId">玩家ID</param>
//        /// <param name="buffId">Buff配置ID</param>
//        /// <returns>是否添加成功</returns>
//        public bool AddBuff(long playerId, int buffId)
//        {
//            ThrowIfNotInitialized();

//            // 确保玩家Buff列表存在
//            _playerBuffs.TryAdd(playerId, new List<BattleBuff>());

//            // 检查Buff是否已存在
//            if (_playerBuffs[playerId].Any(buff => buff.BuffId == buffId))
//            {
//                Log.Warning($"[{LogName}] Buff {buffId} already exists for player {playerId}");
//                return false;
//            }

//            // 查找Buff配置
//            var buffLibrary = GetBuffLibrary();
//            var buffConfig = buffLibrary.FirstOrDefault(b => b.BuffId == buffId);
//            if (buffConfig == null)
//            {
//                Log.Error($"[{LogName}] Buff config {buffId} not found");
//                return false;
//            }

//            // 创建并添加新Buff
//            var battleBuff = ReferencePool.Acquire<BattleBuff>();
//            battleBuff.Init(buffId, buffConfig.Name, buffConfig.Description, playerId);
//            _playerBuffs[playerId].Add(battleBuff);

//            Log.Info($"[{LogName}] Added buff {buffId} ({buffConfig.Name}) to player {playerId}");
//            return true;
//        }
        
//        /// <summary>
//        /// 移除Buff
//        /// </summary>
//        public bool RemoveBuff(long playerId, int buffId)
//        {
//            ThrowIfNotInitialized();

//            if (!_playerBuffs.TryGetValue(playerId, out var buffs))
//                return false;

//            var buffToRemove = buffs.FirstOrDefault(b => b.BuffId == buffId);
//            if (buffToRemove == null) return false;

//            buffs.Remove(buffToRemove);
//            ReferencePool.Release(buffToRemove);
//            return true;
//        }

//        /// <summary>
//        /// 检查玩家是否拥有指定Buff
//        /// </summary>
//        public bool HasBuff(long playerId, int buffId)
//        {
//            ThrowIfNotInitialized();

//            return _playerBuffs.TryGetValue(playerId, out var buffs) &&
//                   buffs.Any(buff => buff.BuffId == buffId);
//        }
        
//        /// <summary>
//        /// 获取玩家所有Buff ID
//        /// </summary>
//        public List<int> GetPlayerBuffIds(long playerId)
//        {
//            ThrowIfNotInitialized();

//            return _playerBuffs.TryGetValue(playerId, out var buffs)
//                ? buffs.Select(buff => buff.BuffId).ToList()
//                : new List<int>();
//        }

//        /// <summary>
//        /// 清理指定玩家的所有Buff
//        /// </summary>
//        public void ClearPlayerBuffs(long playerId)
//        {
//            ThrowIfNotInitialized();

//            if (!_playerBuffs.TryGetValue(playerId, out var buffs)) return;

//            foreach (var buff in buffs)
//            {
//                ReferencePool.Release(buff);
//            }
//            buffs.Clear();
//        }

//        protected override void OnClear()
//        {
//            foreach (var playerBuffList in _playerBuffs.Values)
//            {
//                foreach (var buff in playerBuffList)
//                {
//                    ReferencePool.Release(buff);
//                }
//                playerBuffList.Clear();
//            }

//            _playerBuffs.Clear();
//            _playerBuffOptions.Clear();

//            Log.Info($"[{LogName}] Cleared all buffs");
//        }
        
//        /// <summary>
//        /// 根据玩家阵容和棋盘状态生成Buff选项
//        /// </summary>
//        /// <param name="playerId">玩家ID</param>
//        /// <param name="lineup">玩家阵容</param>
//        /// <param name="board">棋盘</param>
//        /// <returns>Buff选项列表</returns>
//        public List<BuffOption> GenerateBuffOptions(long playerId, List<int> lineup, CheckerBoard board)
//        {
//            ThrowIfNotInitialized();

//            // 已有的Buff IDs
//            var existingBuffIds = GetPlayerBuffIds(playerId);

//            // 可用的Buff池 - 排除已有的
//            var buffLibrary = GetBuffLibrary();
//            var availableBuffs = buffLibrary.Where(b => !existingBuffIds.Contains(b.BuffId)).ToList();

//            if (availableBuffs.Count == 0)
//            {
//                Log.Warning($"[{LogName}] No available buffs for player {playerId}, all buffs have been selected");
//                return new List<BuffOption>();
//            }

//            // 随机选择指定数量的Buff
//            var selectedBuffs = availableBuffs
//                .OrderBy(_ => _random.Next())
//                .Take(BattleConfig.Buff.OptionsPerRound)
//                .ToList();

//            // 保存生成的选项
//            _playerBuffOptions[playerId] = selectedBuffs;

//            // 详细记录生成的buff选项
//            var buffIdList = selectedBuffs.Select(b => b.BuffId).ToList();
//            Log.Info($"[{LogName}] Generated {selectedBuffs.Count} buff options for player {playerId}: [{string.Join(", ", buffIdList)}]");
//            return selectedBuffs;
//        }
        
//        /// <summary>
//        /// 玩家选择Buff
//        /// </summary>
//        /// <param name="playerId">玩家ID</param>
//        /// <param name="selectedBuffId">选择的BuffID</param>
//        /// <returns>是否选择成功</returns>
//        public bool PlayerSelectBuff(long playerId, int selectedBuffId)
//        {
//            // 首先检查玩家是否已经拥有这个buff（防止重复选择）
//            if (HasBuff(playerId, selectedBuffId))
//            {
//                Log.Warning($"[{LogName}] Player {playerId} already has buff {selectedBuffId}, ignoring duplicate selection");
//                return true; // 返回true避免客户端认为操作失败
//            }

//            // 检查是否有可选的Buff选项
//            if (!_playerBuffOptions.TryGetValue(playerId, out var buffOptions) || buffOptions.Count == 0)
//            {
//                Log.Error($"[{LogName}] No buff options available for player {playerId}");
//                return false;
//            }

//            // 检查选择的Buff是否在选项中
//            var selectedOption = buffOptions.FirstOrDefault(b => b.BuffId == selectedBuffId);
//            if (selectedOption == null)
//            {
//                var availableBuffIds = buffOptions.Select(b => b.BuffId).ToList();
//                Log.Error($"[{LogName}] Selected buff {selectedBuffId} is not in the options for player {playerId}. Available options: [{string.Join(", ", availableBuffIds)}]");
//                return false;
//            }

//            // 添加选择的Buff
//            bool result = AddBuff(playerId, selectedBuffId);

//            if (result)
//            {
//                // 清空选项
//                _playerBuffOptions.Remove(playerId);
//                Log.Info($"[{LogName}] Player {playerId} successfully selected buff {selectedBuffId}, options cleared");
//            }

//            return result;
//        }
        
//        /// <summary>
//        /// 玩家随机选择Buff（自动选择）
//        /// </summary>
//        /// <param name="playerId">玩家ID</param>
//        /// <returns>是否选择成功</returns>
//        public bool PlayerRandomSelectBuff(long playerId)
//        {
//            // 检查是否有可选的Buff选项
//            if (!_playerBuffOptions.TryGetValue(playerId, out var buffOptions) || buffOptions.Count == 0)
//            {
//                Log.Error($"[{LogName}] No buff options available for player {playerId}");
//                return false;
//            }
            
//            // 随机选择一个Buff
//            int randomIndex = _random.Next(buffOptions.Count);
//            int selectedBuffId = buffOptions[randomIndex].BuffId;
            
//            Log.Info($"[{LogName}] Random selected buff {selectedBuffId} for player {playerId}");
            
//            // 添加选择的Buff
//            bool result = AddBuff(playerId, selectedBuffId);
            
//            if (result)
//            {
//                // 清空选项
//                _playerBuffOptions.Remove(playerId);
//            }
            
//            return result;
//        }
        
//        /// <summary>
//        /// 获取当前回合的Buff选项
//        /// </summary>
//        /// <param name="playerId">玩家ID</param>
//        /// <returns>Buff选项列表</returns>
//        public List<BuffOption> GetPlayerBuffOptions(long playerId)
//        {
//            if (_playerBuffOptions.TryGetValue(playerId, out var options))
//            {
//                return options;
//            }
//            return new List<BuffOption>();
//        }
        
//        /// <summary>
//        /// 获取可用的Buff选项（用于回合开始时发送给客户端）
//        /// </summary>
//        /// <returns>可用的Buff ID列表</returns>
//        public List<int> GetAvailableBuffOptions()
//        {
//            var buffLibrary = GetBuffLibrary();
//            return buffLibrary.Select(b => b.BuffId).ToList();
//        }

//        /// <summary>
//        /// 应用战斗开始时的Buff效果
//        /// </summary>
//        /// <param name="playerId">玩家ID</param>
//        public void ApplyBattleStartBuffs(long playerId)
//        {
//            if (!_playerBuffs.ContainsKey(playerId))
//                return;

//            foreach (var buff in _playerBuffs[playerId])
//            {
//                // 应用战斗开始时的Buff效果
//                // 实际项目中应该有更复杂的逻辑
//                Log.Info($"[{LogName}] Applying battle start buff {buff.BuffId} ({buff.Name}) for player {playerId}");
//            }
//        }
//    }
//}
