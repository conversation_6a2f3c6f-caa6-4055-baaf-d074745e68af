#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableTreasure
	{

		public static readonly string TName="Treasure.json";

		#region 属性定义
		/// <summary> 
		/// 宝物ID 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 品质 
		/// </summary> 
		public int quality {get; set;}
		/// <summary> 
		/// 升级上限 
		/// </summary> 
		public int MaxLv {get; set;}
		/// <summary> 
		/// 升星上限 
		/// </summary> 
		public int MaxStar {get; set;}
		/// <summary> 
		/// 羁绊英雄 
		/// </summary> 
		public int[] Role {get; set;}
		/// <summary> 
		/// 羁绊效果 
		/// </summary> 
		public int[] EffectRelation {get; set;}
		/// <summary> 
		/// 升星效果 
		/// </summary> 
		public int[] EffectStar {get; set;}
		/// <summary> 
		/// 等级属性 
		/// </summary> 
		public int[][] Attribute {get; set;}
		/// <summary> 
		/// 名称 
		/// </summary> 
		public int Name {get; set;}
		/// <summary> 
		/// 描述 
		/// </summary> 
		public int Des {get; set;}
		/// <summary> 
		/// 图标资源 
		/// </summary> 
		public int Icon {get; set;}
		#endregion

		public static TableTreasure GetData(int ID)
		{
			return TableManager.TreasureData.Get(ID);
		}

		public static List<TableTreasure> GetAllData()
		{
			return TableManager.TreasureData.GetAll();
		}

	}
	public sealed partial class TableTreasureData
	{
		private Dictionary<int, TableTreasure> dict = new Dictionary<int, TableTreasure>();
		private List<TableTreasure> dataList = new List<TableTreasure>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableTreasure.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableTreasure>>(jsonContent);
			foreach (TableTreasure config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableTreasure Get(int id)
		{
			if (dict.TryGetValue(id, out TableTreasure item))
				return item;
			return null;
		}

		public List<TableTreasure> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
