﻿
using Aurora.Framework;
using BattleServer.Server;

namespace BattleServer.Server
{
    public class GlobalManager<T> : Singleton<T>, IWorkUnitOwner where T : GlobalManager<T>, new()
    {
        private string m_strLogName;
        private WorkUnitBase m_WorkUnit;

        public GlobalManager()
        {
            (this as IWorkUnitOwner).InitWorkUnit();
        }

        public string LogName
        {
            get { return m_strLogName; }
            set { m_strLogName = value; }
        }

        public WorkUnitBase WorkUnit
        {
            get { return m_WorkUnit; }
            set { m_WorkUnit = value; }
        }

        public virtual void Tick(int nDeltaTime)
        {
        }

        void IWorkUnitOwner.InitWorkUnit()
        {
            WorkUnitManager.Instance.CreateWorkUnit(this);
        }

        void IWorkUnitOwner.ClearWorkUnit()
        {
            WorkUnitManager.Instance.DestroyWorkUnit(this);
        }

        public virtual void ProcessMessage(IMessage message)
        {
        }

        public virtual void OnStart()
        {
        }

        public virtual void OnStop()
        {
        }

        public virtual void OnDestroy()
        {
            (this as IWorkUnitOwner).ClearWorkUnit();
        }

        public virtual void OnException(Exception e)
        {
        }
    }
}
