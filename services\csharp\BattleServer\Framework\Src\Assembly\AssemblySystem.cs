﻿//*********************************************************
// Framework
// Author:  Jasen
// Date  :  2022-11-24
//*********************************************************

using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;

namespace Aurora.Framework
{
    //全局Assembly模块管理相关
    public class AssemblyManager:Singleton<AssemblyManager>
    {   

        private readonly Dictionary<string, Type> m_AllTypes = new Dictionary<string, Type>();
        private readonly List<Type> m_AllTypesList = new List<Type>();

        private readonly Dictionary<string, List<Type>> m_AttributeTypes = new Dictionary<string, List<Type>>();
        private readonly Dictionary<string, Dictionary<Type, List<string>>> m_AttributeTypeMethods = new Dictionary<string, Dictionary<Type, List<string>>>();

        public void AddTypeMethods(string attribute, Type type, string methodName)
        {
            if (type == null) return;
            Dictionary<Type, List<string>> typesList;
            if (!m_AttributeTypeMethods.TryGetValue(attribute, out typesList))
            {
                typesList = new Dictionary<Type, List<string>>();
                m_AttributeTypeMethods.Add(attribute, typesList);
            }
            List<string> nameList;
            if (!typesList.TryGetValue(type, out nameList))
            {
                nameList = new List<string>();
                typesList.Add(type, nameList);
            }
            if (nameList.Contains(methodName))
            {
                return;
            }
            nameList.Add(methodName);
            typesList[type] = nameList;
            m_AttributeTypeMethods[attribute] = typesList;

            if (!m_AllTypes.ContainsKey(type.FullName))
                m_AllTypes[type.FullName] = type;
            if (!m_AllTypesList.Contains(type))
                m_AllTypesList.Add(type);
        }

        public void AddTypes(string attribute, Type type)
        {
            if (type == null) return;
            List<Type> list;
            if (!m_AttributeTypes.TryGetValue(attribute, out list))
            {
                list = new List<Type>();
                m_AttributeTypes.Add(attribute, list);
            }
            if (list.Contains(type))
            {
                return;
            }
            list.Add(type);
            m_AttributeTypes[attribute] = list;

            if (!m_AllTypes.ContainsKey(type.FullName))
                m_AllTypes[type.FullName] = type;
            if (!m_AllTypesList.Contains(type))
                m_AllTypesList.Add(type);
        }
        public void AddAssembly(Assembly assembly)
        {
            if (assembly == null)
                return;
            foreach (Type type in assembly.GetTypes())
            {
                if(!m_AllTypes.ContainsKey(type.FullName))
                    m_AllTypes[type.FullName] = type;
                else
                {
                    Log.Warning($"AssemblyManager in m_AllTypes has exist: type:{type.FullName} in Assembly:{assembly.FullName}");
                }
                if(!m_AllTypesList.Contains(type))
                {
                    m_AllTypesList.Add(type);
                }
                else
                {
                    Log.Warning($"AssemblyManager in m_AllTypesList has exist type:{type.FullName} in Assembly:{assembly.FullName}");
                }
            }
        }

        public Dictionary<string,Type> GetAllTypesDic()
        {
            return m_AllTypes;
        }

        public List<Type> GetAllTypes()
        {
            return m_AllTypesList;
        }
        public List<Type> GetAllTypes<T>()where T : Attribute
        {
            string attribute = typeof(T).FullName.Replace('+', '.');
            List<Type> types;
            if (m_AttributeTypes.TryGetValue(attribute, out types))
            {
                return types;
            }
            return new List<Type>();
        }
        public Dictionary<Type, List<string>> GetAllTypeMethods<T>() where T : Attribute
        {
            string attribute = typeof(T).FullName.Replace('+', '.');
            Dictionary<Type, List<string>> types;
            if (m_AttributeTypeMethods.TryGetValue(attribute, out types))
            {
                return types;
            }
            return new Dictionary<Type, List<string>>();
        }
        
    }
}
