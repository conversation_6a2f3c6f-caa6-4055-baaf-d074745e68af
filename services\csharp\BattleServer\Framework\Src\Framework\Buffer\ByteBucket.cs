﻿//*********************************************************
// Framework
// Author:  Jasen
// Date  :  2023-4-12
//*********************************************************

using System.Collections.Generic;

namespace Aurora.Framework
{
    //Byte数组桶容器
    internal sealed class ByteBucket : Stack<byte[]>
    {
        int m_Size;     //byte数组大小
        public int ElmSize => m_Size;

        public ByteBucket(int size)
        {
            m_Size = size;
        }

        public long TotalSize => m_Size * Count;

        public bool TryGet(out byte[] buff)
        {
            if(TryPop(out buff))
            {
                return true;
            }
            return false;
        }

        public void Return(byte[] buff)
        {
            Push(buff);
        }
    }
}
