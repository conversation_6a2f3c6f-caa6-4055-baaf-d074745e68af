﻿namespace Aurora.Framework
{
    public class ValueMonitor
    {
        private ulong m_nMinBaseLine;
        private ulong m_nMaxIncreaseAmount;
        private float m_fBaseLineIncreaseRatio;
        private ulong m_nLowLine;
        private ulong m_nBaseLine;
        private ulong m_nHighLine;

        public ValueMonitor(ulong nMinBaseLine, ulong nMaxIncreaseAmount, float fBaseLineIncreaseRatio = 2)
        {
            const ulong nMinBaseLineMinLimit = 16;
            const ulong nMaxIncreaseAmountMinLimit = 16;
            const float fBaseLineIncreaseRatioMinLimit = 1.125f;

            m_nMinBaseLine = (nMinBaseLine < nMinBaseLineMinLimit) ? nMinBaseLineMinLimit : nMinBaseLine;
            m_nMaxIncreaseAmount = (nMaxIncreaseAmount < nMaxIncreaseAmountMinLimit) ? nMaxIncreaseAmountMinLimit : nMaxIncreaseAmount;
            m_fBaseLineIncreaseRatio = (fBaseLineIncreaseRatio < fBaseLineIncreaseRatioMinLimit) ? fBaseLineIncreaseRatioMinLimit : fBaseLineIncreaseRatio;

            ResetBaseLine(m_nMinBaseLine);
        }

        public void Clear()
        {
            ResetBaseLine(m_nMinBaseLine);
        }

        private void ResetBaseLine(ulong nBaseLine)
        {
            m_nBaseLine = nBaseLine;

            m_nLowLine = (ulong)(m_nBaseLine / m_fBaseLineIncreaseRatio);
            if ((m_nBaseLine - m_nLowLine) > m_nMaxIncreaseAmount)
            {
                m_nLowLine = m_nBaseLine - m_nMaxIncreaseAmount;
            }

            m_nHighLine = (ulong)(m_nBaseLine * m_fBaseLineIncreaseRatio);
            if ((m_nHighLine - m_nBaseLine) > m_nMaxIncreaseAmount)
            {
                m_nHighLine = m_nBaseLine + m_nMaxIncreaseAmount;
            }
        }

        public bool CheckCross(ulong nNewValue, out bool bAbove, out ulong nCrossLine)
        {
            if (nNewValue < m_nLowLine)
            {
                if (m_nBaseLine > m_nMinBaseLine)
                {
                    bAbove = false;
                    nCrossLine = m_nLowLine;
                    ResetBaseLine(m_nLowLine);
                    return true;
                }
            }
            else if (nNewValue > m_nHighLine)
            {
                bAbove = true;
                nCrossLine = m_nHighLine;
                ResetBaseLine(m_nHighLine);
                return true;
            }

            bAbove = false;
            nCrossLine = 0;
            return false;
        }

        public bool CheckCross(long nNewValue, out bool bAbove, out ulong nCrossLine)
        {
            ulong nCorrectedValue = (nNewValue < 0) ? 0 : (ulong)nNewValue;
            return CheckCross(nCorrectedValue, out bAbove, out nCrossLine);
        }
    }
}
