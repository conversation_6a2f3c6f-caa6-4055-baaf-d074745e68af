﻿//*********************************************************
// Framework
// Author:  Jasen
// Date  :  2022-11-16
//*********************************************************

using System;
using System.IO;


namespace Aurora.Framework
{
    public enum MessageType : byte
    {
        None = 0,
        PB,
        <PERSON><PERSON>,
        <PERSON>son,
    }
    public enum MessageClass : byte
    {
        None = 0,
        IMessage,
        IRequest,
        IResponse,
    }
    public enum MessageTarget
    {
        None = 0,
        Client,
        Login,
        Gate,
        Server,
        World,
        DB,
        AOI,
        Chat,
        GMT,
        MatchServer,
    }
    public interface IJsonMessage
    {
        void Serialize(MemoryStream memoryStream);
        void Deserialize(MemoryStream memoryStream);
    }

    public interface IBsonMessage
    {
        void Serialize(MemoryStream memoryStream);
        void Deserialize(MemoryStream memoryStream);
    }

    public interface IPacket
    {
        //MessageType MsgType { get; set; }
        MessageClass MsgClass { get; set; }
        ushort MsgID { get; set; }
        int RpcID { get; set; }
        //long ActorID { get; set; }
        int PktSize { get; set; }

        void SetMessage(IMessage msg);
        IMessage GetMessage();
    }

    public class MessageAddress
    {
        public enum EType : byte
        {
            None = 0,
            Client,
            ClientProxy,
            Stage,
            Service,
            GlobalMgr,
            Scene,
            LoginPlayer,
            GamePlayer,
            GMT,
            MatchServer,
        }

		public EType m_eType { get; set; }
        public ushort m_nServerNum { get; set; }
        public byte m_nServiceType { get; set; }
        public ushort m_nServiceIndex { get; set; }
        public ushort m_nSceneID { get; set; }
        public uint m_nPlayerID { get; set; }
        public long m_nGUID { get; set; }
        public byte m_nGlobalMgrType { get; set; }
        public int m_nZwid { get; set; }
        public int m_nBakZwid { get; set; }     //备选ZoneWorldID，现在唯一用到的地方是ToSpecialWorldGuid，平时不要用
        public int m_nBakServerNum { get; set; }    //备选ServerNum

        enum EArgumentIndex : ushort
		{
			Type = 0,
			ServerID,
			ServiceType,
			ServiceIndex,
			SceneID,
			PlayerID,
			GUID,
            GlobalMgrType,
            ZWID,       //ZoneWorldID
            BakZWID,
            BakServerNum,    //
		}

        public MessageAddress()
        {
            Init();
        }

        public void Init()
        {
            m_eType = EType.None;
            m_nServerNum = 0;
            m_nServiceType = 0;
            m_nServiceIndex = 0;
            m_nSceneID = 0;
            m_nPlayerID = 0;
            m_nGUID = 0;
            m_nGlobalMgrType = 0;
            m_nZwid = 0;
            m_nBakZwid = 0;
            m_nBakServerNum = 0;
        }

        public void CopyFrom(MessageAddress address)
        {
            if (address == null) return;
            m_eType = address.m_eType;
            m_nServerNum = address.m_nServerNum;
            m_nServiceType = address.m_nServiceType;
            m_nServiceIndex = address.m_nServiceIndex;
            m_nSceneID = address.m_nSceneID;
            m_nPlayerID = address.m_nPlayerID;
            m_nGUID = address.m_nGUID;
            m_nGlobalMgrType = address.m_nGlobalMgrType;
            m_nZwid = address.m_nZwid;
            m_nBakZwid = address.m_nBakZwid;
            m_nBakServerNum = address.m_nBakServerNum;
        }

        public void SetAsNone()
        {
            Init();
        }

        public void SetAsClient()
        {
            Init();
            m_eType = EType.Client;
        }

        public void Read(BinaryReader reader)
        {
            Init();

            if (reader == null) return;
            ushort nFlag = reader.ReadUInt16();

            if ((nFlag & (1 << (ushort)EArgumentIndex.Type)) != 0)
            {
                m_eType = (EType)reader.ReadByte();
            }

			if ((nFlag & (1 << (ushort)EArgumentIndex.ServerID)) != 0)
			{
                m_nServerNum = reader.ReadUInt16();
			}

			if ((nFlag & (1 << (ushort)EArgumentIndex.ServiceType)) != 0)
			{
				m_nServiceType = reader.ReadByte();
			}

			if ((nFlag & (1 << (ushort)EArgumentIndex.ServiceIndex)) != 0)
			{
				m_nServiceIndex = reader.ReadUInt16();
			}

			if ((nFlag & (1 << (ushort)EArgumentIndex.SceneID)) != 0)
			{
				m_nSceneID = reader.ReadUInt16();
			}

			if ((nFlag & (1 << (ushort)EArgumentIndex.PlayerID)) != 0)
			{
				m_nPlayerID = reader.ReadUInt32();
			}

			if ((nFlag & (1 << (ushort)EArgumentIndex.GUID)) != 0)
			{
				m_nGUID = reader.ReadInt64();
			}

            if ((nFlag & (1 << (ushort)EArgumentIndex.GlobalMgrType)) != 0)
            {
                m_nGlobalMgrType = reader.ReadByte();
            }

            if ((nFlag & (1 << (ushort)EArgumentIndex.ZWID)) != 0)
            {
                m_nZwid = reader.ReadInt32();
            }

            if ((nFlag & (1 << (ushort)EArgumentIndex.BakZWID)) != 0)
            {
                m_nBakZwid = reader.ReadInt32();
            }

            if ((nFlag & (1 << (ushort)EArgumentIndex.BakServerNum)) != 0)
            {
                m_nBakServerNum = reader.ReadInt32();
            }
        }

        public int Write(BinaryWriter writer)
        {
            if (writer == null) return 0;
			int nBeginPosition = (int)writer.BaseStream.Position;
			ushort nFlag = 0;
			writer.Seek(sizeof(ushort), SeekOrigin.Current);

			if (m_eType != EType.None)
            {
                writer.Write((byte)m_eType);
				nFlag |= (1 << (ushort)EArgumentIndex.Type);
			}

			if (m_nServerNum != 0)
			{
				writer.Write(m_nServerNum);
				nFlag |= (1 << (ushort)EArgumentIndex.ServerID);
			}

			if (m_nServiceType != 0)
			{
				writer.Write(m_nServiceType);
				nFlag |= (1 << (ushort)EArgumentIndex.ServiceType);
			}

			if (m_nServiceIndex != 0)
			{
				writer.Write(m_nServiceIndex);
				nFlag |= (1 << (ushort)EArgumentIndex.ServiceIndex);
			}

			if (m_nSceneID != 0)
			{
				writer.Write(m_nSceneID);
				nFlag |= (1 << (ushort)EArgumentIndex.SceneID);
			}

			if (m_nPlayerID != 0)
			{
				writer.Write(m_nPlayerID);
				nFlag |= (1 << (ushort)EArgumentIndex.PlayerID);
			}

			if (m_nGUID != 0)
			{
				writer.Write(m_nGUID);
				nFlag |= (1 << (ushort)EArgumentIndex.GUID);
			}

            if (m_nGlobalMgrType != 0)
            {
                writer.Write(m_nGlobalMgrType);
                nFlag |= (1 << (ushort)EArgumentIndex.GlobalMgrType);
            }
            if (m_nZwid != 0) 
            {
                writer.Write(m_nZwid);
                nFlag |= (1 << (ushort)EArgumentIndex.ZWID);
            }
            if (m_nBakZwid != 0)
            {
                writer.Write(m_nBakZwid);
                nFlag |= (1 << (ushort)EArgumentIndex.BakZWID);
            }
            if (m_nBakServerNum != 0)
            {
                writer.Write(m_nBakServerNum);
                nFlag |= (1 << (ushort)EArgumentIndex.BakServerNum);
            }

            int nEndPosition = (int)writer.BaseStream.Position;
			writer.Seek(nBeginPosition, SeekOrigin.Begin);
			writer.Write(nFlag);
			writer.Seek(nEndPosition, SeekOrigin.Begin);

            return nEndPosition - nBeginPosition;
        }
    }

    public class Packet : MemoryStream,IPacket, IReference
    {
        private BinaryWriter _writer = null;
        private BinaryReader _reader = null;

        //public MessageType MsgType { get; set; }
        public MessageClass MsgClass { get; set; }
        public MessageTarget MsgTarget { get; set; }

        public MessageAddress SourceAddress { get;  } = new MessageAddress();
        public MessageAddress TargetAddress { get; } = new MessageAddress();

        public ushort MsgID { get; set; }
        private int m_RpcID;
        public int RpcID { get { return m_RpcID; }
            set {
                m_RpcID = value;
                if (Length <= 0)
                    return;
                //已经有消息流了
                GetBuffer().WriteTo(6, m_RpcID);
            }
        }

        public int PktSize { get; set; }//整个包的长度，包头+消息体

        public int MsgSize { get; set; } //IMessage的长度

        private IMessage m_Message;

        //! 标识是否已经被序列化过了，这个现在用于广播优化
        public bool IsSerialized { get; set; }

        //! 这个标识是否需要你自己主动调用回收，而不被Dispatcher回收，为了不想深拷贝，也是用于广播优化
        public bool NeedDestoryByYourSelf { get; set; }

        public static Packet Create()
        {
            return ReferencePool.Acquire<Packet>();
        }

        public static Packet Create(IMessage message)
        {
            Packet packet = Create();
            {
                packet.SetMessage(message);
            }
            return packet;
        }

        public static void Destroy(Packet packet, bool bForceRelease = false)
        {
            if (bForceRelease || !packet.NeedDestoryByYourSelf)
            {
                ReferencePool.Release(packet);
            }          
        }

        public Packet() : base(1024)
        {
            _writer = new BinaryWriter(this);
            _reader = new BinaryReader(this);

            Clear();
        }

        public void Clear()
        {
            MsgClass = MessageClass.None;
            MsgTarget = MessageTarget.None;
            //ActorID = 0;
            PktSize = 0;
            MsgSize = 0;
            m_RpcID = 0;
            SourceAddress.Init();
            TargetAddress.Init();
            m_Message = null;
            SetLength(0);
            Position = 0;
            IsSerialized = false;
            NeedDestoryByYourSelf = false; 
        }

        public void SetMessage(IMessage msg)
        {
            if (msg == null)
            {
                Log.Error($"Packet set message is empty!");
                return;
            }
            m_Message = msg;
            MsgID = MessageIDComponent.Instance.GetMsgID(msg.GetType());
            MsgClass = Packet.GetMessageClass(msg);
            MsgTarget = MessageIDComponent.Instance.GetMsgTarget(msg.GetType());
        }

        public void Serialize()
        {
            if (m_Message == null)
            {
				Log.Error($"Packet message is empty!");
				return;
            }

            SetLength(0);
			Seek(sizeof(ushort), SeekOrigin.Begin);
            _writer.Write(MsgID);
            //w.Write((byte)MsgType);
            _writer.Write((byte)MsgClass);
            _writer.Write((byte)MsgTarget);
            _writer.Write(RpcID);
            //w.Write(ActorID);
            SourceAddress.Write(_writer);
            TargetAddress.Write(_writer);
            //byte[] msgBytes = ProtobufHelper.Serialize(m_Message);
            //writer.Write(msgBytes);
            //ProtobufHelper.Serialize(m_Message, this);
            Type msgType = MessageIDComponent.Instance.GetMsgType(MsgID);
            byte[] msgBytes = MemoryPackHelper.SerializeMessage(msgType, m_Message);

            MsgSize = msgBytes.Length;
            _writer.Write(MsgSize);
            _writer.Write(msgBytes);

            PktSize = (int)Position;
            if (PktSize > ushort.MaxValue)
            {
                throw new FrameworkException($"Packet size too long! size={PktSize},msg={m_Message.GetType().Name}");
            }
            
#if PRINT_PACKET
            Log.Info($"[PacketSize] Serialize,{MsgID},{PktSize},{MsgSize}");
#endif
            
            _writer.Seek(0, SeekOrigin.Begin);
            _writer.Write((ushort)PktSize);
            _writer.Seek(0, SeekOrigin.Begin);
        }

        public IMessage GetMessage()
        {
            if(m_Message == null)
            {
				//ParseMessage();
				throw new FrameworkException("Packet get message error, message is null!");
			}
            return m_Message;
        }
        public void ParseHead()
        {
            Seek(0, SeekOrigin.Begin);
            PktSize = _reader.ReadUInt16();
            MsgID = _reader.ReadUInt16();
            //MsgType = (MessageType)r.ReadByte();
            MsgClass = (MessageClass)_reader.ReadByte();
            MsgTarget = (MessageTarget)_reader.ReadByte();
            RpcID = _reader.ReadInt32();
            //ActorID = r.ReadInt64();
            SourceAddress.Read(_reader);
            TargetAddress.Read(_reader);
            MsgSize = _reader.ReadInt32();
        }
        public void ParseMessage()
        {
            Type msgType = MessageIDComponent.Instance.GetMsgType(MsgID);

            //byte[] msgBytes = _reader.ReadBytes(MsgSize);
            int begin = (int)(this.Length - MsgSize);
            ReadOnlySpan<byte> msgBytes = new ReadOnlySpan<byte>(GetBuffer(), begin, MsgSize);
            m_Message = MemoryPackHelper.DeserializeMessage(msgType, msgBytes) as IMessage;
            //m_Message =  msg as IMessage;
        }

        public static MessageClass GetMessageClass(IMessage msg)
        {
            if (msg == null) return MessageClass.None;
            return MessageIDComponent.Instance.GetMsgClass(msg.GetType());
        }
    }
}
