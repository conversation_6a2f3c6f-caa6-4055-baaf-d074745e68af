﻿/*
 * @description: tools
 * @Auther: Kimsee
 * @Date: 2022-11-04 22:01
 * 
*/

using Aurora.Framework;
using System.Net.NetworkInformation;
using System.Net;
using System.Reflection;
using System.Diagnostics;
using System.Runtime;
using System.Runtime.InteropServices;
using BattleServer.Server;
using System.Reflection.Emit;
using System.Security.AccessControl;
//using Aurora.Game;

namespace BattleServer.Server
{
    public class MainThread : ThreadObject
    {
        private static MainThread instance;
        public static MainThread Instance
        {
            get
            {
                if (null == instance)
                {
                    instance = new MainThread();
                }
                return instance;
            }
        }
        private MainThread()
        {
        }

        //static bool IsPortInUse(int port)
        //{
        //    var ipGlobalProperties = IPGlobalProperties.GetIPGlobalProperties();

        //    var listeners = ipGlobalProperties.GetActiveTcpListeners();
        //    if (listeners.Any(endpoint => endpoint.Port == port))
        //        return true;

        //    var connections = ipGlobalProperties.GetActiveTcpConnections();
        //    if (connections.Any(connection => connection.LocalEndPoint.Port == port))
        //        return true;
        //    return false;
        //}

        //static void OutputProcessId(AppType appType)
        //{
        //    int currentProcessId = Environment.ProcessId;
        //    string PidFileName = $"{appType}.PID";
        //    using (StreamWriter writer = new StreamWriter(PidFileName, true))
        //    {
        //        writer.WriteLine($"{currentProcessId}");
        //    }
        //}

        public bool Init(string configFilePath)
        {


            string ServerConfigRoot = string.Format("{0}/{1}", Environment.CurrentDirectory, "ServerOnlyConfig/");

            // 初始化配置文件管理
            //ServerConfigManager.Instance.Init(configFilePath);
            // 初始化日志系统            
            ILog consolelogger = null;
            //linux上不开
#if CONSOLE_LOG
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows) && GlobalArgs.bConsole == true)
            {
                consolelogger = new ConsoleLogger();
            }

#endif
            //string nlogConfigPath = ServerConfigManager.Instance.GlobalConfig.LogConfigFilePath;
            string nlogConfigPath = "../configs/NLog.config";
            string fullPath = $"{Environment.CurrentDirectory}/{nlogConfigPath}";
            fullPath = fullPath.Replace('\\', Path.DirectorySeparatorChar);
            ILog logger = new NLogger("BattleServcer", 30001, fullPath);
            Log.Init(logger, consolelogger);

            //            // 初始化publicconfig
            //            PublicConfigManager.Instance.Init();

            //            IDGenerater.Instance.Init(ServerNumHelper.ServerNum);


            //            //// 往进程号文件中追加进程ID,方便根据进程ID关服
            //            //{
            //            //    OutputProcessId(ServerConfigManager.Instance.AppConfig.AppType);
            //            //}

            //            // 初始化参数集
            //            PoolDefineSystem.Instance.LoadFromXML(ServerConfigRoot + "ParamData/ParamXmlDefine.xml");




            Log.Info("MainThread Start.");
            //            BILog.Init();
            LoadAndInitAppAssembly();

            //            ProtobufHelper.InitCommonModel();

            //            MongoHelper.Init();
            //            //SkillLogicModule.Instance.Init();

            WorkUnitManager.Instance.Init(null);

            ThreadManager.Instance.Init(this, 16);
            ThreadManager.Instance.StartWorkUnit(ThreadManager.Instance.WorkUnit, ThreadType.MAIN_THREAD);

            //DataTableManager.Instance.SetLoader(new JsonDataTableLoader());

            //#if OPEN_PERFORMANCE
            //            PerformanceMonitorManager.Instance.SetMonitor(new PerformanceMonitor());
            //#endif

            //            EntitySystem.InstanceGetter = () => { return ThreadObject.CurrentWorkUnit?.EntitySystem; };

            return true;
        }

        public void InitGCSetting()
        {
            //GCSettings.LatencyMode = (GCLatencyMode)ServerConfigManager.Instance.LoginFlowConfig.GCSetting.LatencyMode;
            //Log.Info($"InitGCSetting: {GCSettings.LatencyMode},  Cfg: {ServerConfigManager.Instance.LoginFlowConfig.GCSetting.LatencyMode}");
        }

        public void AfterInitCheck()
        {
            {
                //打印当前环境变量
                Log.Info("=== 环境变量 ===");
                Log.Info($"DOTNET_gcServer={Environment.GetEnvironmentVariable("DOTNET_gcServer")}");
                Log.Info($"DOTNET_GCHeapCount: {Environment.GetEnvironmentVariable("DOTNET_GCHeapCount")}");
                Log.Info($"DOTNET_gcConcurrent" +
                         $": {Environment.GetEnvironmentVariable("DOTNET_gcConcurrent")}");
                Log.Info($"DOTNET_GCDynamicAdaptationMode: {Environment.GetEnvironmentVariable("DOTNET_GCDynamicAdaptationMode")}");
                //Log.Info($"LatencyMode: {ServerConfigManager.Instance.LoginFlowConfig.GCSetting.LatencyMode}");
                Log.Info("===================");
            }
            {
                // 打印当前 GC 配置
                Log.Info("=== .NET GC 配置 ===");
                Log.Info($"GC 模式: {(GCSettings.IsServerGC ? "Server GC" : "Workstation GC")}");
                //Log.Info($"后台 GC: {(GCSettings.LatencyMode == GCLatencyMode.Interactive ? "Enabled" : "Disabled")}");
                Log.Info($"GC的LatencyMode模式: {GCSettings.LatencyMode}");
                Log.Info($"LOH 压缩模式: {GCSettings.LargeObjectHeapCompactionMode}");
                Log.Info("===================");
            }

            ////检查T服的配置文件
            //if (ServerNumHelper.SelfIsTServer())
            //{
            //    var tServerinfo = TServerListCategory.Instance.Get(ServerNumHelper.ServerNum);
            //    if (tServerinfo == null)
            //    {
            //        throw new Exception($"MainThread.AfterInitCheck, tServerinfo not in T, nZoneWorldId:{ServerNumHelper.ZoneWorldId}, Process:{ServerNumHelper.ServerNum} !");
            //    }
            //    Console.WriteLine($"This Server is a TServer! ServerNum=\n{ServerNumHelper.ServerNum}");
            //}

            //CheckNetAddress();

            //// 往进程号文件中追加进程ID,方便根据进程ID关服
            //{
            //    OutputProcessId(ServerConfigManager.Instance.AppConfig.AppType);
            //}
        }

        public void CheckNetAddress()
        {
            //// 检测是否端口占用
            //if (!ServerNumHelper.SelfIsTServer())
            //{
            //    //GMT端口检测
            //    if (ServerNumHelper.SelfIsGMT())
            //    {
            //        CheckPortInUse(ServerConfigManager.Instance.GMTConfig.GMTConfig.OutPort);
            //        CheckPortInUse(ServerConfigManager.Instance.GMTConfig.GMTConfig.InPort);
            //    }
            //    //匹配服检测
            //    else if (ServerNumHelper.SelfIsMatchServer())
            //    {

            //    }
            //    else if (ServerNumHelper.SelfIsIgniteCli())
            //    {

            //    }
            //    else
            //    {
            //        ServerConfig serverConfig = ServerConfigManager.Instance.GroupConfig.GetServerConfig(ServerNumHelper.ServerNum);
            //        CheckPortInUse(serverConfig.InPort);
            //        CheckPortInUse(serverConfig.OutPort);
            //    }
            //}
            //else
            //{
            //    //检查T服的对外端口有没有占用
            //    var tServerinfo = TServerListCategory.Instance.Get(ServerNumHelper.ServerNum);
            //    CheckPortInUse(tServerinfo.OutPort);
            //}
        }

        public bool Release()
        {
            return true;
        }
        // 加载app配置中依赖的程序集
        private void LoadAndInitAppAssembly()
        {
            Log.Info("MainThread LoadAndInitAppAssembly Start...");
            ///*const string prefix = "ServerLibs/";
            //const string ext = ".dll";
            //foreach (AppGameConfig.AssemblyLoadInfo info in ServerConfigManager.Inst.AppConfig.AssemblyList)
            //{
            //    string fullPath = string.Format("{0}/{1}{2}{3}", Environment.CurrentDirectory, prefix, info.Name, ext);
            //    bool ret = AssemblyManager.Inst.LoadModule(fullPath);
            //    if (ret)
            //    {
            //        IModule loaded = AssemblyManager.Inst.GetModule(info.Name);
            //        loaded.Init(info.ConfigPath);
            //    }
            //}*/
            AssemblyManager.Instance.AddAssembly(Assembly.GetEntryAssembly());//主程序集
            AssemblyManager.Instance.AddAssembly(Assembly.GetAssembly(typeof(LiteFrame.Framework.Framework)));//框架程序集
            //foreach (AppGameConfig.AssemblyLoadInfo info in ServerConfigManager.Instance.AppConfig.AssemblyList)
            //{
            //    switch (info.Name)
            //    {
            //        case "Common"://其实这个也都是包含的,就是看后面别的程序集
            //            {
            //                //AssemblyManager.Instance.AddAssembly(Assembly.GetAssembly(typeof(Common)));
            //            }
            //            break;
            //        default:
            //            break;
            //    }
            //}
            //// 初始化EntitySystem            
            //EntitySystem.InitAssembly();
            //PoolCallbackSystem.Instance.InitAssembly();



            Log.Info("MainThread LoadAndInitAppAssembly End...");
        }

        public new void Loop()
        {
            BeforeRun();

            while (true)//todo: app结束的信号判定
            {
                try
                {
                    if (!Run())
                    {
                        break;
                    }

                    Thread.Sleep(s_nLoopIntervalInMS);
                }
                catch (Exception ex)
                {
                    Log.Exception($"Main Loop catch exception! \n{ex.ToString()}");
                }
            }
        }

    }
}
