﻿//*********************************************************
// Framework
// Author:  Jasen
// Date  :  2022-11-30
//*********************************************************

using System;
using System.Collections.Generic;

namespace Aurora.Framework
{
    public interface ISingleton : IDisposable
    {
        public void Destroy();
        bool IsDisposed();
    }

    public abstract class Singleton<T> : ISingleton where T : Singleton<T>, new()
    {
        private bool isDisposed;

        private volatile static T instance;
        private static object m_InstanceLock = new object();

        public static T Instance
        {
            get
            {
                if (null == instance)
                {
                    lock (m_InstanceLock)
                    {
                        if (null == instance)
                        {
                            instance = new T();
                            //SingletonManager.Register(typeof(T).Name, instance);
                        }
                    }
                }
                return instance;
            }
        }

        public void Destroy()
        {
            if (this.isDisposed)
            {
                return;
            }
            this.isDisposed = true;

            instance?.Dispose();
            instance = null;
        }

        bool ISingleton.IsDisposed()
        {
            return this.isDisposed;
        }

        public virtual void Dispose()
        {
            Destroy();
        }
    }

    public class SingletonManager
    {
        public static Dictionary<string, ISingleton> m_AllSingletonList;
        public static void Register(string name, ISingleton obj)
        {
            if (obj == null) return;
            if (null == m_AllSingletonList)
                m_AllSingletonList = new Dictionary<string, ISingleton>();
            if (m_AllSingletonList.TryAdd(name, obj))
            {
                if (typeof(IModuleRunnable).IsAssignableFrom(obj.GetType()))
                {
                    IModuleRunnable runnable = obj as IModuleRunnable;
                    if (ModuleManager.Instance.m_RunnableModuleList.ContainsKey(obj.GetType().Name))
                    {
                        throw new Exception($" ModuleHelper.AddModule failed. Type[{obj.GetType().Name}] duplicated in m_RunnableModuleList !!");
                    }
                    ModuleManager.Instance.m_RunnableModuleList.TryAdd(obj.GetType().Name, runnable);
                }
            }
            else
            {
                throw new Exception($"singleton register twice! {name}");
            }
        }
    }
}
