﻿//*********************************************************
// Framework
// Author:  Jasen
// Date  :  2022-11-4
//*********************************************************


using System;
using System.Runtime.CompilerServices;

namespace Aurora.Framework
{
    public struct ATaskAsyncNothingMethodBuilder
    {
        public static ATaskAsyncNothingMethodBuilder Create()
        {
            return new ATaskAsyncNothingMethodBuilder() { m_Task = new ATaskNothing() };
        }

        private ATaskNothing m_Task;
        public ATaskNothing Task => m_Task;

        public void SetResult()
        {
        }

        public void Start<TStateMachine>(ref TStateMachine stateMachine) where TStateMachine : IAsyncStateMachine
        {
            //Builder用于保存Task及状态机的状态切换
            //编译器代码编译完后，异步函数包装的异步程序第一次执行
            stateMachine.MoveNext();
        }

        public void AwaitOnCompleted<TAwaiter, TStateMachine>(ref TAwaiter awaiter, ref TStateMachine stateMachine)
            where TAwaiter : INotifyCompletion where TStateMachine : IAsyncStateMachine
        {
            //配置完成后的延续,完成后继续执行状态机
            awaiter.OnCompleted(stateMachine.MoveNext);
        }

        public void AwaitUnsafeOnCompleted<TAwaiter, TStateMachine>(ref TAwaiter awaiter, ref TStateMachine stateMachine)
            where TAwaiter :ICriticalNotifyCompletion where TStateMachine : IAsyncStateMachine
        {
            //配置完成后的延续
            awaiter.UnsafeOnCompleted(stateMachine.MoveNext);
        }

        public void SetStateMachine(IAsyncStateMachine stateMachine)
        {
        }

        public void SetException(Exception exception)
        {
            if (exception == null) return;
            Log.Exception($"ATaskAsyncNothingMethodBuilder SetException, Message:{exception.Message}\n StackTrace:\n{exception.StackTrace}\n");
            //Console.WriteLine(exception.Message);
            Task.SetException(exception);
        }
    }
}

