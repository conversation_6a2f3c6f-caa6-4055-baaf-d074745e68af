﻿
using Aurora.Framework;
using BattleServer.Server;
using System.Collections.Concurrent;

namespace BattleServer.Server
{
    public class WorkUnitManager : Singleton<WorkUnitManager>, IModule
    {
        private volatile uint m_nLastID = 0;
        private object m_IDLock = new object();
        private ConcurrentDictionary<uint, WorkUnitBase> m_WorkUnitList = new ConcurrentDictionary<uint, WorkUnitBase>();

        public void CreateWorkUnit(IWorkUnitOwner owner)
        {
            if (owner != null)
            {
                uint nID = GenerateID();
                WorkUnitBase workUnit = ReferencePool.Acquire<WorkUnitBase>();
                if (workUnit != null)
                {
                    workUnit.Init(nID, owner);
                    if (!m_WorkUnitList.TryAdd(nID, workUnit))
                    {
                        Log.Error($"Can Not Register WorkUnit!");
                    }
                }
                else
                {
                    Log.Error($"Can Not Create WorkUnit!");
                }
            }
            else
            {
                Log.Error($"Can Not Create WorkUnit Without Owner!");
            }
        }

        public void DestroyWorkUnit(IWorkUnitOwner owner)
        {
            if (owner != null)
            {
                if (owner.WorkUnit != null)
                {
                    WorkUnitBase workUnit = null;
                    if (m_WorkUnitList.TryRemove(owner.WorkUnit.ID, out workUnit))
                    {
                        ReferencePool.Release(workUnit);
                    }
                    else
                    {
                        Log.Error($"Can Not Unregister WorkUnit!");
                    }
                }
            }
        }

        public WorkUnitBase GetWorkUnit(uint nID)
        {
            WorkUnitBase workUnit = null;
            if (m_WorkUnitList.TryGetValue(nID, out workUnit))
            {
                return workUnit;
            }
            else
            {
                Log.Error($"Can Not Find WorkUnit!");
                return null;
            }
        }

        private uint GenerateID()
        {
            lock (m_IDLock)
            {
                return ++m_nLastID;
            }
        }

        public bool Init(string configFilePath)
        {
            return true;
        }

        public bool Release()
        {
            return true;
        }
    }
}
