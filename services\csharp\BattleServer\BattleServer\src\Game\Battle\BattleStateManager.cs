//using System;
//using Aurora.Framework;
//using Game.Core;
//using BattleServer.Game.Core;

//namespace BattleServer.Game.Battle
//{
//    /// <summary>
//    /// 战斗状态管理器 - 负责管理战斗状态转换、倒计时和状态事件
//    /// </summary>
//    public class BattleStateManager : BattleComponentBase, IUpdatableBattleComponent
//    {
//        // 当前状态
//        private BattleState _currentState = BattleState.StateNone;
//        public BattleState CurrentState => _currentState;

//        // 状态倒计时(毫秒)
//        private int _stateCountdown;
//        public int StateCountdown => _stateCountdown;

//        // 回合计数
//        private int _roundCount;
//        public int RoundCount => _roundCount;

//        // 当前回合是否有Buff选择环节
//        private bool _hasBuffSelectionThisRound;
//        public bool HasBuffSelectionThisRound => _hasBuffSelectionThisRound;

//        // 战斗是否已进入加速状态
//        private bool _battleAccelerating;
//        public bool IsBattleAccelerating => _battleAccelerating;

//        // 战斗加速阶段时间（毫秒）
//        public int BattleAcceleratingTimeMs => BattleConfig.Timing.SpeedUpTime;

//        // Buff选择独立计时器
//        private int _buffSelectionCountdown;
//        public int BuffSelectionCountdown => _buffSelectionCountdown;
//        private bool _buffSelectionTimeoutTriggered;
//        public bool IsBuffSelectionTimeoutTriggered => _buffSelectionTimeoutTriggered;

//        // 事件总线
//        private BattleEventBus _eventBus;

//        /// <summary>
//        /// 构造函数
//        /// </summary>
//        /// <param name="eventBus">事件总线</param>
//        public BattleStateManager(BattleEventBus? eventBus = null)
//        {
//            _eventBus = eventBus ?? new BattleEventBus();
//        }

//        protected override void OnInitialize()
//        {
//            _roundCount = 0;
//            _battleAccelerating = false;
//            _currentState = BattleState.StateNone;

//            Log.Info($"[{LogName}] Initialized");
//        }

//        /// <summary>
//        /// 开始新回合
//        /// </summary>
//        public void StartNewRound()
//        {
//            ThrowIfNotInitialized();

//            _roundCount++;
//            Log.Info($"[{LogName}] ===== STARTING NEW ROUND {_roundCount} =====");

//            // 判断本回合是否有Buff选择环节
//            _hasBuffSelectionThisRound = BattleConfig.Buff.HasBuffSelection(_roundCount);
//            Log.Info($"[{LogName}] Round {_roundCount} has buff selection: {_hasBuffSelectionThisRound}");

//            // 重置buff选择相关状态
//            _buffSelectionTimeoutTriggered = false;
//            _buffSelectionCountdown = 0;

//            // 发布回合开始事件
//            Log.Info($"[{LogName}] Publishing RoundStartedEvent for round {_roundCount}");
//            _eventBus.Publish(new RoundStartedEvent(BattleId, _roundCount));

//            // 设置为回合开始状态
//            Log.Info($"[{LogName}] Setting state to StateRoundStart for round {_roundCount}");
//            SetState(BattleState.StateRoundStart);

//            Log.Info($"[{LogName}] ===== ROUND {_roundCount} INITIALIZATION COMPLETE =====");
//        }

//        /// <summary>
//        /// 设置战斗状态
//        /// </summary>
//        /// <param name="newState">新状态</param>
//        public void SetState(BattleState newState)
//        {
//            ThrowIfNotInitialized();

//            BattleState oldState = _currentState;

//            // 检查状态转换是否有效
//            if (!IsValidStateTransition(oldState, newState))
//            {
//                Log.Warning($"[{LogName}] Invalid state transition from {oldState} to {newState}");
//                return;
//            }

//            // 保存状态
//            _currentState = newState;

//            // 设置超时时间
//            SetStateCountdown(newState);

//            // 简化状态转换日志
//            Log.Info($"[{LogName}] State: {oldState} -> {newState} (R{_roundCount}, {_stateCountdown}ms)");

//            // 发布状态变更事件
//            _eventBus.Publish(new BattleStateChangedEvent(BattleId, oldState, newState, _stateCountdown));
//            Log.Info($"[{LogName}] BattleStateChangedEvent published successfully");
//        }

//        /// <summary>
//        /// 设置状态倒计时
//        /// </summary>
//        /// <param name="state">战斗状态</param>
//        private void SetStateCountdown(BattleState state)
//        {
//            _stateCountdown = state switch
//            {
//                BattleState.StateRoundStart => 1000, // 1秒 - 瞬间匹配对手，生成棋盘
//                BattleState.StatePreparation => BattleConfig.Timing.RoundPrepareTime, // 65秒 - 主要准备阶段（选择Buff、操作英雄、准备）
//                BattleState.StateBattleStarting => BattleConfig.Timing.BattleStartingTime,
//                BattleState.StateBattleInProgress => BattleConfig.Timing.BattleTime,
//                BattleState.StateRoundSettlement => BattleConfig.Timing.RoundSettlementTime, // 3秒 - 回合结算展示时间（包含淘汰检查）
//                BattleState.StateGameOver => 0,
//                _ => 0
//            };

//            // 如果进入准备阶段且有buff选择，启动buff选择独立计时器
//            if (state == BattleState.StatePreparation && _hasBuffSelectionThisRound)
//            {
//                _buffSelectionCountdown = BattleConfig.Timing.BuffSelectionTime;
//                _buffSelectionTimeoutTriggered = false;
//                Log.Info($"[{LogName}] Buff selection timer started: {_buffSelectionCountdown}ms");
//            }

//            // 战斗加速逻辑现在在StateBattleInProgress状态内部处理
//        }

//        private int CalculateFreeOperationTime()
//        {
//            // 自由操作时间 = 准备时间 - Buff选择时间（如果有的话）
//            // 英雄生成是瞬间完成的，不占用时间
//            return _hasBuffSelectionThisRound
//                ? BattleConfig.Timing.RoundPrepareTime - BattleConfig.Timing.BuffSelectionTime
//                : BattleConfig.Timing.RoundPrepareTime;
//        }

//        /// <summary>
//        /// 每帧更新
//        /// </summary>
//        /// <param name="deltaTimeMs">时间增量(毫秒)</param>
//        public void Update(int deltaTimeMs)
//        {
//            ThrowIfNotInitialized();

//            if (_stateCountdown <= 0) return;

//            _stateCountdown -= deltaTimeMs;

//            // 更新buff选择计时器（如果在准备阶段且有buff选择）
//            if (_currentState == BattleState.StatePreparation && _hasBuffSelectionThisRound && _buffSelectionCountdown > 0)
//            {
//                _buffSelectionCountdown -= deltaTimeMs;

//                // 检查buff选择是否超时
//                if (_buffSelectionCountdown <= 0 && !_buffSelectionTimeoutTriggered)
//                {
//                    _buffSelectionTimeoutTriggered = true;
//                    // 发布buff选择超时事件
//                    _eventBus.Publish(new BuffSelectionTimeoutEvent(BattleId));
//                    Log.Info($"[{LogName}] Buff selection timeout triggered");
//                }
//            }

//            // 检查是否需要转为加速状态
//            if (_currentState == BattleState.StateBattleInProgress && !_battleAccelerating)
//            {
//                var elapsedTime = BattleConfig.Timing.BattleTime - _stateCountdown;
//                if (elapsedTime >= BattleConfig.Timing.SpeedUpTime * 1000)
//                {
//                    _battleAccelerating = true;
//                    // 发布战斗加速事件，客户端收到后开始加速
//                    _eventBus.Publish(new BattleAcceleratingEvent(BattleId));
//                    Log.Info($"[{LogName}] Battle acceleration started at {elapsedTime}ms elapsed");
//                }
//            }

//            // 检查倒计时是否结束
//            if (_stateCountdown <= 0)
//            {
//                OnCountdownFinished();
//            }
//        }

//        /// <summary>
//        /// 每帧更新 - 兼容旧接口
//        /// </summary>
//        /// <param name="deltaTimeSeconds">时间增量(秒)</param>
//        public void Tick(float deltaTimeSeconds)
//        {
//            Update((int)(deltaTimeSeconds * 1000));
//        }

//        /// <summary>
//        /// 倒计时结束处理
//        /// </summary>
//        private void OnCountdownFinished()
//        {
//            // 发布超时事件（在状态转换前发布，让处理器有机会处理超时逻辑）
//            if (_currentState == BattleState.StatePreparation ||
//                _currentState == BattleState.StateBattleInProgress ||
//                _currentState == BattleState.StateRoundSettlement)
//            {
//                _eventBus.Publish(new BattleTimeoutEvent(BattleId, _currentState));
//                Log.Info($"[{LogName}] Published BattleTimeoutEvent for state {_currentState}");
//            }

//            // 根据当前状态决定下一个状态
//            switch (_currentState)
//            {
//                case BattleState.StateRoundStart:
//                    SetState(BattleState.StatePreparation);
//                    break;

//                case BattleState.StatePreparation:
//                    // 准备阶段超时，由OnBattleTimeout处理强制准备逻辑
//                    SetState(BattleState.StateBattleStarting);
//                    break;

//                case BattleState.StateBattleStarting:
//                    SetState(BattleState.StateBattleInProgress);
//                    break;

//                case BattleState.StateBattleInProgress:
//                    // 战斗时间结束，由OnBattleTimeout处理强制结算逻辑
//                    Log.Info($"[{LogName}] Battle timeout, proceeding to settlement");
//                    SetState(BattleState.StateRoundSettlement);
//                    break;

//                case BattleState.StateRoundSettlement:
//                    // 结算确认超时，由OnBattleTimeout处理强制确认逻辑
//                    // 实际的状态转换由AutoChessScene根据游戏是否结束来决定
//                    Log.Info($"[{LogName}] Round settlement timeout, waiting for AutoChessScene to handle next state");
//                    break;
//            }
//        }

//        /// <summary>
//        /// 检查状态转换是否有效
//        /// </summary>
//        private bool IsValidStateTransition(BattleState fromState, BattleState toState)
//        {
//            // 初始状态可以转为任何状态
//            if (fromState == BattleState.StateNone)
//            {
//                return true;
//            }

//            // 定义合法的状态转换
//            switch (fromState)
//            {
//                case BattleState.StateRoundStart:
//                    return toState == BattleState.StatePreparation;

//                case BattleState.StatePreparation:
//                    return toState == BattleState.StateBattleStarting;

//                case BattleState.StateBattleStarting:
//                    return toState == BattleState.StateBattleInProgress;

//                case BattleState.StateBattleInProgress:
//                    return toState == BattleState.StateRoundSettlement;

//                case BattleState.StateRoundSettlement:
//                    return toState == BattleState.StateRoundStart || toState == BattleState.StateGameOver;

//                case BattleState.StateGameOver:
//                    return false; // GameOver是终态，不能再转换

//                default:
//                    return false;
//            }
//        }
        
//        /// <summary>
//        /// 设置所有玩家已准备状态（提前开始战斗）
//        /// </summary>
//        public void SetAllPlayersReady()
//        {
//            // 仅在准备阶段可以设置为准备就绪
//            if (_currentState == BattleState.StatePreparation)
//            {
//                SetState(BattleState.StateBattleStarting);
//            }
//        }
        
//        /// <summary>
//        /// 检查是否需要进入战斗加速阶段
//        /// </summary>
//        public void CheckBattleAcceleration()
//        {
//            // 战斗加速现在在StateBattleInProgress状态内部处理，不需要单独的状态
//            if (_currentState == BattleState.StateBattleInProgress && _stateCountdown <= BattleAcceleratingTimeMs)
//            {
//                // 可以在这里触发加速事件，但不改变状态
//                Log.Info($"[{LogName}] Battle acceleration triggered");
//            }
//        }
        
//        /// <summary>
//        /// 结束当前回合战斗
//        /// </summary>
//        public void EndBattle()
//        {
//            if (_currentState == BattleState.StateBattleInProgress)
//            {
//                SetState(BattleState.StateRoundSettlement);
//            }
//        }

//        protected override void OnClear()
//        {
//            _eventBus?.Clear();
//            _currentState = BattleState.StateNone;
//            _stateCountdown = 0;
//            _roundCount = 0;
//            _battleAccelerating = false;
//        }

//        /// <summary>
//        /// 订阅事件
//        /// </summary>
//        public void Subscribe<T>(Action<T> handler) where T : BattleEvent
//        {
//            _eventBus.Subscribe(handler);
//        }

//        /// <summary>
//        /// 取消订阅事件
//        /// </summary>
//        public void Unsubscribe<T>(IBattleEventHandler<T> handler) where T : BattleEvent
//        {
//            _eventBus.Unsubscribe(handler);
//        }
//    }
//}
