﻿//*********************************************************
// Framework
// Author:  Jasen
// Date  :  2022-11-24
//*********************************************************

using System;
using System.Collections.Generic;
using System.Reflection;

namespace Aurora.Framework
{
    public class MessageIDComponent:BaseComponent,IAwake
    {
        public static MessageIDComponent Instance;
        //消息ID映射到类型，运行时方便使用，避开反射
        public readonly Dictionary<ushort, Type> MsgID2Type = new Dictionary<ushort, Type>();
        //消息类型映射到ID
        public readonly Dictionary<Type,ushort> MsgType2ID = new Dictionary<Type, ushort>();
        //消息请求对应的返回类型，运行时使用，避开反射
        public readonly Dictionary<Type, Type> Request2Response = new Dictionary<Type, Type>();
        //消息Type映射到消息类型
        public readonly Dictionary<Type, MessageClass> MsgType2ClassID = new Dictionary<Type, MessageClass>();
        public readonly Dictionary<Type, MessageTarget> MsgType2Target = new Dictionary<Type, MessageTarget>();
    }

    [ComponentSystem(typeof(MessageIDComponent))]
    public static class MessageIDComponentSystem
    {
        [MethodAwake]
        public static void OnAwake(MessageIDComponent self)
        {
            if (self == null) return;
            //初始化消息ID、类型相关的信息，运行时使用
            MessageIDComponent.Instance = self;

            if (EntitySystem.OperatingEnvironment == EntitySystem.OperatingEnvironmentType.Server)
            {
                InitServerComponent(self);
            }
            else
            {
                InitClientComponent(self);
            }
        }

        private static void InitServerComponent(MessageIDComponent self)
        {
            List<Type> allTypes = AssemblyManager.Instance.GetAllTypes();
            foreach (Type msgType in allTypes)
            {
                var attrs = msgType.GetCustomAttribute<MessageIDAttribute>();
                if (attrs != null)
                {
                    ushort msgid = attrs.MessageID;
                    self.MsgID2Type.Add(msgid, msgType);
                    self.MsgType2ID.Add(msgType, msgid);
                    
#if PRINT_PACKET
                    Log.Info($"MsgID2Type {msgid}#{msgType} ");
#endif
                }

                var classAttrs = msgType.GetCustomAttribute<MessageTypeAttribute>();
                if (classAttrs != null)
                {
                    MessageClass messageClass = classAttrs.ClassType;
                    self.MsgType2ClassID.Add(msgType, messageClass);
                }

                var responseAttr = msgType.GetCustomAttribute<ResponseTypeAttribute>();
                if (responseAttr != null)
                {
                    if (typeof(IRequest).IsAssignableFrom(msgType))
                    {
                        Type responseType = responseAttr.MsgType;
                        if (typeof(IResponse).IsAssignableFrom(responseType))
                        {
                            self.Request2Response.Add(msgType, responseType);
                        }
                        else
                        {
                            Log.Error($"Can Only Attach Response Message To Request Message! Message Type Is {msgType.Name}.");
                        }
                    }
                    else
                    {
                        Log.Error($"Can Only Attach Response Message To Request Message! Message Type Is {msgType.Name}.");
                    }
                }
                else
                {
                    if (!msgType.IsInterface && typeof(IRequest).IsAssignableFrom(msgType))
                    {
                        //Log.Error($"Request Message Must Be Attached by Some Response Message! Message Type Is {msgType.Name}.");
                    }
                }

                var targetAttr = msgType.GetCustomAttribute<TargetAttribute>();
                if (targetAttr != null)
                {
                    MessageTarget target = targetAttr.Target;
                    self.MsgType2Target.Add(msgType, target);
                }
            }
        }
        private static void InitClientComponent(MessageIDComponent self)
        {
            self.MsgID2Type.Clear();
            self.MsgType2ID.Clear();
            self.Request2Response.Clear();
            self.MsgType2ClassID.Clear();
            self.MsgType2Target.Clear();
            List<Type> allTypes = AssemblyManager.Instance.GetAllTypes<MessageIDAttribute>();
            foreach (Type msgType in allTypes)
            {
                var attrs = msgType.GetCustomAttribute<MessageIDAttribute>();
                if (attrs != null)
                {
                    ushort msgid = attrs.MessageID;
                    self.MsgID2Type.Add(msgid, msgType);
                    self.MsgType2ID.Add(msgType, msgid);
                }
            }
            allTypes = AssemblyManager.Instance.GetAllTypes<MessageTypeAttribute>();
            foreach (Type msgType in allTypes)
            {
                var classAttrs = msgType.GetCustomAttribute<MessageTypeAttribute>();
                if (classAttrs != null)
                {
                    MessageClass messageClass = classAttrs.ClassType;
                    self.MsgType2ClassID.Add(msgType, messageClass);
                }
            }
            allTypes = AssemblyManager.Instance.GetAllTypes<ResponseTypeAttribute>();
            foreach (Type msgType in allTypes)
            {
                var responseAttr = msgType.GetCustomAttribute<ResponseTypeAttribute>();
                if (responseAttr != null)
                {
                    Type responseType = responseAttr.MsgType;
                    self.Request2Response.Add(msgType, responseType);
                }
            }
            allTypes = AssemblyManager.Instance.GetAllTypes<TargetAttribute>();
            foreach (Type msgType in allTypes)
            {
                var targetAttr = msgType.GetCustomAttribute<TargetAttribute>();
                if (targetAttr != null)
                {
                    MessageTarget target = targetAttr.Target;
                    self.MsgType2Target.Add(msgType, target);
                }
            }
        }
        public static ushort GetMsgID(this MessageIDComponent self, Type msgTpe)
        {
            if (self == null) return 0;
            return self.MsgType2ID[msgTpe];
        }

        public static Type GetMsgType(this MessageIDComponent self, ushort msgID)
        {
            if (self == null) return null;
            return self.MsgID2Type[msgID];
        }
        public static Type GetResponseType(this MessageIDComponent self, Type request)
        {
            if (self == null || request == null) return null;
            if (!self.Request2Response.TryGetValue(request,out Type responseType))
            {
                throw new FrameworkException($"Get request's response type is null,request name:{request.Name}");
            }
            return responseType;
        }

        public static MessageClass GetMsgClass(this MessageIDComponent self, Type request)
        {
            if (self == null || request == null) return MessageClass.None;
            if (self.MsgType2ClassID.TryGetValue(request,out MessageClass classType))
                return classType;

            return MessageClass.None;
        }
        public static MessageTarget GetMsgTarget(this MessageIDComponent self, Type msgTpe)
        {
            if (self == null) return MessageTarget.None;
            if (self.MsgType2Target.TryGetValue(msgTpe, out MessageTarget target))
                return target;

            return MessageTarget.None;
        }
        public static IResponse CreateResponse(this MessageIDComponent self,Packet pkt,int error = 0)
        {
            if (self == null || pkt == null) return null;
            IRequest request = pkt.GetMessage() as IRequest;
            return self.CreateResponse(request, error);
        }

        public static IResponse CreateResponse(this MessageIDComponent self,IRequest request,int error = 0)
        {
            if (self == null || request == null) return null;
            Type responseType = self.GetResponseType(request.GetType());
            IResponse response = (IResponse)Activator.CreateInstance(responseType);
            if (error != 0)
            {
                response.Error = error;
            }
            return response;
        }
        public static bool IsOuterMessage(this MessageIDComponent self, ushort msgID)
        {
            if(msgID >= MessageRangeDefine.PbOuterMin && msgID <= MessageRangeDefine.PbOuterMax)
            {
                return true;
            }
            
            if(msgID >= MessageRangeDefine.KratosOuterMin && msgID <= MessageRangeDefine.KratosOuterMax)
            {
                return true;
            }
            
            return false;
        }
    }
}
