﻿//*********************************************************
// Framework
// Author:  Jasen
// Date  :  2022-11-15
//*********************************************************

using System;
using System.Collections.Concurrent;
using System.Threading;

namespace Aurora.Framework
{
    public class ThreadSyncContext//:SynchronizationContext
    {
        private readonly ConcurrentQueue<Action> m_Queue = new ConcurrentQueue<Action>();
        private Action m_TempA;
        public ThreadSyncContext()
        {
        }

        public void Update()
        {
            while (true)
            {
                if (!m_Queue.TryDequeue(out m_TempA))
                {
                    return;
                }

                try
                {
                    m_TempA();
                    m_TempA = null;
                }
                catch (Exception e)
                {
                    Log.Error($"Exception:{e.Message}\nStackTrace:\n{e.StackTrace}");
                }
            }
        }

        public void Post(Action action)
        {
            m_Queue.Enqueue(action);
        }

        public void Clear()
        {
            m_Queue.Clear();
        }
    }
}
